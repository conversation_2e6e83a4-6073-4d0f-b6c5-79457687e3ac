/**
 * @file error_handler.h
 * @brief Enhanced Error Handling and Recovery System Header
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef ERROR_HANDLER_H
#define ERROR_HANDLER_H

#include "esp_err.h"
#include "system_monitor.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Error handler configuration */
#define ERROR_QUEUE_SIZE                20
#define MAX_MODULE_NAME_LEN             32
#define MAX_ERROR_DESCRIPTION_LEN       128

/* Error severity levels */
typedef enum {
    ERROR_SEVERITY_INFO = 0,
    ERROR_SEVERITY_WARNING,
    ERROR_SEVERITY_ERROR,
    ERROR_SEVERITY_CRITICAL,
    ERROR_SEVERITY_MAX
} error_severity_t;

/* Recovery actions */
typedef enum {
    RECOVERY_ACTION_NONE = 0,
    RECOVERY_ACTION_RETRY,
    RECOVERY_ACTION_RESTART_TASK,
    RECOVERY_ACTION_REINIT_MODULE,
    RECOVERY_ACTION_SYSTEM_RESET,
    RECOVERY_ACTION_MAX
} recovery_action_t;

/* Error event structure */
typedef struct {
    error_type_t type;                          // Error type
    error_severity_t severity;                  // Error severity
    uint32_t timestamp;                         // Error timestamp
    uint8_t retry_count;                        // Number of retries
    char module[MAX_MODULE_NAME_LEN];           // Module name
    char description[MAX_ERROR_DESCRIPTION_LEN]; // Error description
} error_event_t;

/* Recovery strategy */
typedef struct {
    uint8_t max_retries;                        // Maximum retry attempts
    uint32_t retry_delay_ms;                    // Delay between retries
    recovery_action_t escalation_action;        // Action when retries exhausted
    uint32_t critical_threshold;                // Critical error count threshold
} recovery_strategy_t;

/* Error handler state */
typedef struct {
    uint32_t total_errors;                      // Total error count
    uint32_t errors_by_type[ERROR_TYPE_MAX];    // Errors by type
    uint32_t errors_by_severity[ERROR_SEVERITY_MAX]; // Errors by severity
    uint32_t recovery_actions_taken;            // Recovery actions executed
    uint32_t health_checks_performed;           // Health checks performed
    uint32_t start_time;                        // Handler start time
} error_handler_state_t;

/* Error handler statistics */
typedef struct {
    uint32_t total_errors;                      // Total error count
    uint32_t recovery_actions_taken;            // Recovery actions taken
    uint32_t health_checks_performed;           // Health checks performed
    uint32_t uptime_ms;                         // Handler uptime
    uint32_t errors_by_type[ERROR_TYPE_MAX];    // Errors by type
    uint32_t errors_by_severity[ERROR_SEVERITY_MAX]; // Errors by severity
} error_handler_stats_t;

/**
 * @brief Initialize error handler
 * @return esp_err_t
 */
esp_err_t error_handler_init(void);

/**
 * @brief Start error handler task
 * @return esp_err_t
 */
esp_err_t error_handler_start(void);

/**
 * @brief Stop error handler
 * @return esp_err_t
 */
esp_err_t error_handler_stop(void);

/**
 * @brief Error handler task
 * @param pvParameters Task parameters
 */
void error_handler_task(void *pvParameters);

/**
 * @brief Report an error
 * @param type Error type
 * @param severity Error severity
 * @param module Module name (optional)
 * @param description Error description (optional)
 * @return esp_err_t
 */
esp_err_t error_handler_report_error(error_type_t type, error_severity_t severity, 
                                    const char *module, const char *description);

/**
 * @brief Get error handler statistics
 * @param stats Output statistics structure
 * @return esp_err_t
 */
esp_err_t error_handler_get_stats(error_handler_stats_t *stats);

/**
 * @brief Reset error statistics
 * @return esp_err_t
 */
esp_err_t error_handler_reset_stats(void);

/**
 * @brief Set recovery strategy for error type
 * @param type Error type
 * @param strategy Recovery strategy
 * @return esp_err_t
 */
esp_err_t error_handler_set_recovery_strategy(error_type_t type, const recovery_strategy_t *strategy);

/**
 * @brief Check if error handler is running
 * @return bool
 */
bool error_handler_is_running(void);

/* Convenience macros for error reporting */
#define ERROR_REPORT_INFO(module, desc) \
    error_handler_report_error(ERROR_TYPE_SOFTWARE, ERROR_SEVERITY_INFO, module, desc)

#define ERROR_REPORT_WARNING(module, desc) \
    error_handler_report_error(ERROR_TYPE_SOFTWARE, ERROR_SEVERITY_WARNING, module, desc)

#define ERROR_REPORT_ERROR(module, desc) \
    error_handler_report_error(ERROR_TYPE_SOFTWARE, ERROR_SEVERITY_ERROR, module, desc)

#define ERROR_REPORT_CRITICAL(module, desc) \
    error_handler_report_error(ERROR_TYPE_SOFTWARE, ERROR_SEVERITY_CRITICAL, module, desc)

#define ERROR_REPORT_MEMORY(severity, desc) \
    error_handler_report_error(ERROR_TYPE_MEMORY, severity, "memory", desc)

#define ERROR_REPORT_COMM(severity, desc) \
    error_handler_report_error(ERROR_TYPE_COMMUNICATION, severity, "communication", desc)

#define ERROR_REPORT_HARDWARE(severity, desc) \
    error_handler_report_error(ERROR_TYPE_HARDWARE, severity, "hardware", desc)

#ifdef __cplusplus
}
#endif

#endif /* ERROR_HANDLER_H */
