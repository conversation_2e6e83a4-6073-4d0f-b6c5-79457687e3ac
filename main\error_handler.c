/**
 * @file error_handler.c
 * @brief Enhanced Error Handling and Recovery System
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "error_handler.h"
#include "base_config.h"
#include "system_monitor.h"
#include "esp_log.h"
#include "esp_system.h"
#include "esp_timer.h"
#include "esp_task_wdt.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

static const char *TAG = "ERROR_HANDLER";

/* Error handling state */
static error_handler_state_t g_error_state = {0};
static TaskHandle_t g_error_task_handle = NULL;
static QueueHandle_t g_error_queue = NULL;
static bool g_error_handler_running = false;

/* Recovery strategies */
static recovery_strategy_t g_recovery_strategies[ERROR_TYPE_MAX] = {
    [ERROR_TYPE_MEMORY] = {
        .max_retries = 3,
        .retry_delay_ms = 1000,
        .escalation_action = RECOVERY_ACTION_RESTART_TASK,
        .critical_threshold = 5
    },
    [ERROR_TYPE_COMMUNICATION] = {
        .max_retries = 5,
        .retry_delay_ms = 2000,
        .escalation_action = RECOVERY_ACTION_REINIT_MODULE,
        .critical_threshold = 10
    },
    [ERROR_TYPE_HARDWARE] = {
        .max_retries = 2,
        .retry_delay_ms = 5000,
        .escalation_action = RECOVERY_ACTION_SYSTEM_RESET,
        .critical_threshold = 3
    },
    [ERROR_TYPE_SOFTWARE] = {
        .max_retries = 3,
        .retry_delay_ms = 1000,
        .escalation_action = RECOVERY_ACTION_RESTART_TASK,
        .critical_threshold = 8
    }
};

/**
 * @brief Initialize error handler
 */
esp_err_t error_handler_init(void)
{
    ESP_LOGI(TAG, "Initializing error handler...");
    
    // Create error queue
    g_error_queue = xQueueCreate(ERROR_QUEUE_SIZE, sizeof(error_event_t));
    if (g_error_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create error queue");
        return ESP_ERR_NO_MEM;
    }
    
    // Initialize state
    memset(&g_error_state, 0, sizeof(g_error_state));
    g_error_state.start_time = esp_timer_get_time() / 1000;
    
    ESP_LOGI(TAG, "Error handler initialized");
    return ESP_OK;
}

/**
 * @brief Start error handler task
 */
esp_err_t error_handler_start(void)
{
    if (g_error_handler_running) {
        ESP_LOGW(TAG, "Error handler already running");
        return ESP_OK;
    }
    
    BaseType_t ret = xTaskCreate(
        error_handler_task,
        "error_handler",
        TASK_STACK_SIZE_MEDIUM,
        NULL,
        TASK_PRIORITY_HIGH,
        &g_error_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create error handler task");
        return ESP_ERR_NO_MEM;
    }
    
    g_error_handler_running = true;
    ESP_LOGI(TAG, "Error handler started");
    return ESP_OK;
}

/**
 * @brief Stop error handler
 */
esp_err_t error_handler_stop(void)
{
    if (!g_error_handler_running) {
        return ESP_OK;
    }
    
    g_error_handler_running = false;
    
    if (g_error_task_handle) {
        vTaskDelete(g_error_task_handle);
        g_error_task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "Error handler stopped");
    return ESP_OK;
}

/**
 * @brief Report an error
 */
esp_err_t error_handler_report_error(error_type_t type, error_severity_t severity, 
                                    const char *module, const char *description)
{
    if (g_error_queue == NULL) {
        ESP_LOGE(TAG, "Error handler not initialized");
        return ESP_ERR_INVALID_STATE;
    }
    
    error_event_t event;
    event.type = type;
    event.severity = severity;
    event.timestamp = esp_timer_get_time() / 1000;
    event.retry_count = 0;
    
    strncpy(event.module, module ? module : "unknown", sizeof(event.module) - 1);
    event.module[sizeof(event.module) - 1] = '\0';
    
    strncpy(event.description, description ? description : "no description", 
            sizeof(event.description) - 1);
    event.description[sizeof(event.description) - 1] = '\0';
    
    if (xQueueSend(g_error_queue, &event, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to queue error event");
        return ESP_ERR_TIMEOUT;
    }
    
    // Update statistics
    g_error_state.total_errors++;
    g_error_state.errors_by_type[type]++;
    g_error_state.errors_by_severity[severity]++;
    
    // Log error immediately
    const char* severity_str[] = {"INFO", "WARNING", "ERROR", "CRITICAL"};
    ESP_LOGE(TAG, "[%s] %s: %s - %s", severity_str[severity], module, description, 
             (severity >= ERROR_SEVERITY_CRITICAL) ? "CRITICAL ERROR!" : "");
    
    return ESP_OK;
}

/**
 * @brief Execute recovery action
 */
static esp_err_t execute_recovery_action(recovery_action_t action, const error_event_t *event)
{
    ESP_LOGI(TAG, "Executing recovery action: %d for error in %s", action, event->module);
    
    switch (action) {
    case RECOVERY_ACTION_NONE:
        ESP_LOGI(TAG, "No recovery action required");
        break;
        
    case RECOVERY_ACTION_RETRY:
        ESP_LOGI(TAG, "Retry operation (attempt %d)", event->retry_count + 1);
        // The calling module should handle the retry
        break;
        
    case RECOVERY_ACTION_RESTART_TASK:
        ESP_LOGW(TAG, "Restarting task for module: %s", event->module);
        // This would require module-specific restart functions
        // For now, just log the action
        break;
        
    case RECOVERY_ACTION_REINIT_MODULE:
        ESP_LOGW(TAG, "Reinitializing module: %s", event->module);
        // This would require module-specific reinitialization
        // For now, just log the action
        break;
        
    case RECOVERY_ACTION_SYSTEM_RESET:
        ESP_LOGE(TAG, "System reset required due to critical error in %s", event->module);
        // Give some time for logging
        vTaskDelay(pdMS_TO_TICKS(1000));
        esp_restart();
        break;
        
    default:
        ESP_LOGE(TAG, "Unknown recovery action: %d", action);
        return ESP_ERR_INVALID_ARG;
    }
    
    g_error_state.recovery_actions_taken++;
    return ESP_OK;
}

/**
 * @brief Process error event
 */
static void process_error_event(error_event_t *event)
{
    recovery_strategy_t *strategy = &g_recovery_strategies[event->type];
    
    // Check if this is a critical error
    if (event->severity >= ERROR_SEVERITY_CRITICAL) {
        ESP_LOGE(TAG, "Critical error detected in %s: %s", event->module, event->description);
        execute_recovery_action(RECOVERY_ACTION_SYSTEM_RESET, event);
        return;
    }
    
    // Check retry count
    if (event->retry_count < strategy->max_retries) {
        event->retry_count++;
        
        // Delay before retry
        if (strategy->retry_delay_ms > 0) {
            vTaskDelay(pdMS_TO_TICKS(strategy->retry_delay_ms));
        }
        
        execute_recovery_action(RECOVERY_ACTION_RETRY, event);
    } else {
        // Max retries exceeded, escalate
        ESP_LOGW(TAG, "Max retries exceeded for %s, escalating to action: %d", 
                 event->module, strategy->escalation_action);
        execute_recovery_action(strategy->escalation_action, event);
    }
    
    // Check if error count for this type exceeds critical threshold
    if (g_error_state.errors_by_type[event->type] >= strategy->critical_threshold) {
        ESP_LOGE(TAG, "Critical threshold exceeded for error type %d, forcing system reset", 
                 event->type);
        execute_recovery_action(RECOVERY_ACTION_SYSTEM_RESET, event);
    }
}

/**
 * @brief Perform system health check
 */
static void perform_health_check(void)
{
    static uint32_t last_health_check = 0;
    uint32_t current_time = esp_timer_get_time() / 1000;
    
    // Perform health check every 30 seconds
    if (current_time - last_health_check < 30000) {
        return;
    }
    
    last_health_check = current_time;
    
    // Check memory usage
    size_t free_heap = esp_get_free_heap_size();
    size_t min_free_heap = esp_get_minimum_free_heap_size();
    
    if (free_heap < (min_free_heap * 1.2)) {
        error_handler_report_error(ERROR_TYPE_MEMORY, ERROR_SEVERITY_WARNING,
                                 "health_check", "Low memory detected");
    }
    
    // Check task count
    UBaseType_t task_count = uxTaskGetNumberOfTasks();
    if (task_count > 15) {
        error_handler_report_error(ERROR_TYPE_SOFTWARE, ERROR_SEVERITY_WARNING,
                                 "health_check", "High task count detected");
    }
    
    // Update health check statistics
    g_error_state.health_checks_performed++;
    
    ESP_LOGI(TAG, "Health check completed - Free heap: %d, Tasks: %d", 
             free_heap, task_count);
}

/**
 * @brief Error handler task
 */
void error_handler_task(void *pvParameters)
{
    ESP_LOGI(TAG, "Error handler task started");
    
    error_event_t event;
    
    while (g_error_handler_running) {
        // Wait for error events
        if (xQueueReceive(g_error_queue, &event, pdMS_TO_TICKS(5000)) == pdTRUE) {
            process_error_event(&event);
        }
        
        // Perform periodic health checks
        perform_health_check();
        
        // Feed watchdog
        esp_task_wdt_reset();
    }
    
    ESP_LOGI(TAG, "Error handler task stopped");
    vTaskDelete(NULL);
}

/**
 * @brief Get error handler statistics
 */
esp_err_t error_handler_get_stats(error_handler_stats_t *stats)
{
    if (stats == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    stats->total_errors = g_error_state.total_errors;
    stats->recovery_actions_taken = g_error_state.recovery_actions_taken;
    stats->health_checks_performed = g_error_state.health_checks_performed;
    stats->uptime_ms = esp_timer_get_time() / 1000 - g_error_state.start_time;
    
    memcpy(stats->errors_by_type, g_error_state.errors_by_type, 
           sizeof(g_error_state.errors_by_type));
    memcpy(stats->errors_by_severity, g_error_state.errors_by_severity, 
           sizeof(g_error_state.errors_by_severity));
    
    return ESP_OK;
}

/**
 * @brief Reset error statistics
 */
esp_err_t error_handler_reset_stats(void)
{
    memset(&g_error_state.errors_by_type, 0, sizeof(g_error_state.errors_by_type));
    memset(&g_error_state.errors_by_severity, 0, sizeof(g_error_state.errors_by_severity));
    g_error_state.total_errors = 0;
    g_error_state.recovery_actions_taken = 0;
    g_error_state.health_checks_performed = 0;
    
    ESP_LOGI(TAG, "Error statistics reset");
    return ESP_OK;
}

/**
 * @brief Set recovery strategy for error type
 */
esp_err_t error_handler_set_recovery_strategy(error_type_t type, const recovery_strategy_t *strategy)
{
    if (type >= ERROR_TYPE_MAX || strategy == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memcpy(&g_recovery_strategies[type], strategy, sizeof(recovery_strategy_t));
    ESP_LOGI(TAG, "Recovery strategy updated for error type: %d", type);
    
    return ESP_OK;
}

/**
 * @brief Check if error handler is running
 */
bool error_handler_is_running(void)
{
    return g_error_handler_running;
}
