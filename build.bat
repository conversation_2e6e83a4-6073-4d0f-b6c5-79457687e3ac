@echo off
echo ========================================
echo ESP32-C2 TIMO Base Station Build Script
echo ========================================

echo 方案1: 使用ESP-IDF export.bat脚本设置环境
echo 正在运行 ESP-IDF export.bat...
call "D:\APP\Espressif\esp\v5.3.3\esp-idf\export.bat"

if errorlevel 1 (
    echo.
    echo export.bat 执行失败，尝试手动设置环境...
    echo.

    echo 方案2: 手动设置环境变量
    set "IDF_PATH=D:\APP\Espressif\esp\v5.3.3\esp-idf"
    set "PATH=D:\APP\Espressif\.espressif\v5.3.3\tools\riscv32-esp-elf\esp-13.2.0_20240530\riscv32-esp-elf\bin;%PATH%"
    set "PATH=D:\APP\Espressif\.espressif\v5.3.3\tools\ninja\1.12.1;%PATH%"
    set "PATH=D:\APP\Espressif\.espressif\v5.3.3\tools\cmake\3.30.2\bin;%PATH%"
    set "PATH=D:\APP\Espressif\.espressif\v5.3.3\python_env\idf5.3_py3.11_env\Scripts;%PATH%"
)

echo.
echo 检查工具可用性...
where idf.py >nul 2>&1
if errorlevel 1 (
    echo 警告: idf.py 未找到，尝试直接使用Python...
    set "IDF_PY_CMD=python %IDF_PATH%\tools\idf.py"
) else (
    echo idf.py 找到！
    set "IDF_PY_CMD=idf.py"
)

echo.
echo 清理之前的构建...
if exist build rmdir /s /q build

echo.
echo 开始构建项目...
echo 设置目标芯片为 ESP32-C2...
%IDF_PY_CMD% set-target esp32c2

if errorlevel 1 (
    echo 设置目标失败！
    pause
    exit /b 1
)

echo.
echo 开始编译...
%IDF_PY_CMD% build

if errorlevel 1 (
    echo.
    echo ❌ 编译失败！
    echo 请检查错误信息并修复问题。
    pause
    exit /b 1
) else (
    echo.
    echo ✅ 编译成功！
    echo.
    echo 要烧录固件，请运行：
    echo %IDF_PY_CMD% -p COM端口号 flash monitor
    echo （请将 COM端口号 替换为实际的串口号）
)

echo.
pause
