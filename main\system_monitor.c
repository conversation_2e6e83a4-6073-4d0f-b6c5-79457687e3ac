/**
 * @file system_monitor.c
 * @brief Enhanced System Monitoring for ESP32-C2
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "system_monitor.h"
#include "base_config.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "esp_heap_caps.h"
#include "esp_system.h"
#include "esp_task_wdt.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"

static const char *TAG = "SYS_MONITOR";

/* System monitoring data */
static system_stats_t g_system_stats = {0};
static system_performance_t g_performance = {0};
static error_statistics_t g_error_stats = {0};
static bool g_monitor_enabled = false;

/* Task monitoring */
static TaskHandle_t g_monitor_task_handle = NULL;
static QueueHandle_t g_alert_queue = NULL;

/**
 * @brief Initialize system monitoring
 */
esp_err_t system_monitor_init(void)
{
    ESP_LOGI(TAG, "Initializing system monitor...");
    
    // Create alert queue
    g_alert_queue = xQueueCreate(SYSTEM_ALERT_QUEUE_SIZE, sizeof(system_alert_t));
    if (g_alert_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create alert queue");
        return ESP_ERR_NO_MEM;
    }
    
    // Initialize statistics
    memset(&g_system_stats, 0, sizeof(g_system_stats));
    memset(&g_performance, 0, sizeof(g_performance));
    memset(&g_error_stats, 0, sizeof(g_error_stats));
    
    g_system_stats.boot_time = esp_timer_get_time() / 1000;
    g_system_stats.total_heap_size = heap_caps_get_total_size(MALLOC_CAP_8BIT);
    
    ESP_LOGI(TAG, "System monitor initialized");
    return ESP_OK;
}

/**
 * @brief Start system monitoring
 */
esp_err_t system_monitor_start(void)
{
    if (g_monitor_enabled) {
        ESP_LOGW(TAG, "System monitor already running");
        return ESP_OK;
    }
    
    // Create monitoring task
    BaseType_t ret = xTaskCreate(
        system_monitor_task,
        "sys_monitor",
        TASK_STACK_SIZE_MEDIUM,
        NULL,
        TASK_PRIORITY_LOW,
        &g_monitor_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create monitor task");
        return ESP_ERR_NO_MEM;
    }
    
    g_monitor_enabled = true;
    ESP_LOGI(TAG, "System monitor started");
    return ESP_OK;
}

/**
 * @brief Stop system monitoring
 */
esp_err_t system_monitor_stop(void)
{
    if (!g_monitor_enabled) {
        return ESP_OK;
    }
    
    g_monitor_enabled = false;
    
    if (g_monitor_task_handle) {
        vTaskDelete(g_monitor_task_handle);
        g_monitor_task_handle = NULL;
    }
    
    ESP_LOGI(TAG, "System monitor stopped");
    return ESP_OK;
}

/**
 * @brief Update system statistics
 */
static void update_system_stats(void)
{
    // Memory statistics
    g_system_stats.free_heap_size = esp_get_free_heap_size();
    g_system_stats.min_free_heap_size = esp_get_minimum_free_heap_size();
    g_system_stats.largest_free_block = heap_caps_get_largest_free_block(MALLOC_CAP_8BIT);
    
    // Calculate memory usage percentage
    g_system_stats.heap_usage_percent = 
        ((g_system_stats.total_heap_size - g_system_stats.free_heap_size) * 100) / 
        g_system_stats.total_heap_size;
    
    // Update uptime
    g_system_stats.uptime_ms = esp_timer_get_time() / 1000;
    
    // Task count
    g_system_stats.task_count = uxTaskGetNumberOfTasks();
    
    // Update performance metrics
    static uint32_t last_update_time = 0;
    uint32_t current_time = g_system_stats.uptime_ms;
    
    if (last_update_time > 0) {
        uint32_t interval = current_time - last_update_time;
        if (interval > 0) {
            // Calculate CPU usage (simplified)
            g_performance.cpu_usage_percent = 
                (interval > SYSTEM_MONITOR_INTERVAL_MS) ? 
                ((interval - SYSTEM_MONITOR_INTERVAL_MS) * 100) / interval : 0;
        }
    }
    
    last_update_time = current_time;
    
    // Update counters
    g_performance.monitor_cycles++;
}

/**
 * @brief Check for system alerts
 */
static void check_system_alerts(void)
{
    system_alert_t alert;
    
    // Memory usage alert
    if (g_system_stats.heap_usage_percent > MEMORY_USAGE_ALERT_THRESHOLD) {
        alert.type = ALERT_MEMORY_HIGH;
        alert.severity = (g_system_stats.heap_usage_percent > MEMORY_USAGE_CRITICAL_THRESHOLD) ? 
                        SEVERITY_CRITICAL : SEVERITY_WARNING;
        alert.timestamp = g_system_stats.uptime_ms;
        alert.value = g_system_stats.heap_usage_percent;
        
        if (xQueueSend(g_alert_queue, &alert, 0) == pdTRUE) {
            ESP_LOGW(TAG, "Memory usage alert: %d%%", g_system_stats.heap_usage_percent);
        }
    }
    
    // Task count alert
    if (g_system_stats.task_count > MAX_TASK_COUNT_THRESHOLD) {
        alert.type = ALERT_TASK_COUNT_HIGH;
        alert.severity = SEVERITY_WARNING;
        alert.timestamp = g_system_stats.uptime_ms;
        alert.value = g_system_stats.task_count;
        
        if (xQueueSend(g_alert_queue, &alert, 0) == pdTRUE) {
            ESP_LOGW(TAG, "High task count alert: %d tasks", g_system_stats.task_count);
        }
    }
    
    // Heap fragmentation alert
    if (g_system_stats.largest_free_block < (g_system_stats.free_heap_size / 2)) {
        alert.type = ALERT_HEAP_FRAGMENTATION;
        alert.severity = SEVERITY_WARNING;
        alert.timestamp = g_system_stats.uptime_ms;
        alert.value = (g_system_stats.free_heap_size - g_system_stats.largest_free_block);
        
        if (xQueueSend(g_alert_queue, &alert, 0) == pdTRUE) {
            ESP_LOGW(TAG, "Heap fragmentation detected");
        }
    }
}

/**
 * @brief System monitoring task
 */
void system_monitor_task(void *pvParameters)
{
    ESP_LOGI(TAG, "System monitor task started");
    
    TickType_t last_wake_time = xTaskGetTickCount();
    
    while (g_monitor_enabled) {
        // Update system statistics
        update_system_stats();
        
        // Check for alerts
        check_system_alerts();
        
        // Log periodic status (every 30 seconds)
        static uint32_t log_counter = 0;
        if (++log_counter >= (30000 / SYSTEM_MONITOR_INTERVAL_MS)) {
            log_counter = 0;
            ESP_LOGI(TAG, "System Status - Heap: %d/%d bytes (%.1f%%), Tasks: %d, Uptime: %lu ms",
                     g_system_stats.free_heap_size,
                     g_system_stats.total_heap_size,
                     (float)g_system_stats.heap_usage_percent,
                     g_system_stats.task_count,
                     g_system_stats.uptime_ms);
        }
        
        // Wait for next monitoring cycle
        vTaskDelayUntil(&last_wake_time, pdMS_TO_TICKS(SYSTEM_MONITOR_INTERVAL_MS));
    }
    
    ESP_LOGI(TAG, "System monitor task stopped");
    vTaskDelete(NULL);
}

/**
 * @brief Get current system statistics
 */
esp_err_t system_monitor_get_stats(system_stats_t *stats)
{
    if (stats == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memcpy(stats, &g_system_stats, sizeof(system_stats_t));
    return ESP_OK;
}

/**
 * @brief Get performance metrics
 */
esp_err_t system_monitor_get_performance(system_performance_t *performance)
{
    if (performance == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memcpy(performance, &g_performance, sizeof(system_performance_t));
    return ESP_OK;
}

/**
 * @brief Get error statistics
 */
esp_err_t system_monitor_get_errors(error_statistics_t *errors)
{
    if (errors == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memcpy(errors, &g_error_stats, sizeof(error_statistics_t));
    return ESP_OK;
}

/**
 * @brief Record an error
 */
esp_err_t system_monitor_record_error(error_type_t type, const char *description)
{
    g_error_stats.total_errors++;
    
    switch (type) {
    case ERROR_TYPE_MEMORY:
        g_error_stats.memory_errors++;
        break;
    case ERROR_TYPE_COMMUNICATION:
        g_error_stats.communication_errors++;
        break;
    case ERROR_TYPE_HARDWARE:
        g_error_stats.hardware_errors++;
        break;
    case ERROR_TYPE_SOFTWARE:
        g_error_stats.software_errors++;
        break;
    default:
        g_error_stats.other_errors++;
        break;
    }
    
    g_error_stats.last_error_time = esp_timer_get_time() / 1000;
    
    ESP_LOGE(TAG, "Error recorded [%d]: %s", type, description ? description : "Unknown");
    
    return ESP_OK;
}

/**
 * @brief Get next system alert
 */
esp_err_t system_monitor_get_alert(system_alert_t *alert, uint32_t timeout_ms)
{
    if (alert == NULL || g_alert_queue == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    TickType_t timeout_ticks = (timeout_ms == 0) ? 0 : pdMS_TO_TICKS(timeout_ms);
    
    if (xQueueReceive(g_alert_queue, alert, timeout_ticks) == pdTRUE) {
        return ESP_OK;
    }
    
    return ESP_ERR_TIMEOUT;
}

/**
 * @brief Reset error statistics
 */
esp_err_t system_monitor_reset_errors(void)
{
    memset(&g_error_stats, 0, sizeof(g_error_stats));
    ESP_LOGI(TAG, "Error statistics reset");
    return ESP_OK;
}

/**
 * @brief Check if monitoring is enabled
 */
bool system_monitor_is_enabled(void)
{
    return g_monitor_enabled;
}
