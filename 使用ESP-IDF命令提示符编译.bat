@echo off
echo ========================================
echo ESP32-C2 TIMO项目编译指南
echo ========================================
echo.
echo 请按照以下步骤操作：
echo.
echo 1. 打开Windows开始菜单
echo 2. 搜索 "ESP-IDF"
echo 3. 点击 "ESP-IDF Command Prompt" 或 "ESP-IDF PowerShell"
echo 4. 在打开的命令窗口中运行以下命令：
echo.
echo    cd /d "e:\Electronics\MyProject\ESP\Timo_main\2_base_station_firmware"
echo    idf.py set-target esp32c2
echo    idf.py build
echo.
echo 如果编译成功，可以继续烧录：
echo    idf.py -p COM端口号 flash monitor
echo.
echo ========================================
echo 注意事项：
echo - 请将 COM端口号 替换为实际的串口号（如 COM3）
echo - 如果找不到ESP-IDF命令提示符，请检查ESP-IDF是否正确安装
echo - 编译过程可能需要几分钟时间
echo ========================================
echo.
pause
