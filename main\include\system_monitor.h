/**
 * @file system_monitor.h
 * @brief Enhanced System Monitoring Header for ESP32-C2
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SYSTEM_MONITOR_H
#define SYSTEM_MONITOR_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* System monitoring configuration */
#define SYSTEM_MONITOR_INTERVAL_MS          1000    // 1 second
#define SYSTEM_ALERT_QUEUE_SIZE             10
#define MEMORY_USAGE_ALERT_THRESHOLD        75      // 75%
#define MEMORY_USAGE_CRITICAL_THRESHOLD     90      // 90%
#define MAX_TASK_COUNT_THRESHOLD            15

/* Error types */
typedef enum {
    ERROR_TYPE_MEMORY = 0,
    ERROR_TYPE_COMMUNICATION,
    ERROR_TYPE_HARDWARE,
    ERROR_TYPE_SOFTWARE,
    ERROR_TYPE_OTHER,
    ERROR_TYPE_MAX
} error_type_t;

/* <PERSON><PERSON> types */
typedef enum {
    ALERT_MEMORY_HIGH = 0,
    ALERT_TASK_COUNT_HIGH,
    ALERT_HEAP_FRAGMENTATION,
    ALERT_CPU_HIGH,
    ALERT_COMMUNICATION_FAILURE,
    ALERT_HARDWARE_FAILURE,
    ALERT_TYPE_MAX
} alert_type_t;

/* Alert severity */
typedef enum {
    SEVERITY_INFO = 0,
    SEVERITY_WARNING,
    SEVERITY_ERROR,
    SEVERITY_CRITICAL
} alert_severity_t;

/* System statistics structure */
typedef struct {
    uint32_t uptime_ms;                 // System uptime in milliseconds
    uint32_t boot_time;                 // Boot timestamp
    size_t total_heap_size;             // Total heap size
    size_t free_heap_size;              // Current free heap
    size_t min_free_heap_size;          // Minimum free heap since boot
    size_t largest_free_block;          // Largest contiguous free block
    uint8_t heap_usage_percent;         // Heap usage percentage
    uint8_t task_count;                 // Number of active tasks
    uint32_t reset_reason;              // Last reset reason
} system_stats_t;

/* Performance metrics structure */
typedef struct {
    uint8_t cpu_usage_percent;          // CPU usage percentage
    uint32_t context_switches;          // Number of context switches
    uint32_t interrupts_count;          // Number of interrupts
    uint32_t monitor_cycles;            // Number of monitoring cycles
    uint32_t max_task_runtime_ms;       // Maximum task runtime
    uint32_t avg_task_runtime_ms;       // Average task runtime
} system_performance_t;

/* Error statistics structure */
typedef struct {
    uint32_t total_errors;              // Total error count
    uint32_t memory_errors;             // Memory-related errors
    uint32_t communication_errors;      // Communication errors
    uint32_t hardware_errors;           // Hardware errors
    uint32_t software_errors;           // Software errors
    uint32_t other_errors;              // Other errors
    uint32_t last_error_time;           // Timestamp of last error
} error_statistics_t;

/* System alert structure */
typedef struct {
    alert_type_t type;                  // Alert type
    alert_severity_t severity;          // Alert severity
    uint32_t timestamp;                 // Alert timestamp
    uint32_t value;                     // Alert value (context-dependent)
    char description[64];               // Alert description
} system_alert_t;

/**
 * @brief Initialize system monitoring
 * @return esp_err_t
 */
esp_err_t system_monitor_init(void);

/**
 * @brief Start system monitoring
 * @return esp_err_t
 */
esp_err_t system_monitor_start(void);

/**
 * @brief Stop system monitoring
 * @return esp_err_t
 */
esp_err_t system_monitor_stop(void);

/**
 * @brief System monitoring task
 * @param pvParameters Task parameters
 */
void system_monitor_task(void *pvParameters);

/**
 * @brief Get current system statistics
 * @param stats Output statistics structure
 * @return esp_err_t
 */
esp_err_t system_monitor_get_stats(system_stats_t *stats);

/**
 * @brief Get performance metrics
 * @param performance Output performance structure
 * @return esp_err_t
 */
esp_err_t system_monitor_get_performance(system_performance_t *performance);

/**
 * @brief Get error statistics
 * @param errors Output error statistics structure
 * @return esp_err_t
 */
esp_err_t system_monitor_get_errors(error_statistics_t *errors);

/**
 * @brief Record an error
 * @param type Error type
 * @param description Error description (optional)
 * @return esp_err_t
 */
esp_err_t system_monitor_record_error(error_type_t type, const char *description);

/**
 * @brief Get next system alert
 * @param alert Output alert structure
 * @param timeout_ms Timeout in milliseconds (0 = no wait)
 * @return esp_err_t
 */
esp_err_t system_monitor_get_alert(system_alert_t *alert, uint32_t timeout_ms);

/**
 * @brief Reset error statistics
 * @return esp_err_t
 */
esp_err_t system_monitor_reset_errors(void);

/**
 * @brief Check if monitoring is enabled
 * @return bool
 */
bool system_monitor_is_enabled(void);

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_MONITOR_H */
