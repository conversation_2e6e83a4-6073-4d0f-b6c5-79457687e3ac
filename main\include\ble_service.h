/**
 * @file ble_service.h
 * @brief TIMO底座BLE服务头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef BLE_SERVICE_H
#define BLE_SERVICE_H

#include "esp_err.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief BLE数据接收回调函数类型
 * @param data 接收到的数据
 * @param length 数据长度
 */
typedef void (*ble_data_received_cb_t)(const uint8_t *data, uint16_t length);

/**
 * @brief 初始化BLE服务
 * @return esp_err_t 
 */
esp_err_t ble_service_init(void);

/**
 * @brief 启动BLE服务
 * @return esp_err_t 
 */
esp_err_t ble_service_start(void);

/**
 * @brief 停止BLE服务
 * @return esp_err_t 
 */
esp_err_t ble_service_stop(void);

/**
 * @brief 开始BLE广播
 * @return esp_err_t 
 */
esp_err_t ble_service_start_advertising(void);

/**
 * @brief 发送数据到连接的设备
 * @param data 要发送的数据
 * @param length 数据长度
 * @return esp_err_t 
 */
esp_err_t ble_service_send_data(const uint8_t *data, uint16_t length);

/**
 * @brief 设置数据接收回调函数
 * @param callback 回调函数
 */
void ble_service_set_data_received_callback(ble_data_received_cb_t callback);

/**
 * @brief 获取BLE连接状态
 * @return true 已连接
 * @return false 未连接
 */
bool ble_service_is_connected(void);

#ifdef __cplusplus
}
#endif

#endif /* BLE_SERVICE_H */
