#
                # Automatically generated file. DO NOT EDIT.
                # Espressif IoT Development Framework (ESP-IDF) Configuration cmake include file
                #
set(CONFIG_SOC_ADC_SUPPORTED "y")
set(CONFIG_SOC_DEDICATED_GPIO_SUPPORTED "y")
set(CONFIG_SOC_UART_SUPPORTED "y")
set(CONFIG_SOC_GDMA_SUPPORTED "y")
set(CONFIG_SOC_AHB_GDMA_SUPPORTED "y")
set(CONFIG_SOC_GPTIMER_SUPPORTED "y")
set(CONFIG_SOC_PHY_SUPPORTED "y")
set(CONFIG_SOC_BT_SUPPORTED "y")
set(CONFIG_SOC_WIFI_SUPPORTED "y")
set(CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED "y")
set(CONFIG_SOC_SUPPORTS_SECURE_DL_MODE "y")
set(CONFIG_SOC_EFUSE_CONSISTS_OF_ONE_KEY_BLOCK "y")
set(CONFIG_SOC_EFUSE_SUPPORTED "y")
set(CONFIG_SOC_TEMP_SENSOR_SUPPORTED "y")
set(CONFIG_SOC_LEDC_SUPPORTED "y")
set(CONFIG_SOC_I2C_SUPPORTED "y")
set(CONFIG_SOC_GPSPI_SUPPORTED "y")
set(CONFIG_SOC_SHA_SUPPORTED "y")
set(CONFIG_SOC_ECC_SUPPORTED "y")
set(CONFIG_SOC_FLASH_ENC_SUPPORTED "y")
set(CONFIG_SOC_SECURE_BOOT_SUPPORTED "y")
set(CONFIG_SOC_SYSTIMER_SUPPORTED "y")
set(CONFIG_SOC_BOD_SUPPORTED "y")
set(CONFIG_SOC_CLK_TREE_SUPPORTED "y")
set(CONFIG_SOC_ASSIST_DEBUG_SUPPORTED "y")
set(CONFIG_SOC_WDT_SUPPORTED "y")
set(CONFIG_SOC_SPI_FLASH_SUPPORTED "y")
set(CONFIG_SOC_RNG_SUPPORTED "y")
set(CONFIG_SOC_LIGHT_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_DEEP_SLEEP_SUPPORTED "y")
set(CONFIG_SOC_LP_PERIPH_SHARE_INTERRUPT "y")
set(CONFIG_SOC_PM_SUPPORTED "y")
set(CONFIG_SOC_XTAL_SUPPORT_26M "y")
set(CONFIG_SOC_XTAL_SUPPORT_40M "y")
set(CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED "y")
set(CONFIG_SOC_ADC_DIG_IIR_FILTER_SUPPORTED "y")
set(CONFIG_SOC_ADC_MONITOR_SUPPORTED "y")
set(CONFIG_SOC_ADC_PERIPH_NUM "1")
set(CONFIG_SOC_ADC_MAX_CHANNEL_NUM "5")
set(CONFIG_SOC_ADC_ATTEN_NUM "4")
set(CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM "1")
set(CONFIG_SOC_ADC_PATT_LEN_MAX "8")
set(CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM "2")
set(CONFIG_SOC_ADC_DIGI_MONITOR_NUM "2")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH "83333")
set(CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW "611")
set(CONFIG_SOC_ADC_RTC_MIN_BITWIDTH "12")
set(CONFIG_SOC_ADC_RTC_MAX_BITWIDTH "12")
set(CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED "y")
set(CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED "y")
set(CONFIG_SOC_ADC_SHARED_POWER "y")
set(CONFIG_SOC_BROWNOUT_RESET_SUPPORTED "y")
set(CONFIG_SOC_SHARED_IDCACHE_SUPPORTED "y")
set(CONFIG_SOC_CPU_CORES_NUM "1")
set(CONFIG_SOC_CPU_INTR_NUM "32")
set(CONFIG_SOC_CPU_HAS_FLEXIBLE_INTC "y")
set(CONFIG_SOC_CPU_HAS_CSR_PC "y")
set(CONFIG_SOC_CPU_BREAKPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINTS_NUM "2")
set(CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE "0x80000000")
set(CONFIG_SOC_CPU_IDRAM_SPLIT_USING_PMP "y")
set(CONFIG_SOC_ECC_SUPPORT_POINT_VERIFY_QUIRK "y")
set(CONFIG_SOC_AHB_GDMA_VERSION "1")
set(CONFIG_SOC_GDMA_NUM_GROUPS_MAX "1")
set(CONFIG_SOC_GDMA_PAIRS_PER_GROUP_MAX "1")
set(CONFIG_SOC_GPIO_PORT "1")
set(CONFIG_SOC_GPIO_PIN_COUNT "21")
set(CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER "y")
set(CONFIG_SOC_GPIO_FILTER_CLK_SUPPORT_APB "y")
set(CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD "y")
set(CONFIG_SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP "y")
set(CONFIG_SOC_GPIO_IN_RANGE_MAX "20")
set(CONFIG_SOC_GPIO_OUT_RANGE_MAX "20")
set(CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK "0")
set(CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT "6")
set(CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK "0x1fffc0")
set(CONFIG_SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX "y")
set(CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM "3")
set(CONFIG_SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP "y")
set(CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM "8")
set(CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM "8")
set(CONFIG_SOC_DEDIC_PERIPH_ALWAYS_ENABLE "y")
set(CONFIG_SOC_I2C_NUM "1")
set(CONFIG_SOC_HP_I2C_NUM "1")
set(CONFIG_SOC_I2C_FIFO_LEN "16")
set(CONFIG_SOC_I2C_CMD_REG_NUM "8")
set(CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS "y")
set(CONFIG_SOC_I2C_SUPPORT_XTAL "y")
set(CONFIG_SOC_I2C_SUPPORT_RTC "y")
set(CONFIG_SOC_I2C_SUPPORT_10BIT_ADDR "y")
set(CONFIG_SOC_LEDC_SUPPORT_PLL_DIV_CLOCK "y")
set(CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK "y")
set(CONFIG_SOC_LEDC_CHANNEL_NUM "6")
set(CONFIG_SOC_LEDC_TIMER_BIT_WIDTH "14")
set(CONFIG_SOC_LEDC_SUPPORT_FADE_STOP "y")
set(CONFIG_SOC_MMU_PAGE_SIZE_CONFIGURABLE "y")
set(CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM "1")
set(CONFIG_SOC_MMU_PERIPH_NUM "1")
set(CONFIG_SOC_MPU_MIN_REGION_SIZE "0x20000000")
set(CONFIG_SOC_MPU_REGIONS_MAX_NUM "8")
set(CONFIG_SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH "128")
set(CONFIG_SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM "108")
set(CONFIG_SOC_RTCIO_PIN_COUNT "0")
set(CONFIG_SOC_RSA_MAX_BIT_LEN "3072")
set(CONFIG_SOC_SHA_SUPPORT_RESUME "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA1 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA224 "y")
set(CONFIG_SOC_SHA_SUPPORT_SHA256 "y")
set(CONFIG_SOC_SPI_PERIPH_NUM "2")
set(CONFIG_SOC_SPI_MAX_CS_NUM "6")
set(CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE "64")
set(CONFIG_SOC_SPI_SUPPORT_DDRCLK "y")
set(CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS "y")
set(CONFIG_SOC_SPI_SUPPORT_CD_SIG "y")
set(CONFIG_SOC_SPI_SUPPORT_CONTINUOUS_TRANS "y")
set(CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2 "y")
set(CONFIG_SOC_SPI_SUPPORT_CLK_XTAL "y")
set(CONFIG_SOC_SPI_SUPPORT_CLK_PLL_F40M "y")
set(CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT "y")
set(CONFIG_SOC_SPI_SCT_SUPPORTED "y")
set(CONFIG_SOC_SPI_SCT_REG_NUM "14")
set(CONFIG_SOC_SPI_SCT_BUFFER_NUM_MAX "y")
set(CONFIG_SOC_SPI_SCT_CONF_BITLEN_MAX "0x3fffa")
set(CONFIG_SOC_MEMSPI_IS_INDEPENDENT "y")
set(CONFIG_SOC_SPI_MAX_PRE_DIVIDER "16")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_IDLE_INTR "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_CHECK_SUS "y")
set(CONFIG_SOC_SPI_MEM_SUPPORT_WRAP "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_60M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_30M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED "y")
set(CONFIG_SOC_MEMSPI_SRC_FREQ_15M_SUPPORTED "y")
set(CONFIG_SOC_SYSTIMER_COUNTER_NUM "2")
set(CONFIG_SOC_SYSTIMER_ALARM_NUM "3")
set(CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO "32")
set(CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI "20")
set(CONFIG_SOC_SYSTIMER_FIXED_DIVIDER "y")
set(CONFIG_SOC_SYSTIMER_INT_LEVEL "y")
set(CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE "y")
set(CONFIG_SOC_TIMER_GROUPS "1")
set(CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP "1")
set(CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH "54")
set(CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL "y")
set(CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS "1")
set(CONFIG_SOC_LP_TIMER_BIT_WIDTH_LO "32")
set(CONFIG_SOC_LP_TIMER_BIT_WIDTH_HI "16")
set(CONFIG_SOC_MWDT_SUPPORT_XTAL "y")
set(CONFIG_SOC_EFUSE_DIS_DOWNLOAD_ICACHE "y")
set(CONFIG_SOC_EFUSE_DIS_PAD_JTAG "y")
set(CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT "y")
set(CONFIG_SOC_SECURE_BOOT_V2_ECC "y")
set(CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS "1")
set(CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX "32")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128 "y")
set(CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128_DERIVED "y")
set(CONFIG_SOC_UART_NUM "2")
set(CONFIG_SOC_UART_HP_NUM "2")
set(CONFIG_SOC_UART_FIFO_LEN "128")
set(CONFIG_SOC_UART_BITRATE_MAX "2500000")
set(CONFIG_SOC_UART_SUPPORT_WAKEUP_INT "y")
set(CONFIG_SOC_UART_SUPPORT_PLL_F40M_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_RTC_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_XTAL_CLK "y")
set(CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND "y")
set(CONFIG_SOC_SUPPORT_COEXISTENCE "y")
set(CONFIG_SOC_COEX_HW_PTI "y")
set(CONFIG_SOC_EXTERNAL_COEX_ADVANCE "y")
set(CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE "21")
set(CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH "12")
set(CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_BT_WAKEUP "y")
set(CONFIG_SOC_PM_SUPPORT_RC_FAST_PD "y")
set(CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD "y")
set(CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED "y")
set(CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256 "y")
set(CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION "y")
set(CONFIG_SOC_CLK_OSC_SLOW_SUPPORTED "y")
set(CONFIG_SOC_WIFI_HW_TSF "y")
set(CONFIG_SOC_WIFI_FTM_SUPPORT "y")
set(CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW "y")
set(CONFIG_SOC_WIFI_PHY_NEEDS_USB_WORKAROUND "y")
set(CONFIG_SOC_BLE_SUPPORTED "y")
set(CONFIG_SOC_ESP_NIMBLE_CONTROLLER "y")
set(CONFIG_SOC_BLE_50_SUPPORTED "y")
set(CONFIG_SOC_BLE_DEVICE_PRIVACY_SUPPORTED "y")
set(CONFIG_SOC_BLUFI_SUPPORTED "y")
set(CONFIG_SOC_BLE_PERIODIC_ADV_ENH_SUPPORTED "y")
set(CONFIG_SOC_PHY_IMPROVE_RX_11B "y")
set(CONFIG_SOC_PHY_COMBO_MODULE "y")
set(CONFIG_IDF_CMAKE "y")
set(CONFIG_IDF_TOOLCHAIN "gcc")
set(CONFIG_IDF_TARGET_ARCH_RISCV "y")
set(CONFIG_IDF_TARGET_ARCH "riscv")
set(CONFIG_IDF_TARGET "esp32c2")
set(CONFIG_IDF_INIT_VERSION "5.3.3")
set(CONFIG_IDF_TARGET_ESP32C2 "y")
set(CONFIG_IDF_FIRMWARE_CHIP_ID "0xc")
set(CONFIG_APP_BUILD_TYPE_APP_2NDBOOT "y")
set(CONFIG_APP_BUILD_TYPE_RAM "")
set(CONFIG_APP_BUILD_GENERATE_BINARIES "y")
set(CONFIG_APP_BUILD_BOOTLOADER "y")
set(CONFIG_APP_BUILD_USE_FLASH_SECTIONS "y")
set(CONFIG_APP_REPRODUCIBLE_BUILD "")
set(CONFIG_APP_NO_BLOBS "")
set(CONFIG_BOOTLOADER_COMPILE_TIME_DATE "y")
set(CONFIG_BOOTLOADER_PROJECT_VER "1")
set(CONFIG_BOOTLOADER_OFFSET_IN_FLASH "0x0")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE "y")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_NONE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_ERROR "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_WARN "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_INFO "y")
set(CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG "")
set(CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE "")
set(CONFIG_BOOTLOADER_LOG_LEVEL "3")
set(CONFIG_BOOTLOADER_FLASH_DC_AWARE "")
set(CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT "y")
set(CONFIG_BOOTLOADER_FACTORY_RESET "")
set(CONFIG_BOOTLOADER_APP_TEST "")
set(CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_ENABLE "y")
set(CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE "")
set(CONFIG_BOOTLOADER_WDT_TIME_MS "9000")
set(CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON "")
set(CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS "")
set(CONFIG_SECURE_BOOT_V2_ECC_SUPPORTED "y")
set(CONFIG_SECURE_BOOT_V2_PREFERRED "y")
set(CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT "")
set(CONFIG_SECURE_BOOT "")
set(CONFIG_SECURE_FLASH_ENC_ENABLED "")
set(CONFIG_SECURE_ROM_DL_MODE_ENABLED "y")
set(CONFIG_APP_COMPILE_TIME_DATE "y")
set(CONFIG_APP_EXCLUDE_PROJECT_VER_VAR "")
set(CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR "")
set(CONFIG_APP_PROJECT_VER_FROM_CONFIG "")
set(CONFIG_APP_RETRIEVE_LEN_ELF_SHA "9")
set(CONFIG_ESP_ROM_HAS_CRC_LE "y")
set(CONFIG_ESP_ROM_HAS_CRC_BE "y")
set(CONFIG_ESP_ROM_UART_CLK_IS_XTAL "y")
set(CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING "y")
set(CONFIG_ESP_ROM_GET_CLK_FREQ "y")
set(CONFIG_ESP_ROM_HAS_RVFPLIB "y")
set(CONFIG_ESP_ROM_HAS_HAL_WDT "y")
set(CONFIG_ESP_ROM_HAS_HAL_SYSTIMER "y")
set(CONFIG_ESP_ROM_HAS_HEAP_TLSF "y")
set(CONFIG_ESP_ROM_TLSF_CHECK_PATCH "y")
set(CONFIG_ESP_ROM_MULTI_HEAP_WALK_PATCH "y")
set(CONFIG_ESP_ROM_HAS_LAYOUT_TABLE "y")
set(CONFIG_ESP_ROM_HAS_SPI_FLASH "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB "y")
set(CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT "y")
set(CONFIG_ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE "y")
set(CONFIG_ESP_ROM_RAM_APP_NEEDS_MMU_INIT "y")
set(CONFIG_ESP_ROM_HAS_MBEDTLS_CRYPTO_LIB "y")
set(CONFIG_ESP_ROM_HAS_SW_FLOAT "y")
set(CONFIG_ESP_ROM_USB_OTG_NUM "-1")
set(CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM "-1")
set(CONFIG_ESP_ROM_HAS_VERSION "y")
set(CONFIG_BOOT_ROM_LOG_ALWAYS_ON "y")
set(CONFIG_BOOT_ROM_LOG_ALWAYS_OFF "")
set(CONFIG_BOOT_ROM_LOG_ON_GPIO_HIGH "")
set(CONFIG_BOOT_ROM_LOG_ON_GPIO_LOW "")
set(CONFIG_ESPTOOLPY_NO_STUB "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QIO "")
set(CONFIG_ESPTOOLPY_FLASHMODE_QOUT "")
set(CONFIG_ESPTOOLPY_FLASHMODE_DIO "y")
set(CONFIG_ESPTOOLPY_FLASHMODE_DOUT "")
set(CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR "y")
set(CONFIG_ESPTOOLPY_FLASHMODE "dio")
set(CONFIG_ESPTOOLPY_FLASHFREQ_60M "y")
set(CONFIG_ESPTOOLPY_FLASHFREQ_30M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_20M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ_15M "")
set(CONFIG_ESPTOOLPY_FLASHFREQ "60m")
set(CONFIG_ESPTOOLPY_FLASHSIZE_1MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_2MB "y")
set(CONFIG_ESPTOOLPY_FLASHSIZE_4MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_8MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_16MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_32MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_64MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE_128MB "")
set(CONFIG_ESPTOOLPY_FLASHSIZE "2MB")
set(CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE "")
set(CONFIG_ESPTOOLPY_BEFORE_RESET "y")
set(CONFIG_ESPTOOLPY_BEFORE_NORESET "")
set(CONFIG_ESPTOOLPY_BEFORE "default_reset")
set(CONFIG_ESPTOOLPY_AFTER_RESET "y")
set(CONFIG_ESPTOOLPY_AFTER_NORESET "")
set(CONFIG_ESPTOOLPY_AFTER "hard_reset")
set(CONFIG_ESPTOOLPY_MONITOR_BAUD "115200")
set(CONFIG_PARTITION_TABLE_SINGLE_APP "y")
set(CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE "")
set(CONFIG_PARTITION_TABLE_TWO_OTA "")
set(CONFIG_PARTITION_TABLE_CUSTOM "")
set(CONFIG_PARTITION_TABLE_CUSTOM_FILENAME "partitions.csv")
set(CONFIG_PARTITION_TABLE_FILENAME "partitions_singleapp.csv")
set(CONFIG_PARTITION_TABLE_OFFSET "0x8000")
set(CONFIG_PARTITION_TABLE_MD5 "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_SIZE "")
set(CONFIG_COMPILER_OPTIMIZATION_PERF "")
set(CONFIG_COMPILER_OPTIMIZATION_NONE "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE "")
set(CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB "")
set(CONFIG_COMPILER_FLOAT_LIB_FROM_RVFPLIB "y")
set(CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT "")
set(CONFIG_COMPILER_HIDE_PATHS_MACROS "y")
set(CONFIG_COMPILER_CXX_EXCEPTIONS "")
set(CONFIG_COMPILER_CXX_RTTI "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NONE "y")
set(CONFIG_COMPILER_STACK_CHECK_MODE_NORM "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_STRONG "")
set(CONFIG_COMPILER_STACK_CHECK_MODE_ALL "")
set(CONFIG_COMPILER_WARN_WRITE_STRINGS "")
set(CONFIG_COMPILER_SAVE_RESTORE_LIBCALLS "")
set(CONFIG_COMPILER_DISABLE_GCC12_WARNINGS "")
set(CONFIG_COMPILER_DISABLE_GCC13_WARNINGS "")
set(CONFIG_COMPILER_DUMP_RTL_FILES "")
set(CONFIG_COMPILER_RT_LIB_GCCLIB "y")
set(CONFIG_COMPILER_RT_LIB_NAME "gcc")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING "")
set(CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE "y")
set(CONFIG_APPTRACE_DEST_JTAG "")
set(CONFIG_APPTRACE_DEST_NONE "y")
set(CONFIG_APPTRACE_DEST_UART1 "")
set(CONFIG_APPTRACE_DEST_UART_NONE "y")
set(CONFIG_APPTRACE_UART_TASK_PRIO "1")
set(CONFIG_APPTRACE_LOCK_ENABLE "y")
set(CONFIG_BT_ENABLED "y")
set(CONFIG_BT_BLUEDROID_ENABLED "")
set(CONFIG_BT_NIMBLE_ENABLED "y")
set(CONFIG_BT_CONTROLLER_ONLY "")
set(CONFIG_BT_CONTROLLER_ENABLED "y")
set(CONFIG_BT_CONTROLLER_DISABLED "")
set(CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_INTERNAL "y")
set(CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_DEFAULT "")
set(CONFIG_BT_NIMBLE_LOG_LEVEL_NONE "")
set(CONFIG_BT_NIMBLE_LOG_LEVEL_ERROR "")
set(CONFIG_BT_NIMBLE_LOG_LEVEL_WARNING "")
set(CONFIG_BT_NIMBLE_LOG_LEVEL_INFO "y")
set(CONFIG_BT_NIMBLE_LOG_LEVEL_DEBUG "")
set(CONFIG_BT_NIMBLE_LOG_LEVEL "1")
set(CONFIG_BT_NIMBLE_MAX_CONNECTIONS "1")
set(CONFIG_BT_NIMBLE_MAX_BONDS "3")
set(CONFIG_BT_NIMBLE_MAX_CCCDS "8")
set(CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM "0")
set(CONFIG_BT_NIMBLE_PINNED_TO_CORE "0")
set(CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE "4096")
set(CONFIG_BT_NIMBLE_ROLE_CENTRAL "y")
set(CONFIG_BT_NIMBLE_ROLE_PERIPHERAL "y")
set(CONFIG_BT_NIMBLE_ROLE_BROADCASTER "y")
set(CONFIG_BT_NIMBLE_ROLE_OBSERVER "y")
set(CONFIG_BT_NIMBLE_NVS_PERSIST "")
set(CONFIG_BT_NIMBLE_SMP_ID_RESET "")
set(CONFIG_BT_NIMBLE_SECURITY_ENABLE "y")
set(CONFIG_BT_NIMBLE_SM_LEGACY "y")
set(CONFIG_BT_NIMBLE_SM_SC "y")
set(CONFIG_BT_NIMBLE_SM_SC_DEBUG_KEYS "")
set(CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION "y")
set(CONFIG_BT_NIMBLE_SM_LVL "0")
set(CONFIG_BT_NIMBLE_SM_SC_ONLY "0")
set(CONFIG_BT_NIMBLE_DEBUG "")
set(CONFIG_BT_NIMBLE_DYNAMIC_SERVICE "")
set(CONFIG_BT_NIMBLE_SVC_GAP_DEVICE_NAME "nimble")
set(CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN "31")
set(CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU "256")
set(CONFIG_BT_NIMBLE_SVC_GAP_APPEARANCE "0x0")
set(CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT "24")
set(CONFIG_BT_NIMBLE_MSYS_1_BLOCK_SIZE "128")
set(CONFIG_BT_NIMBLE_MSYS_2_BLOCK_COUNT "24")
set(CONFIG_BT_NIMBLE_MSYS_2_BLOCK_SIZE "320")
set(CONFIG_BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT "24")
set(CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE "255")
set(CONFIG_BT_NIMBLE_TRANSPORT_EVT_SIZE "70")
set(CONFIG_BT_NIMBLE_TRANSPORT_EVT_COUNT "30")
set(CONFIG_BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT "8")
set(CONFIG_BT_NIMBLE_L2CAP_COC_SDU_BUFF_COUNT "1")
set(CONFIG_BT_NIMBLE_GATT_MAX_PROCS "4")
set(CONFIG_BT_NIMBLE_HS_FLOW_CTRL "")
set(CONFIG_BT_NIMBLE_RPA_TIMEOUT "900")
set(CONFIG_BT_NIMBLE_MESH "")
set(CONFIG_BT_NIMBLE_CRYPTO_STACK_MBEDTLS "y")
set(CONFIG_BT_NIMBLE_HS_STOP_TIMEOUT_MS "2000")
set(CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT "y")
set(CONFIG_BT_NIMBLE_MAX_CONN_REATTEMPT "3")
set(CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT "y")
set(CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY "y")
set(CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY "y")
set(CONFIG_BT_NIMBLE_EXT_ADV "")
set(CONFIG_BT_NIMBLE_EXT_SCAN "y")
set(CONFIG_BT_NIMBLE_ENABLE_PERIODIC_SYNC "y")
set(CONFIG_BT_NIMBLE_MAX_PERIODIC_SYNCS "0")
set(CONFIG_BT_NIMBLE_MAX_PERIODIC_ADVERTISER_LIST "5")
set(CONFIG_BT_NIMBLE_GATT_CACHING "")
set(CONFIG_BT_NIMBLE_WHITELIST_SIZE "12")
set(CONFIG_BT_NIMBLE_TEST_THROUGHPUT_TEST "")
set(CONFIG_BT_NIMBLE_BLUFI_ENABLE "")
set(CONFIG_BT_NIMBLE_USE_ESP_TIMER "y")
set(CONFIG_BT_NIMBLE_BLE_GATT_BLOB_TRANSFER "")
set(CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE "")
set(CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ENC "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHN "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHR "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_CAR_CHAR_NOT_SUPP "y")
set(CONFIG_BT_NIMBLE_SVC_GAP_CAR_NOT_SUPP "")
set(CONFIG_BT_NIMBLE_SVC_GAP_CAR_SUPP "")
set(CONFIG_BT_NIMBLE_SVC_GAP_CENT_ADDR_RESOLUTION "-1")
set(CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE "")
set(CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_ENC "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHEN "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHOR "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SLAVE_LATENCY "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO "0")
set(CONFIG_BT_NIMBLE_SVC_GAP_GATT_SECURITY_LEVEL "")
set(CONFIG_BT_NIMBLE_HID_SERVICE "")
set(CONFIG_BT_NIMBLE_SVC_BAS_BATTERY_LEVEL_NOTIFY "")
set(CONFIG_BT_NIMBLE_SVC_DIS_MANUFACTURER_NAME "")
set(CONFIG_BT_NIMBLE_SVC_DIS_SERIAL_NUMBER "")
set(CONFIG_BT_NIMBLE_SVC_DIS_HARDWARE_REVISION "")
set(CONFIG_BT_NIMBLE_SVC_DIS_FIRMWARE_REVISION "")
set(CONFIG_BT_NIMBLE_SVC_DIS_SOFTWARE_REVISION "")
set(CONFIG_BT_NIMBLE_SVC_DIS_SYSTEM_ID "")
set(CONFIG_BT_NIMBLE_SVC_DIS_PNP_ID "")
set(CONFIG_BT_NIMBLE_SVC_DIS_INCLUDED "")
set(CONFIG_BT_NIMBLE_VS_SUPPORT "")
set(CONFIG_BT_NIMBLE_ENC_ADV_DATA "")
set(CONFIG_BT_NIMBLE_HIGH_DUTY_ADV_ITVL "")
set(CONFIG_BT_NIMBLE_HOST_ALLOW_CONNECT_WITH_SCAN "")
set(CONFIG_BT_NIMBLE_HOST_QUEUE_CONG_CHECK "")
set(CONFIG_BT_NIMBLE_GATTC_PROC_PREEMPTION_PROTECT "")
set(CONFIG_UART_HW_FLOWCTRL_DISABLE "y")
set(CONFIG_UART_HW_FLOWCTRL_CTS_RTS "")
set(CONFIG_BT_NIMBLE_HCI_UART_FLOW_CTRL "0")
set(CONFIG_BT_NIMBLE_HCI_UART_RTS_PIN "19")
set(CONFIG_BT_NIMBLE_HCI_UART_CTS_PIN "23")
set(CONFIG_BT_NIMBLE_EATT_CHAN_NUM "0")
set(CONFIG_BT_NIMBLE_SUBRATE "")
set(CONFIG_BT_LE_HCI_INTERFACE_USE_RAM "y")
set(CONFIG_BT_LE_HCI_INTERFACE_USE_UART "")
set(CONFIG_BT_LE_CONTROLLER_NPL_OS_PORTING_SUPPORT "y")
set(CONFIG_BT_LE_CONTROLLER_TASK_STACK_SIZE "4096")
set(CONFIG_BT_LE_CONTROLLER_LOG_ENABLED "")
set(CONFIG_BT_LE_LL_RESOLV_LIST_SIZE "4")
set(CONFIG_BT_LE_LL_DUP_SCAN_LIST_COUNT "20")
set(CONFIG_BT_LE_LL_SCA "60")
set(CONFIG_BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EN "")
set(CONFIG_BT_LE_COEX_PHY_CODED_TX_RX_TLIM_DIS "y")
set(CONFIG_BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EFF "0")
set(CONFIG_BT_LE_SLEEP_ENABLE "")
set(CONFIG_BT_LE_LP_CLK_SRC_MAIN_XTAL "y")
set(CONFIG_BT_LE_LP_CLK_SRC_DEFAULT "")
set(CONFIG_BT_LE_RELEASE_IRAM_SUPPORTED "y")
set(CONFIG_BT_LE_TX_CCA_ENABLED "")
set(CONFIG_BT_LE_DTM_ENABLED "")
set(CONFIG_BT_LE_FEAT_LL_ENCRYPTION "y")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N24 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N21 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N18 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N15 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N12 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N9 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N6 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N3 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N0 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P3 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P6 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P9 "y")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P12 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P15 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P18 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P20 "")
set(CONFIG_BT_LE_DFT_TX_POWER_LEVEL_DBM_EFF "9")
set(CONFIG_BT_CTRL_RUN_IN_FLASH_ONLY "")
set(CONFIG_BT_LE_CTRL_CHECK_CONNECT_IND_ACCESS_ADDRESS "")
set(CONFIG_BT_RELEASE_IRAM "")
set(CONFIG_BT_ALARM_MAX_NUM "50")
set(CONFIG_BT_BLE_LOG_SPI_OUT_ENABLED "")
set(CONFIG_BT_HCI_LOG_DEBUG_EN "")
set(CONFIG_BLE_MESH "")
set(CONFIG_CONSOLE_SORTED_HELP "")
set(CONFIG_ADC_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_ADC_SKIP_LEGACY_CONFLICT_CHECK "")
set(CONFIG_ADC_CALI_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_GPTIMER_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_GPTIMER_SKIP_LEGACY_CONFLICT_CHECK "")
set(CONFIG_TEMP_SENSOR_SUPPRESS_DEPRECATE_WARN "")
set(CONFIG_TEMP_SENSOR_SKIP_LEGACY_CONFLICT_CHECK "")
set(CONFIG_EFUSE_CUSTOM_TABLE "")
set(CONFIG_EFUSE_VIRTUAL "")
set(CONFIG_EFUSE_MAX_BLK_LEN "256")
set(CONFIG_ESP_TLS_USING_MBEDTLS "y")
set(CONFIG_ESP_TLS_CLIENT_SESSION_TICKETS "")
set(CONFIG_ESP_TLS_SERVER_SESSION_TICKETS "")
set(CONFIG_ESP_TLS_SERVER_CERT_SELECT_HOOK "")
set(CONFIG_ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL "")
set(CONFIG_ESP_TLS_PSK_VERIFICATION "")
set(CONFIG_ESP_TLS_INSECURE "")
set(CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM "")
set(CONFIG_ADC_ENABLE_DEBUG_LOG "")
set(CONFIG_ESP_COEX_ENABLED "y")
set(CONFIG_ESP_COEX_SW_COEXIST_ENABLE "y")
set(CONFIG_ESP_COEX_POWER_MANAGEMENT "")
set(CONFIG_ESP_COEX_GPIO_DEBUG "")
set(CONFIG_ESP_ERR_TO_NAME_LOOKUP "y")
set(CONFIG_GPIO_CTRL_FUNC_IN_IRAM "")
set(CONFIG_GPTIMER_ISR_HANDLER_IN_IRAM "y")
set(CONFIG_GPTIMER_CTRL_FUNC_IN_IRAM "")
set(CONFIG_GPTIMER_ISR_IRAM_SAFE "")
set(CONFIG_GPTIMER_OBJ_CACHE_SAFE "y")
set(CONFIG_GPTIMER_ENABLE_DEBUG_LOG "")
set(CONFIG_I2C_ISR_IRAM_SAFE "")
set(CONFIG_I2C_ENABLE_DEBUG_LOG "")
set(CONFIG_LEDC_CTRL_FUNC_IN_IRAM "")
set(CONFIG_SPI_MASTER_IN_IRAM "")
set(CONFIG_SPI_MASTER_ISR_IN_IRAM "y")
set(CONFIG_SPI_SLAVE_IN_IRAM "")
set(CONFIG_SPI_SLAVE_ISR_IN_IRAM "y")
set(CONFIG_TEMP_SENSOR_ENABLE_DEBUG_LOG "")
set(CONFIG_UART_ISR_IN_IRAM "")
set(CONFIG_ETH_ENABLED "y")
set(CONFIG_ETH_USE_SPI_ETHERNET "y")
set(CONFIG_ETH_SPI_ETHERNET_DM9051 "")
set(CONFIG_ETH_SPI_ETHERNET_W5500 "")
set(CONFIG_ETH_SPI_ETHERNET_KSZ8851SNL "")
set(CONFIG_ETH_USE_OPENETH "")
set(CONFIG_ETH_TRANSMIT_MUTEX "")
set(CONFIG_ESP_EVENT_LOOP_PROFILING "")
set(CONFIG_ESP_EVENT_POST_FROM_ISR "y")
set(CONFIG_ESP_EVENT_POST_FROM_IRAM_ISR "y")
set(CONFIG_ESP_GDBSTUB_ENABLED "y")
set(CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME "")
set(CONFIG_ESP_GDBSTUB_SUPPORT_TASKS "y")
set(CONFIG_ESP_GDBSTUB_MAX_TASKS "32")
set(CONFIG_ESPHID_TASK_SIZE_BT "2048")
set(CONFIG_ESPHID_TASK_SIZE_BLE "4096")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS "y")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH "")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH "")
set(CONFIG_ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT "")
set(CONFIG_HTTPD_MAX_REQ_HDR_LEN "512")
set(CONFIG_HTTPD_MAX_URI_LEN "512")
set(CONFIG_HTTPD_ERR_RESP_NO_DELAY "y")
set(CONFIG_HTTPD_PURGE_BUF_LEN "32")
set(CONFIG_HTTPD_LOG_PURGE_DATA "")
set(CONFIG_HTTPD_WS_SUPPORT "")
set(CONFIG_HTTPD_QUEUE_WORK_BLOCKING "")
set(CONFIG_ESP_HTTPS_OTA_DECRYPT_CB "")
set(CONFIG_ESP_HTTPS_OTA_ALLOW_HTTP "")
set(CONFIG_ESP_HTTPS_SERVER_ENABLE "")
set(CONFIG_ESP32C2_REV_MIN_1 "y")
set(CONFIG_ESP32C2_REV_MIN_1_1 "")
set(CONFIG_ESP32C2_REV_MIN_200 "")
set(CONFIG_ESP32C2_REV_MIN_FULL "100")
set(CONFIG_ESP_REV_MIN_FULL "100")
set(CONFIG_ESP32C2_REV_MAX_FULL "299")
set(CONFIG_ESP_REV_MAX_FULL "299")
set(CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL "0")
set(CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL "99")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_BT "y")
set(CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES "4")
set(CONFIG_ESP32C2_UNIVERSAL_MAC_ADDRESSES_TWO "")
set(CONFIG_ESP32C2_UNIVERSAL_MAC_ADDRESSES_FOUR "y")
set(CONFIG_ESP32C2_UNIVERSAL_MAC_ADDRESSES "4")
set(CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC "")
set(CONFIG_ESP_SLEEP_POWER_DOWN_FLASH "")
set(CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU "y")
set(CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND "y")
set(CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY "0")
set(CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION "")
set(CONFIG_ESP_SLEEP_DEBUG "")
set(CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS "y")
set(CONFIG_RTC_CLK_SRC_INT_RC "y")
set(CONFIG_RTC_CLK_SRC_EXT_OSC "")
set(CONFIG_RTC_CLK_SRC_INT_8MD256 "")
set(CONFIG_RTC_CLK_CAL_CYCLES "1024")
set(CONFIG_PERIPH_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_GDMA_CTRL_FUNC_IN_IRAM "y")
set(CONFIG_GDMA_ISR_IRAM_SAFE "")
set(CONFIG_GDMA_ENABLE_DEBUG_LOG "")
set(CONFIG_XTAL_FREQ_26 "")
set(CONFIG_XTAL_FREQ_40 "y")
set(CONFIG_XTAL_FREQ "40")
set(CONFIG_ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM "y")
set(CONFIG_LCD_ENABLE_DEBUG_LOG "")
set(CONFIG_ESP_NETIF_IP_LOST_TIMER_INTERVAL "120")
set(CONFIG_ESP_NETIF_TCPIP_LWIP "y")
set(CONFIG_ESP_NETIF_LOOPBACK "")
set(CONFIG_ESP_NETIF_USES_TCPIP_WITH_BSD_API "y")
set(CONFIG_ESP_NETIF_RECEIVE_REPORT_ERRORS "")
set(CONFIG_ESP_NETIF_L2_TAP "")
set(CONFIG_ESP_NETIF_BRIDGE_EN "")
set(CONFIG_ESP_NETIF_SET_DNS_PER_DEFAULT_NETIF "")
set(CONFIG_ESP_PHY_ENABLED "y")
set(CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE "y")
set(CONFIG_ESP_PHY_INIT_DATA_IN_PARTITION "")
set(CONFIG_ESP_PHY_MAX_WIFI_TX_POWER "20")
set(CONFIG_ESP_PHY_MAX_TX_POWER "20")
set(CONFIG_ESP_PHY_REDUCE_TX_POWER "")
set(CONFIG_ESP_PHY_ENABLE_USB "")
set(CONFIG_ESP_PHY_ENABLE_CERT_TEST "")
set(CONFIG_ESP_PHY_RF_CAL_PARTIAL "y")
set(CONFIG_ESP_PHY_RF_CAL_NONE "")
set(CONFIG_ESP_PHY_RF_CAL_FULL "")
set(CONFIG_ESP_PHY_CALIBRATION_MODE "0")
set(CONFIG_ESP_PHY_IMPROVE_RX_11B "")
set(CONFIG_ESP_PHY_PLL_TRACK_DEBUG "")
set(CONFIG_ESP_PHY_RECORD_USED_TIME "")
set(CONFIG_PM_ENABLE "")
set(CONFIG_PM_SLP_IRAM_OPT "")
set(CONFIG_RINGBUF_PLACE_FUNCTIONS_INTO_FLASH "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80 "")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_120 "y")
set(CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ "120")
set(CONFIG_ESP32C2_INSTRUCTION_CACHE_WRAP "")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT "")
set(CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT "y")
set(CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT "")
set(CONFIG_ESP_SYSTEM_PANIC_GDBSTUB "")
set(CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS "0")
set(CONFIG_ESP_SYSTEM_SINGLE_CORE_MODE "y")
set(CONFIG_ESP_SYSTEM_USE_EH_FRAME "")
set(CONFIG_ESP_SYSTEM_PMP_IDRAM_SPLIT "y")
set(CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_ESP_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0 "y")
set(CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY "")
set(CONFIG_ESP_MAIN_TASK_AFFINITY "0x0")
set(CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE "2048")
set(CONFIG_ESP_CONSOLE_UART_DEFAULT "y")
set(CONFIG_ESP_CONSOLE_UART_CUSTOM "")
set(CONFIG_ESP_CONSOLE_NONE "")
set(CONFIG_ESP_CONSOLE_UART "y")
set(CONFIG_ESP_CONSOLE_UART_NUM "0")
set(CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM "0")
set(CONFIG_ESP_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_ESP_INT_WDT "y")
set(CONFIG_ESP_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_ESP_TASK_WDT_EN "y")
set(CONFIG_ESP_TASK_WDT_USE_ESP_TIMER "y")
set(CONFIG_ESP_TASK_WDT_INIT "y")
set(CONFIG_ESP_TASK_WDT_PANIC "")
set(CONFIG_ESP_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_ESP_PANIC_HANDLER_IRAM "")
set(CONFIG_ESP_DEBUG_STUBS_ENABLE "")
set(CONFIG_ESP_DEBUG_OCDAWARE "y")
set(CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4 "y")
set(CONFIG_ESP_BROWNOUT_DET "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7 "y")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_ESP_BROWNOUT_DET_LVL "7")
set(CONFIG_ESP_SYSTEM_BROWNOUT_INTR "y")
set(CONFIG_ESP_SYSTEM_HW_STACK_GUARD "y")
set(CONFIG_ESP_SYSTEM_BBPLL_RECALIB "y")
set(CONFIG_ESP_SYSTEM_HW_PC_RECORD "y")
set(CONFIG_ESP_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_ESP_TIMER_PROFILING "")
set(CONFIG_ESP_TIME_FUNCS_USE_RTC_TIMER "y")
set(CONFIG_ESP_TIME_FUNCS_USE_ESP_TIMER "y")
set(CONFIG_ESP_TIMER_TASK_STACK_SIZE "3584")
set(CONFIG_ESP_TIMER_INTERRUPT_LEVEL "1")
set(CONFIG_ESP_TIMER_SHOW_EXPERIMENTAL "")
set(CONFIG_ESP_TIMER_TASK_AFFINITY "0x0")
set(CONFIG_ESP_TIMER_TASK_AFFINITY_CPU0 "y")
set(CONFIG_ESP_TIMER_ISR_AFFINITY_CPU0 "y")
set(CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD "y")
set(CONFIG_ESP_TIMER_IMPL_SYSTIMER "y")
set(CONFIG_ESP_WIFI_ENABLED "y")
set(CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM "10")
set(CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM "32")
set(CONFIG_ESP_WIFI_STATIC_TX_BUFFER "")
set(CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER "y")
set(CONFIG_ESP_WIFI_TX_BUFFER_TYPE "1")
set(CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM "32")
set(CONFIG_ESP_WIFI_STATIC_RX_MGMT_BUFFER "y")
set(CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER "")
set(CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUF "0")
set(CONFIG_ESP_WIFI_RX_MGMT_BUF_NUM_DEF "5")
set(CONFIG_ESP_WIFI_AMPDU_TX_ENABLED "y")
set(CONFIG_ESP_WIFI_TX_BA_WIN "6")
set(CONFIG_ESP_WIFI_AMPDU_RX_ENABLED "y")
set(CONFIG_ESP_WIFI_RX_BA_WIN "6")
set(CONFIG_ESP_WIFI_NVS_ENABLED "y")
set(CONFIG_ESP_WIFI_SOFTAP_BEACON_MAX_LEN "752")
set(CONFIG_ESP_WIFI_MGMT_SBUF_NUM "32")
set(CONFIG_ESP_WIFI_IRAM_OPT "y")
set(CONFIG_ESP_WIFI_EXTRA_IRAM_OPT "")
set(CONFIG_ESP_WIFI_RX_IRAM_OPT "y")
set(CONFIG_ESP_WIFI_ENABLE_WPA3_SAE "y")
set(CONFIG_ESP_WIFI_ENABLE_SAE_PK "y")
set(CONFIG_ESP_WIFI_SOFTAP_SAE_SUPPORT "y")
set(CONFIG_ESP_WIFI_ENABLE_WPA3_OWE_STA "y")
set(CONFIG_ESP_WIFI_SLP_IRAM_OPT "")
set(CONFIG_ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME "50")
set(CONFIG_ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME "10")
set(CONFIG_ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME "15")
set(CONFIG_ESP_WIFI_FTM_ENABLE "")
set(CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE "y")
set(CONFIG_ESP_WIFI_GMAC_SUPPORT "y")
set(CONFIG_ESP_WIFI_SOFTAP_SUPPORT "y")
set(CONFIG_ESP_WIFI_SLP_BEACON_LOST_OPT "")
set(CONFIG_ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM "2")
set(CONFIG_ESP_WIFI_MBEDTLS_CRYPTO "y")
set(CONFIG_ESP_WIFI_MBEDTLS_TLS_CLIENT "y")
set(CONFIG_ESP_WIFI_11KV_SUPPORT "")
set(CONFIG_ESP_WIFI_MBO_SUPPORT "")
set(CONFIG_ESP_WIFI_DPP_SUPPORT "")
set(CONFIG_ESP_WIFI_11R_SUPPORT "")
set(CONFIG_ESP_WIFI_WPS_SOFTAP_REGISTRAR "")
set(CONFIG_ESP_WIFI_WPS_STRICT "")
set(CONFIG_ESP_WIFI_WPS_PASSPHRASE "")
set(CONFIG_ESP_WIFI_DEBUG_PRINT "")
set(CONFIG_ESP_WIFI_TESTING_OPTIONS "")
set(CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT "y")
set(CONFIG_ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER "y")
set(CONFIG_ESP_COREDUMP_ENABLE_TO_FLASH "")
set(CONFIG_ESP_COREDUMP_ENABLE_TO_UART "")
set(CONFIG_ESP_COREDUMP_ENABLE_TO_NONE "y")
set(CONFIG_FATFS_VOLUME_COUNT "2")
set(CONFIG_FATFS_LFN_NONE "y")
set(CONFIG_FATFS_LFN_HEAP "")
set(CONFIG_FATFS_LFN_STACK "")
set(CONFIG_FATFS_SECTOR_512 "")
set(CONFIG_FATFS_SECTOR_4096 "y")
set(CONFIG_FATFS_CODEPAGE_DYNAMIC "")
set(CONFIG_FATFS_CODEPAGE_437 "y")
set(CONFIG_FATFS_CODEPAGE_720 "")
set(CONFIG_FATFS_CODEPAGE_737 "")
set(CONFIG_FATFS_CODEPAGE_771 "")
set(CONFIG_FATFS_CODEPAGE_775 "")
set(CONFIG_FATFS_CODEPAGE_850 "")
set(CONFIG_FATFS_CODEPAGE_852 "")
set(CONFIG_FATFS_CODEPAGE_855 "")
set(CONFIG_FATFS_CODEPAGE_857 "")
set(CONFIG_FATFS_CODEPAGE_860 "")
set(CONFIG_FATFS_CODEPAGE_861 "")
set(CONFIG_FATFS_CODEPAGE_862 "")
set(CONFIG_FATFS_CODEPAGE_863 "")
set(CONFIG_FATFS_CODEPAGE_864 "")
set(CONFIG_FATFS_CODEPAGE_865 "")
set(CONFIG_FATFS_CODEPAGE_866 "")
set(CONFIG_FATFS_CODEPAGE_869 "")
set(CONFIG_FATFS_CODEPAGE_932 "")
set(CONFIG_FATFS_CODEPAGE_936 "")
set(CONFIG_FATFS_CODEPAGE_949 "")
set(CONFIG_FATFS_CODEPAGE_950 "")
set(CONFIG_FATFS_CODEPAGE "437")
set(CONFIG_FATFS_FS_LOCK "0")
set(CONFIG_FATFS_TIMEOUT_MS "10000")
set(CONFIG_FATFS_PER_FILE_CACHE "y")
set(CONFIG_FATFS_USE_FASTSEEK "")
set(CONFIG_FATFS_VFS_FSTAT_BLKSIZE "0")
set(CONFIG_FATFS_IMMEDIATE_FSYNC "")
set(CONFIG_FATFS_USE_LABEL "")
set(CONFIG_FATFS_LINK_LOCK "y")
set(CONFIG_FREERTOS_SMP "")
set(CONFIG_FREERTOS_UNICORE "y")
set(CONFIG_FREERTOS_HZ "100")
set(CONFIG_FREERTOS_OPTIMIZED_SCHEDULER "y")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL "")
set(CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY "y")
set(CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS "1")
set(CONFIG_FREERTOS_IDLE_TASK_STACKSIZE "1536")
set(CONFIG_FREERTOS_USE_IDLE_HOOK "")
set(CONFIG_FREERTOS_USE_TICK_HOOK "")
set(CONFIG_FREERTOS_MAX_TASK_NAME_LEN "16")
set(CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY "")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME "Tmr Svc")
set(CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0 "")
set(CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY "y")
set(CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_TIMER_TASK_PRIORITY "1")
set(CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_FREERTOS_TIMER_QUEUE_LENGTH "10")
set(CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE "0")
set(CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES "1")
set(CONFIG_FREERTOS_USE_TRACE_FACILITY "")
set(CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES "")
set(CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS "")
set(CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG "")
set(CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER "y")
set(CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK "")
set(CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS "y")
set(CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK "")
set(CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP "")
set(CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER "y")
set(CONFIG_FREERTOS_ISR_STACKSIZE "1536")
set(CONFIG_FREERTOS_INTERRUPT_BACKTRACE "y")
set(CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER "y")
set(CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1 "y")
set(CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL3 "")
set(CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER "y")
set(CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH "")
set(CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE "")
set(CONFIG_FREERTOS_PORT "y")
set(CONFIG_FREERTOS_NO_AFFINITY "0x7fffffff")
set(CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION "y")
set(CONFIG_FREERTOS_DEBUG_OCDAWARE "y")
set(CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT "y")
set(CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH "y")
set(CONFIG_FREERTOS_NUMBER_OF_CORES "1")
set(CONFIG_HAL_ASSERTION_EQUALS_SYSTEM "y")
set(CONFIG_HAL_ASSERTION_DISABLE "")
set(CONFIG_HAL_ASSERTION_SILENT "")
set(CONFIG_HAL_ASSERTION_ENABLE "")
set(CONFIG_HAL_DEFAULT_ASSERTION_LEVEL "2")
set(CONFIG_HAL_SYSTIMER_USE_ROM_IMPL "y")
set(CONFIG_HAL_WDT_USE_ROM_IMPL "y")
set(CONFIG_HAL_SPI_MASTER_FUNC_IN_IRAM "y")
set(CONFIG_HAL_SPI_SLAVE_FUNC_IN_IRAM "y")
set(CONFIG_HEAP_POISONING_DISABLED "y")
set(CONFIG_HEAP_POISONING_LIGHT "")
set(CONFIG_HEAP_POISONING_COMPREHENSIVE "")
set(CONFIG_HEAP_TRACING_OFF "y")
set(CONFIG_HEAP_TRACING_STANDALONE "")
set(CONFIG_HEAP_TRACING_TOHOST "")
set(CONFIG_HEAP_USE_HOOKS "")
set(CONFIG_HEAP_TASK_TRACKING "")
set(CONFIG_HEAP_ABORT_WHEN_ALLOCATION_FAILS "")
set(CONFIG_HEAP_TLSF_USE_ROM_IMPL "y")
set(CONFIG_LOG_DEFAULT_LEVEL_NONE "")
set(CONFIG_LOG_DEFAULT_LEVEL_ERROR "")
set(CONFIG_LOG_DEFAULT_LEVEL_WARN "")
set(CONFIG_LOG_DEFAULT_LEVEL_INFO "y")
set(CONFIG_LOG_DEFAULT_LEVEL_DEBUG "")
set(CONFIG_LOG_DEFAULT_LEVEL_VERBOSE "")
set(CONFIG_LOG_DEFAULT_LEVEL "3")
set(CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT "y")
set(CONFIG_LOG_MAXIMUM_LEVEL_DEBUG "")
set(CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE "")
set(CONFIG_LOG_MAXIMUM_LEVEL "3")
set(CONFIG_LOG_MASTER_LEVEL "")
set(CONFIG_LOG_COLORS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_RTOS "y")
set(CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM "")
set(CONFIG_LWIP_ENABLE "y")
set(CONFIG_LWIP_LOCAL_HOSTNAME "espressif")
set(CONFIG_LWIP_NETIF_API "")
set(CONFIG_LWIP_TCPIP_TASK_PRIO "18")
set(CONFIG_LWIP_TCPIP_CORE_LOCKING "")
set(CONFIG_LWIP_CHECK_THREAD_SAFETY "")
set(CONFIG_LWIP_DNS_SUPPORT_MDNS_QUERIES "y")
set(CONFIG_LWIP_L2_TO_L3_COPY "")
set(CONFIG_LWIP_IRAM_OPTIMIZATION "")
set(CONFIG_LWIP_EXTRA_IRAM_OPTIMIZATION "")
set(CONFIG_LWIP_TIMERS_ONDEMAND "y")
set(CONFIG_LWIP_ND6 "y")
set(CONFIG_LWIP_FORCE_ROUTER_FORWARDING "")
set(CONFIG_LWIP_MAX_SOCKETS "10")
set(CONFIG_LWIP_USE_ONLY_LWIP_SELECT "")
set(CONFIG_LWIP_SO_LINGER "")
set(CONFIG_LWIP_SO_REUSE "y")
set(CONFIG_LWIP_SO_REUSE_RXTOALL "y")
set(CONFIG_LWIP_SO_RCVBUF "")
set(CONFIG_LWIP_NETBUF_RECVINFO "")
set(CONFIG_LWIP_IP_DEFAULT_TTL "64")
set(CONFIG_LWIP_IP4_FRAG "y")
set(CONFIG_LWIP_IP6_FRAG "y")
set(CONFIG_LWIP_IP4_REASSEMBLY "")
set(CONFIG_LWIP_IP6_REASSEMBLY "")
set(CONFIG_LWIP_IP_REASS_MAX_PBUFS "10")
set(CONFIG_LWIP_IP_FORWARD "")
set(CONFIG_LWIP_STATS "")
set(CONFIG_LWIP_ESP_GRATUITOUS_ARP "y")
set(CONFIG_LWIP_GARP_TMR_INTERVAL "60")
set(CONFIG_LWIP_ESP_MLDV6_REPORT "y")
set(CONFIG_LWIP_MLDV6_TMR_INTERVAL "40")
set(CONFIG_LWIP_TCPIP_RECVMBOX_SIZE "32")
set(CONFIG_LWIP_DHCP_DOES_ARP_CHECK "y")
set(CONFIG_LWIP_DHCP_DISABLE_CLIENT_ID "")
set(CONFIG_LWIP_DHCP_DISABLE_VENDOR_CLASS_ID "y")
set(CONFIG_LWIP_DHCP_RESTORE_LAST_IP "")
set(CONFIG_LWIP_DHCP_OPTIONS_LEN "68")
set(CONFIG_LWIP_NUM_NETIF_CLIENT_DATA "0")
set(CONFIG_LWIP_DHCP_COARSE_TIMER_SECS "1")
set(CONFIG_LWIP_DHCPS "y")
set(CONFIG_LWIP_DHCPS_LEASE_UNIT "60")
set(CONFIG_LWIP_DHCPS_MAX_STATION_NUM "8")
set(CONFIG_LWIP_DHCPS_STATIC_ENTRIES "y")
set(CONFIG_LWIP_AUTOIP "")
set(CONFIG_LWIP_IPV4 "y")
set(CONFIG_LWIP_IPV6 "y")
set(CONFIG_LWIP_IPV6_AUTOCONFIG "")
set(CONFIG_LWIP_IPV6_NUM_ADDRESSES "3")
set(CONFIG_LWIP_IPV6_FORWARD "")
set(CONFIG_LWIP_NETIF_STATUS_CALLBACK "")
set(CONFIG_LWIP_NETIF_LOOPBACK "y")
set(CONFIG_LWIP_LOOPBACK_MAX_PBUFS "8")
set(CONFIG_LWIP_MAX_ACTIVE_TCP "16")
set(CONFIG_LWIP_MAX_LISTENING_TCP "16")
set(CONFIG_LWIP_TCP_HIGH_SPEED_RETRANSMISSION "y")
set(CONFIG_LWIP_TCP_MAXRTX "12")
set(CONFIG_LWIP_TCP_SYNMAXRTX "12")
set(CONFIG_LWIP_TCP_MSS "1440")
set(CONFIG_LWIP_TCP_TMR_INTERVAL "250")
set(CONFIG_LWIP_TCP_MSL "60000")
set(CONFIG_LWIP_TCP_FIN_WAIT_TIMEOUT "20000")
set(CONFIG_LWIP_TCP_SND_BUF_DEFAULT "5760")
set(CONFIG_LWIP_TCP_WND_DEFAULT "5760")
set(CONFIG_LWIP_TCP_RECVMBOX_SIZE "6")
set(CONFIG_LWIP_TCP_ACCEPTMBOX_SIZE "6")
set(CONFIG_LWIP_TCP_QUEUE_OOSEQ "y")
set(CONFIG_LWIP_TCP_OOSEQ_TIMEOUT "6")
set(CONFIG_LWIP_TCP_OOSEQ_MAX_PBUFS "4")
set(CONFIG_LWIP_TCP_SACK_OUT "")
set(CONFIG_LWIP_TCP_OVERSIZE_MSS "y")
set(CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS "")
set(CONFIG_LWIP_TCP_OVERSIZE_DISABLE "")
set(CONFIG_LWIP_TCP_RTO_TIME "1500")
set(CONFIG_LWIP_MAX_UDP_PCBS "16")
set(CONFIG_LWIP_UDP_RECVMBOX_SIZE "6")
set(CONFIG_LWIP_CHECKSUM_CHECK_IP "")
set(CONFIG_LWIP_CHECKSUM_CHECK_UDP "")
set(CONFIG_LWIP_CHECKSUM_CHECK_ICMP "y")
set(CONFIG_LWIP_TCPIP_TASK_STACK_SIZE "3072")
set(CONFIG_LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY "y")
set(CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU0 "")
set(CONFIG_LWIP_TCPIP_TASK_AFFINITY "0x7fffffff")
set(CONFIG_LWIP_IPV6_ND6_NUM_PREFIXES "5")
set(CONFIG_LWIP_IPV6_ND6_NUM_ROUTERS "3")
set(CONFIG_LWIP_IPV6_ND6_NUM_DESTINATIONS "10")
set(CONFIG_LWIP_PPP_SUPPORT "")
set(CONFIG_LWIP_IPV6_MEMP_NUM_ND6_QUEUE "3")
set(CONFIG_LWIP_IPV6_ND6_NUM_NEIGHBORS "5")
set(CONFIG_LWIP_SLIP_SUPPORT "")
set(CONFIG_LWIP_ICMP "y")
set(CONFIG_LWIP_MULTICAST_PING "")
set(CONFIG_LWIP_BROADCAST_PING "")
set(CONFIG_LWIP_MAX_RAW_PCBS "16")
set(CONFIG_LWIP_SNTP_MAX_SERVERS "1")
set(CONFIG_LWIP_DHCP_GET_NTP_SRV "")
set(CONFIG_LWIP_SNTP_UPDATE_DELAY "3600000")
set(CONFIG_LWIP_SNTP_STARTUP_DELAY "y")
set(CONFIG_LWIP_SNTP_MAXIMUM_STARTUP_DELAY "5000")
set(CONFIG_LWIP_DNS_MAX_HOST_IP "1")
set(CONFIG_LWIP_DNS_MAX_SERVERS "3")
set(CONFIG_LWIP_FALLBACK_DNS_SERVER_SUPPORT "")
set(CONFIG_LWIP_DNS_SETSERVER_WITH_NETIF "")
set(CONFIG_LWIP_BRIDGEIF_MAX_PORTS "7")
set(CONFIG_LWIP_ESP_LWIP_ASSERT "y")
set(CONFIG_LWIP_HOOK_TCP_ISN_NONE "")
set(CONFIG_LWIP_HOOK_TCP_ISN_DEFAULT "y")
set(CONFIG_LWIP_HOOK_TCP_ISN_CUSTOM "")
set(CONFIG_LWIP_HOOK_IP6_ROUTE_NONE "y")
set(CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT "")
set(CONFIG_LWIP_HOOK_IP6_ROUTE_CUSTOM "")
set(CONFIG_LWIP_HOOK_ND6_GET_GW_NONE "y")
set(CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT "")
set(CONFIG_LWIP_HOOK_ND6_GET_GW_CUSTOM "")
set(CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE "y")
set(CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT "")
set(CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM "")
set(CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE "y")
set(CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT "")
set(CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM "")
set(CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_NONE "y")
set(CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_CUSTOM "")
set(CONFIG_LWIP_HOOK_IP6_INPUT_NONE "")
set(CONFIG_LWIP_HOOK_IP6_INPUT_DEFAULT "y")
set(CONFIG_LWIP_HOOK_IP6_INPUT_CUSTOM "")
set(CONFIG_LWIP_DEBUG "")
set(CONFIG_MBEDTLS_INTERNAL_MEM_ALLOC "y")
set(CONFIG_MBEDTLS_DEFAULT_MEM_ALLOC "")
set(CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC "")
set(CONFIG_MBEDTLS_ASYMMETRIC_CONTENT_LEN "y")
set(CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN "16384")
set(CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN "4096")
set(CONFIG_MBEDTLS_DYNAMIC_BUFFER "")
set(CONFIG_MBEDTLS_DEBUG "")
set(CONFIG_MBEDTLS_SSL_PROTO_TLS1_3 "")
set(CONFIG_MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH "")
set(CONFIG_MBEDTLS_X509_TRUSTED_CERT_CALLBACK "")
set(CONFIG_MBEDTLS_SSL_CONTEXT_SERIALIZATION "")
set(CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE "y")
set(CONFIG_MBEDTLS_PKCS7_C "y")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE "y")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL "y")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN "")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE "")
set(CONFIG_MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE "")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST "")
set(CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS "200")
set(CONFIG_MBEDTLS_ECP_RESTARTABLE "")
set(CONFIG_MBEDTLS_CMAC_C "y")
set(CONFIG_MBEDTLS_HARDWARE_SHA "y")
set(CONFIG_MBEDTLS_HARDWARE_ECC "y")
set(CONFIG_MBEDTLS_ECC_OTHER_CURVES_SOFT_FALLBACK "y")
set(CONFIG_MBEDTLS_ROM_MD5 "y")
set(CONFIG_MBEDTLS_ATCA_HW_ECDSA_SIGN "")
set(CONFIG_MBEDTLS_ATCA_HW_ECDSA_VERIFY "")
set(CONFIG_MBEDTLS_HAVE_TIME "y")
set(CONFIG_MBEDTLS_PLATFORM_TIME_ALT "")
set(CONFIG_MBEDTLS_HAVE_TIME_DATE "")
set(CONFIG_MBEDTLS_ECDSA_DETERMINISTIC "y")
set(CONFIG_MBEDTLS_SHA512_C "y")
set(CONFIG_MBEDTLS_SHA3_C "")
set(CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT "y")
set(CONFIG_MBEDTLS_TLS_SERVER_ONLY "")
set(CONFIG_MBEDTLS_TLS_CLIENT_ONLY "")
set(CONFIG_MBEDTLS_TLS_DISABLED "")
set(CONFIG_MBEDTLS_TLS_SERVER "y")
set(CONFIG_MBEDTLS_TLS_CLIENT "y")
set(CONFIG_MBEDTLS_TLS_ENABLED "y")
set(CONFIG_MBEDTLS_PSK_MODES "")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_RSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA "y")
set(CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA "y")
set(CONFIG_MBEDTLS_SSL_RENEGOTIATION "y")
set(CONFIG_MBEDTLS_SSL_PROTO_TLS1_2 "y")
set(CONFIG_MBEDTLS_SSL_PROTO_GMTSSL1_1 "")
set(CONFIG_MBEDTLS_SSL_PROTO_DTLS "")
set(CONFIG_MBEDTLS_SSL_ALPN "y")
set(CONFIG_MBEDTLS_CLIENT_SSL_SESSION_TICKETS "y")
set(CONFIG_MBEDTLS_SERVER_SSL_SESSION_TICKETS "y")
set(CONFIG_MBEDTLS_AES_C "y")
set(CONFIG_MBEDTLS_CAMELLIA_C "")
set(CONFIG_MBEDTLS_DES_C "")
set(CONFIG_MBEDTLS_BLOWFISH_C "")
set(CONFIG_MBEDTLS_XTEA_C "")
set(CONFIG_MBEDTLS_CCM_C "y")
set(CONFIG_MBEDTLS_GCM_C "y")
set(CONFIG_MBEDTLS_NIST_KW_C "")
set(CONFIG_MBEDTLS_RIPEMD160_C "")
set(CONFIG_MBEDTLS_PEM_PARSE_C "y")
set(CONFIG_MBEDTLS_PEM_WRITE_C "y")
set(CONFIG_MBEDTLS_X509_CRL_PARSE_C "y")
set(CONFIG_MBEDTLS_X509_CSR_PARSE_C "y")
set(CONFIG_MBEDTLS_ECP_C "y")
set(CONFIG_MBEDTLS_DHM_C "")
set(CONFIG_MBEDTLS_ECDH_C "y")
set(CONFIG_MBEDTLS_ECDSA_C "y")
set(CONFIG_MBEDTLS_ECJPAKE_C "")
set(CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED "y")
set(CONFIG_MBEDTLS_ECP_NIST_OPTIM "y")
set(CONFIG_MBEDTLS_ECP_FIXED_POINT_OPTIM "")
set(CONFIG_MBEDTLS_POLY1305_C "")
set(CONFIG_MBEDTLS_CHACHA20_C "")
set(CONFIG_MBEDTLS_HKDF_C "")
set(CONFIG_MBEDTLS_THREADING_C "")
set(CONFIG_MBEDTLS_ERROR_STRINGS "y")
set(CONFIG_MBEDTLS_USE_CRYPTO_ROM_IMPL "")
set(CONFIG_MBEDTLS_FS_IO "y")
set(CONFIG_MQTT_PROTOCOL_311 "y")
set(CONFIG_MQTT_PROTOCOL_5 "")
set(CONFIG_MQTT_TRANSPORT_SSL "y")
set(CONFIG_MQTT_TRANSPORT_WEBSOCKET "y")
set(CONFIG_MQTT_TRANSPORT_WEBSOCKET_SECURE "y")
set(CONFIG_MQTT_MSG_ID_INCREMENTAL "")
set(CONFIG_MQTT_SKIP_PUBLISH_IF_DISCONNECTED "")
set(CONFIG_MQTT_REPORT_DELETED_MESSAGES "")
set(CONFIG_MQTT_USE_CUSTOM_CONFIG "")
set(CONFIG_MQTT_TASK_CORE_SELECTION_ENABLED "")
set(CONFIG_MQTT_CUSTOM_OUTBOX "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF "y")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_LF "")
set(CONFIG_NEWLIB_STDIN_LINE_ENDING_CR "y")
set(CONFIG_NEWLIB_NANO_FORMAT "y")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT "y")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT "")
set(CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE "")
set(CONFIG_NVS_ASSERT_ERROR_CHECK "")
set(CONFIG_NVS_LEGACY_DUP_KEYS_COMPATIBILITY "")
set(CONFIG_OPENTHREAD_ENABLED "")
set(CONFIG_OPENTHREAD_SPINEL_ONLY "")
set(CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0 "y")
set(CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1 "y")
set(CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2 "y")
set(CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_PATCH_VERSION "y")
set(CONFIG_PTHREAD_TASK_PRIO_DEFAULT "5")
set(CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT "3072")
set(CONFIG_PTHREAD_STACK_MIN "768")
set(CONFIG_PTHREAD_TASK_CORE_DEFAULT "-1")
set(CONFIG_PTHREAD_TASK_NAME_DEFAULT "pthread")
set(CONFIG_MMU_PAGE_SIZE_32KB "y")
set(CONFIG_MMU_PAGE_MODE "32KB")
set(CONFIG_MMU_PAGE_SIZE "0x8000")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC "y")
set(CONFIG_SPI_FLASH_BROWNOUT_RESET "y")
set(CONFIG_SPI_FLASH_SUSPEND_QVL_SUPPORTED "y")
set(CONFIG_SPI_FLASH_AUTO_SUSPEND "")
set(CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US "50")
set(CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND "")
set(CONFIG_SPI_FLASH_VERIFY_WRITE "")
set(CONFIG_SPI_FLASH_ENABLE_COUNTERS "")
set(CONFIG_SPI_FLASH_ROM_DRIVER_PATCH "y")
set(CONFIG_SPI_FLASH_ROM_IMPL "")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS "y")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS "")
set(CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED "")
set(CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE "")
set(CONFIG_SPI_FLASH_YIELD_DURING_ERASE "y")
set(CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS "20")
set(CONFIG_SPI_FLASH_ERASE_YIELD_TICKS "1")
set(CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE "8192")
set(CONFIG_SPI_FLASH_SIZE_OVERRIDE "")
set(CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED "")
set(CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST "")
set(CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_BOYA_SUPPORTED "y")
set(CONFIG_SPI_FLASH_VENDOR_TH_SUPPORTED "y")
set(CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_GD_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP "y")
set(CONFIG_SPI_FLASH_SUPPORT_TH_CHIP "y")
set(CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE "y")
set(CONFIG_SPIFFS_MAX_PARTITIONS "3")
set(CONFIG_SPIFFS_CACHE "y")
set(CONFIG_SPIFFS_CACHE_WR "y")
set(CONFIG_SPIFFS_CACHE_STATS "")
set(CONFIG_SPIFFS_PAGE_CHECK "y")
set(CONFIG_SPIFFS_GC_MAX_RUNS "10")
set(CONFIG_SPIFFS_GC_STATS "")
set(CONFIG_SPIFFS_PAGE_SIZE "256")
set(CONFIG_SPIFFS_OBJ_NAME_LEN "32")
set(CONFIG_SPIFFS_FOLLOW_SYMLINKS "")
set(CONFIG_SPIFFS_USE_MAGIC "y")
set(CONFIG_SPIFFS_USE_MAGIC_LENGTH "y")
set(CONFIG_SPIFFS_META_LENGTH "4")
set(CONFIG_SPIFFS_USE_MTIME "y")
set(CONFIG_SPIFFS_DBG "")
set(CONFIG_SPIFFS_API_DBG "")
set(CONFIG_SPIFFS_GC_DBG "")
set(CONFIG_SPIFFS_CACHE_DBG "")
set(CONFIG_SPIFFS_CHECK_DBG "")
set(CONFIG_SPIFFS_TEST_VISUALISATION "")
set(CONFIG_WS_TRANSPORT "y")
set(CONFIG_WS_BUFFER_SIZE "1024")
set(CONFIG_WS_DYNAMIC_BUFFER "")
set(CONFIG_UNITY_ENABLE_FLOAT "y")
set(CONFIG_UNITY_ENABLE_DOUBLE "y")
set(CONFIG_UNITY_ENABLE_64BIT "")
set(CONFIG_UNITY_ENABLE_COLOR "")
set(CONFIG_UNITY_ENABLE_IDF_TEST_RUNNER "y")
set(CONFIG_UNITY_ENABLE_FIXTURE "")
set(CONFIG_UNITY_ENABLE_BACKTRACE_ON_FAIL "")
set(CONFIG_VFS_SUPPORT_IO "y")
set(CONFIG_VFS_SUPPORT_DIR "y")
set(CONFIG_VFS_SUPPORT_SELECT "y")
set(CONFIG_VFS_SUPPRESS_SELECT_DEBUG_OUTPUT "y")
set(CONFIG_VFS_SELECT_IN_RAM "")
set(CONFIG_VFS_SUPPORT_TERMIOS "y")
set(CONFIG_VFS_MAX_COUNT "8")
set(CONFIG_VFS_SEMIHOSTFS_MAX_MOUNT_POINTS "1")
set(CONFIG_WL_SECTOR_SIZE_512 "")
set(CONFIG_WL_SECTOR_SIZE_4096 "y")
set(CONFIG_WL_SECTOR_SIZE "4096")
set(CONFIG_WIFI_PROV_SCAN_MAX_ENTRIES "16")
set(CONFIG_WIFI_PROV_AUTOSTOP_TIMEOUT "30")
set(CONFIG_WIFI_PROV_BLE_BONDING "")
set(CONFIG_WIFI_PROV_BLE_SEC_CONN "y")
set(CONFIG_WIFI_PROV_BLE_FORCE_ENCRYPTION "")
set(CONFIG_WIFI_PROV_BLE_NOTIFY "")
set(CONFIG_WIFI_PROV_KEEP_BLE_ON_AFTER_PROV "")
set(CONFIG_WIFI_PROV_STA_ALL_CHANNEL_SCAN "y")
set(CONFIG_WIFI_PROV_STA_FAST_SCAN "")
set(CONFIG_IDF_EXPERIMENTAL_FEATURES "")
set(CONFIGS_LIST CONFIG_SOC_ADC_SUPPORTED;CONFIG_SOC_DEDICATED_GPIO_SUPPORTED;CONFIG_SOC_UART_SUPPORTED;CONFIG_SOC_GDMA_SUPPORTED;CONFIG_SOC_AHB_GDMA_SUPPORTED;CONFIG_SOC_GPTIMER_SUPPORTED;CONFIG_SOC_PHY_SUPPORTED;CONFIG_SOC_BT_SUPPORTED;CONFIG_SOC_WIFI_SUPPORTED;CONFIG_SOC_ASYNC_MEMCPY_SUPPORTED;CONFIG_SOC_SUPPORTS_SECURE_DL_MODE;CONFIG_SOC_EFUSE_CONSISTS_OF_ONE_KEY_BLOCK;CONFIG_SOC_EFUSE_SUPPORTED;CONFIG_SOC_TEMP_SENSOR_SUPPORTED;CONFIG_SOC_LEDC_SUPPORTED;CONFIG_SOC_I2C_SUPPORTED;CONFIG_SOC_GPSPI_SUPPORTED;CONFIG_SOC_SHA_SUPPORTED;CONFIG_SOC_ECC_SUPPORTED;CONFIG_SOC_FLASH_ENC_SUPPORTED;CONFIG_SOC_SECURE_BOOT_SUPPORTED;CONFIG_SOC_SYSTIMER_SUPPORTED;CONFIG_SOC_BOD_SUPPORTED;CONFIG_SOC_CLK_TREE_SUPPORTED;CONFIG_SOC_ASSIST_DEBUG_SUPPORTED;CONFIG_SOC_WDT_SUPPORTED;CONFIG_SOC_SPI_FLASH_SUPPORTED;CONFIG_SOC_RNG_SUPPORTED;CONFIG_SOC_LIGHT_SLEEP_SUPPORTED;CONFIG_SOC_DEEP_SLEEP_SUPPORTED;CONFIG_SOC_LP_PERIPH_SHARE_INTERRUPT;CONFIG_SOC_PM_SUPPORTED;CONFIG_SOC_XTAL_SUPPORT_26M;CONFIG_SOC_XTAL_SUPPORT_40M;CONFIG_SOC_ADC_DIG_CTRL_SUPPORTED;CONFIG_SOC_ADC_DIG_IIR_FILTER_SUPPORTED;CONFIG_SOC_ADC_MONITOR_SUPPORTED;CONFIG_SOC_ADC_PERIPH_NUM;CONFIG_SOC_ADC_MAX_CHANNEL_NUM;CONFIG_SOC_ADC_ATTEN_NUM;CONFIG_SOC_ADC_DIGI_CONTROLLER_NUM;CONFIG_SOC_ADC_PATT_LEN_MAX;CONFIG_SOC_ADC_DIGI_MIN_BITWIDTH;CONFIG_SOC_ADC_DIGI_MAX_BITWIDTH;CONFIG_SOC_ADC_DIGI_IIR_FILTER_NUM;CONFIG_SOC_ADC_DIGI_MONITOR_NUM;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_HIGH;CONFIG_SOC_ADC_SAMPLE_FREQ_THRES_LOW;CONFIG_SOC_ADC_RTC_MIN_BITWIDTH;CONFIG_SOC_ADC_RTC_MAX_BITWIDTH;CONFIG_SOC_ADC_CALIBRATION_V1_SUPPORTED;CONFIG_SOC_ADC_SELF_HW_CALI_SUPPORTED;CONFIG_SOC_ADC_SHARED_POWER;CONFIG_SOC_BROWNOUT_RESET_SUPPORTED;CONFIG_SOC_SHARED_IDCACHE_SUPPORTED;CONFIG_SOC_CPU_CORES_NUM;CONFIG_SOC_CPU_INTR_NUM;CONFIG_SOC_CPU_HAS_FLEXIBLE_INTC;CONFIG_SOC_CPU_HAS_CSR_PC;CONFIG_SOC_CPU_BREAKPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINTS_NUM;CONFIG_SOC_CPU_WATCHPOINT_MAX_REGION_SIZE;CONFIG_SOC_CPU_IDRAM_SPLIT_USING_PMP;CONFIG_SOC_ECC_SUPPORT_POINT_VERIFY_QUIRK;CONFIG_SOC_AHB_GDMA_VERSION;CONFIG_SOC_GDMA_NUM_GROUPS_MAX;CONFIG_SOC_GDMA_PAIRS_PER_GROUP_MAX;CONFIG_SOC_GPIO_PORT;CONFIG_SOC_GPIO_PIN_COUNT;CONFIG_SOC_GPIO_SUPPORT_PIN_GLITCH_FILTER;CONFIG_SOC_GPIO_FILTER_CLK_SUPPORT_APB;CONFIG_SOC_GPIO_SUPPORT_FORCE_HOLD;CONFIG_SOC_GPIO_SUPPORT_DEEPSLEEP_WAKEUP;CONFIG_SOC_GPIO_IN_RANGE_MAX;CONFIG_SOC_GPIO_OUT_RANGE_MAX;CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_VALID_GPIO_MASK;CONFIG_SOC_GPIO_DEEP_SLEEP_WAKE_SUPPORTED_PIN_CNT;CONFIG_SOC_GPIO_VALID_DIGITAL_IO_PAD_MASK;CONFIG_SOC_GPIO_CLOCKOUT_BY_GPIO_MATRIX;CONFIG_SOC_GPIO_CLOCKOUT_CHANNEL_NUM;CONFIG_SOC_GPIO_SUPPORT_HOLD_IO_IN_DSLP;CONFIG_SOC_DEDIC_GPIO_OUT_CHANNELS_NUM;CONFIG_SOC_DEDIC_GPIO_IN_CHANNELS_NUM;CONFIG_SOC_DEDIC_PERIPH_ALWAYS_ENABLE;CONFIG_SOC_I2C_NUM;CONFIG_SOC_HP_I2C_NUM;CONFIG_SOC_I2C_FIFO_LEN;CONFIG_SOC_I2C_CMD_REG_NUM;CONFIG_SOC_I2C_SUPPORT_HW_CLR_BUS;CONFIG_SOC_I2C_SUPPORT_XTAL;CONFIG_SOC_I2C_SUPPORT_RTC;CONFIG_SOC_I2C_SUPPORT_10BIT_ADDR;CONFIG_SOC_LEDC_SUPPORT_PLL_DIV_CLOCK;CONFIG_SOC_LEDC_SUPPORT_XTAL_CLOCK;CONFIG_SOC_LEDC_CHANNEL_NUM;CONFIG_SOC_LEDC_TIMER_BIT_WIDTH;CONFIG_SOC_LEDC_SUPPORT_FADE_STOP;CONFIG_SOC_MMU_PAGE_SIZE_CONFIGURABLE;CONFIG_SOC_MMU_LINEAR_ADDRESS_REGION_NUM;CONFIG_SOC_MMU_PERIPH_NUM;CONFIG_SOC_MPU_MIN_REGION_SIZE;CONFIG_SOC_MPU_REGIONS_MAX_NUM;CONFIG_SOC_RTC_CNTL_CPU_PD_DMA_BUS_WIDTH;CONFIG_SOC_RTC_CNTL_CPU_PD_REG_FILE_NUM;CONFIG_SOC_RTCIO_PIN_COUNT;CONFIG_SOC_RSA_MAX_BIT_LEN;CONFIG_SOC_SHA_SUPPORT_RESUME;CONFIG_SOC_SHA_SUPPORT_SHA1;CONFIG_SOC_SHA_SUPPORT_SHA224;CONFIG_SOC_SHA_SUPPORT_SHA256;CONFIG_SOC_SPI_PERIPH_NUM;CONFIG_SOC_SPI_MAX_CS_NUM;CONFIG_SOC_SPI_MAXIMUM_BUFFER_SIZE;CONFIG_SOC_SPI_SUPPORT_DDRCLK;CONFIG_SOC_SPI_SLAVE_SUPPORT_SEG_TRANS;CONFIG_SOC_SPI_SUPPORT_CD_SIG;CONFIG_SOC_SPI_SUPPORT_CONTINUOUS_TRANS;CONFIG_SOC_SPI_SUPPORT_SLAVE_HD_VER2;CONFIG_SOC_SPI_SUPPORT_CLK_XTAL;CONFIG_SOC_SPI_SUPPORT_CLK_PLL_F40M;CONFIG_SOC_SPI_PERIPH_SUPPORT_CONTROL_DUMMY_OUT;CONFIG_SOC_SPI_SCT_SUPPORTED;CONFIG_SOC_SPI_SCT_REG_NUM;CONFIG_SOC_SPI_SCT_BUFFER_NUM_MAX;CONFIG_SOC_SPI_SCT_CONF_BITLEN_MAX;CONFIG_SOC_MEMSPI_IS_INDEPENDENT;CONFIG_SOC_SPI_MAX_PRE_DIVIDER;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_WAIT_IDLE;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_SUSPEND;CONFIG_SOC_SPI_MEM_SUPPORT_AUTO_RESUME;CONFIG_SOC_SPI_MEM_SUPPORT_IDLE_INTR;CONFIG_SOC_SPI_MEM_SUPPORT_SW_SUSPEND;CONFIG_SOC_SPI_MEM_SUPPORT_CHECK_SUS;CONFIG_SOC_SPI_MEM_SUPPORT_WRAP;CONFIG_SOC_MEMSPI_SRC_FREQ_60M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_30M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_20M_SUPPORTED;CONFIG_SOC_MEMSPI_SRC_FREQ_15M_SUPPORTED;CONFIG_SOC_SYSTIMER_COUNTER_NUM;CONFIG_SOC_SYSTIMER_ALARM_NUM;CONFIG_SOC_SYSTIMER_BIT_WIDTH_LO;CONFIG_SOC_SYSTIMER_BIT_WIDTH_HI;CONFIG_SOC_SYSTIMER_FIXED_DIVIDER;CONFIG_SOC_SYSTIMER_INT_LEVEL;CONFIG_SOC_SYSTIMER_ALARM_MISS_COMPENSATE;CONFIG_SOC_TIMER_GROUPS;CONFIG_SOC_TIMER_GROUP_TIMERS_PER_GROUP;CONFIG_SOC_TIMER_GROUP_COUNTER_BIT_WIDTH;CONFIG_SOC_TIMER_GROUP_SUPPORT_XTAL;CONFIG_SOC_TIMER_GROUP_TOTAL_TIMERS;CONFIG_SOC_LP_TIMER_BIT_WIDTH_LO;CONFIG_SOC_LP_TIMER_BIT_WIDTH_HI;CONFIG_SOC_MWDT_SUPPORT_XTAL;CONFIG_SOC_EFUSE_DIS_DOWNLOAD_ICACHE;CONFIG_SOC_EFUSE_DIS_PAD_JTAG;CONFIG_SOC_EFUSE_DIS_DIRECT_BOOT;CONFIG_SOC_SECURE_BOOT_V2_ECC;CONFIG_SOC_EFUSE_SECURE_BOOT_KEY_DIGESTS;CONFIG_SOC_FLASH_ENCRYPTED_XTS_AES_BLOCK_MAX;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_OPTIONS;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128;CONFIG_SOC_FLASH_ENCRYPTION_XTS_AES_128_DERIVED;CONFIG_SOC_UART_NUM;CONFIG_SOC_UART_HP_NUM;CONFIG_SOC_UART_FIFO_LEN;CONFIG_SOC_UART_BITRATE_MAX;CONFIG_SOC_UART_SUPPORT_WAKEUP_INT;CONFIG_SOC_UART_SUPPORT_PLL_F40M_CLK;CONFIG_SOC_UART_SUPPORT_RTC_CLK;CONFIG_SOC_UART_SUPPORT_XTAL_CLK;CONFIG_SOC_UART_SUPPORT_FSM_TX_WAIT_SEND;CONFIG_SOC_SUPPORT_COEXISTENCE;CONFIG_SOC_COEX_HW_PTI;CONFIG_SOC_EXTERNAL_COEX_ADVANCE;CONFIG_SOC_PHY_DIG_REGS_MEM_SIZE;CONFIG_SOC_WIFI_LIGHT_SLEEP_CLK_WIDTH;CONFIG_SOC_PM_SUPPORT_WIFI_WAKEUP;CONFIG_SOC_PM_SUPPORT_BT_WAKEUP;CONFIG_SOC_PM_SUPPORT_RC_FAST_PD;CONFIG_SOC_PM_SUPPORT_VDDSDIO_PD;CONFIG_SOC_CLK_RC_FAST_D256_SUPPORTED;CONFIG_SOC_RTC_SLOW_CLK_SUPPORT_RC_FAST_D256;CONFIG_SOC_CLK_RC_FAST_SUPPORT_CALIBRATION;CONFIG_SOC_CLK_OSC_SLOW_SUPPORTED;CONFIG_SOC_WIFI_HW_TSF;CONFIG_SOC_WIFI_FTM_SUPPORT;CONFIG_SOC_WIFI_SUPPORT_VARIABLE_BEACON_WINDOW;CONFIG_SOC_WIFI_PHY_NEEDS_USB_WORKAROUND;CONFIG_SOC_BLE_SUPPORTED;CONFIG_SOC_ESP_NIMBLE_CONTROLLER;CONFIG_SOC_BLE_50_SUPPORTED;CONFIG_SOC_BLE_DEVICE_PRIVACY_SUPPORTED;CONFIG_SOC_BLUFI_SUPPORTED;CONFIG_SOC_BLE_PERIODIC_ADV_ENH_SUPPORTED;CONFIG_SOC_PHY_IMPROVE_RX_11B;CONFIG_SOC_PHY_COMBO_MODULE;CONFIG_IDF_CMAKE;CONFIG_IDF_TOOLCHAIN;CONFIG_IDF_TARGET_ARCH_RISCV;CONFIG_IDF_TARGET_ARCH;CONFIG_IDF_TARGET;CONFIG_IDF_INIT_VERSION;CONFIG_IDF_TARGET_ESP32C2;CONFIG_IDF_FIRMWARE_CHIP_ID;CONFIG_APP_BUILD_TYPE_APP_2NDBOOT;CONFIG_APP_BUILD_TYPE_RAM;CONFIG_APP_BUILD_TYPE_ELF_RAM;CONFIG_APP_BUILD_GENERATE_BINARIES;CONFIG_APP_BUILD_BOOTLOADER;CONFIG_APP_BUILD_USE_FLASH_SECTIONS;CONFIG_APP_REPRODUCIBLE_BUILD;CONFIG_APP_NO_BLOBS;CONFIG_NO_BLOBS;CONFIG_BOOTLOADER_COMPILE_TIME_DATE;CONFIG_BOOTLOADER_PROJECT_VER;CONFIG_BOOTLOADER_OFFSET_IN_FLASH;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_SIZE;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_DEBUG;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_PERF;CONFIG_BOOTLOADER_COMPILER_OPTIMIZATION_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_NONE;CONFIG_LOG_BOOTLOADER_LEVEL_NONE;CONFIG_BOOTLOADER_LOG_LEVEL_ERROR;CONFIG_LOG_BOOTLOADER_LEVEL_ERROR;CONFIG_BOOTLOADER_LOG_LEVEL_WARN;CONFIG_LOG_BOOTLOADER_LEVEL_WARN;CONFIG_BOOTLOADER_LOG_LEVEL_INFO;CONFIG_LOG_BOOTLOADER_LEVEL_INFO;CONFIG_BOOTLOADER_LOG_LEVEL_DEBUG;CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG;CONFIG_BOOTLOADER_LOG_LEVEL_VERBOSE;CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE;CONFIG_BOOTLOADER_LOG_LEVEL;CONFIG_LOG_BOOTLOADER_LEVEL;CONFIG_BOOTLOADER_FLASH_DC_AWARE;CONFIG_BOOTLOADER_FLASH_XMC_SUPPORT;CONFIG_BOOTLOADER_FACTORY_RESET;CONFIG_BOOTLOADER_APP_TEST;CONFIG_BOOTLOADER_REGION_PROTECTION_ENABLE;CONFIG_BOOTLOADER_WDT_ENABLE;CONFIG_BOOTLOADER_WDT_DISABLE_IN_USER_CODE;CONFIG_BOOTLOADER_WDT_TIME_MS;CONFIG_BOOTLOADER_APP_ROLLBACK_ENABLE;CONFIG_APP_ROLLBACK_ENABLE;CONFIG_BOOTLOADER_SKIP_VALIDATE_ON_POWER_ON;CONFIG_BOOTLOADER_SKIP_VALIDATE_ALWAYS;CONFIG_SECURE_BOOT_V2_ECC_SUPPORTED;CONFIG_SECURE_BOOT_V2_PREFERRED;CONFIG_SECURE_SIGNED_APPS_NO_SECURE_BOOT;CONFIG_SECURE_BOOT;CONFIG_SECURE_FLASH_ENC_ENABLED;CONFIG_FLASH_ENCRYPTION_ENABLED;CONFIG_SECURE_ROM_DL_MODE_ENABLED;CONFIG_APP_COMPILE_TIME_DATE;CONFIG_APP_EXCLUDE_PROJECT_VER_VAR;CONFIG_APP_EXCLUDE_PROJECT_NAME_VAR;CONFIG_APP_PROJECT_VER_FROM_CONFIG;CONFIG_APP_RETRIEVE_LEN_ELF_SHA;CONFIG_ESP_ROM_HAS_CRC_LE;CONFIG_ESP_ROM_HAS_CRC_BE;CONFIG_ESP_ROM_UART_CLK_IS_XTAL;CONFIG_ESP_ROM_HAS_RETARGETABLE_LOCKING;CONFIG_ESP_ROM_GET_CLK_FREQ;CONFIG_ESP_ROM_HAS_RVFPLIB;CONFIG_ESP_ROM_HAS_HAL_WDT;CONFIG_ESP_ROM_HAS_HAL_SYSTIMER;CONFIG_ESP_ROM_HAS_HEAP_TLSF;CONFIG_ESP_ROM_TLSF_CHECK_PATCH;CONFIG_ESP_ROM_MULTI_HEAP_WALK_PATCH;CONFIG_ESP_ROM_HAS_LAYOUT_TABLE;CONFIG_ESP_ROM_HAS_SPI_FLASH;CONFIG_ESP_ROM_HAS_NEWLIB;CONFIG_ESP_ROM_HAS_NEWLIB_NANO_FORMAT;CONFIG_ESP_ROM_NEEDS_SET_CACHE_MMU_SIZE;CONFIG_ESP_ROM_RAM_APP_NEEDS_MMU_INIT;CONFIG_ESP_ROM_HAS_MBEDTLS_CRYPTO_LIB;CONFIG_ESP_ROM_HAS_SW_FLOAT;CONFIG_ESP_ROM_USB_OTG_NUM;CONFIG_ESP_ROM_USB_SERIAL_DEVICE_NUM;CONFIG_ESP_ROM_HAS_VERSION;CONFIG_BOOT_ROM_LOG_ALWAYS_ON;CONFIG_BOOT_ROM_LOG_ALWAYS_OFF;CONFIG_BOOT_ROM_LOG_ON_GPIO_HIGH;CONFIG_BOOT_ROM_LOG_ON_GPIO_LOW;CONFIG_ESPTOOLPY_NO_STUB;CONFIG_ESPTOOLPY_FLASHMODE_QIO;CONFIG_FLASHMODE_QIO;CONFIG_ESPTOOLPY_FLASHMODE_QOUT;CONFIG_FLASHMODE_QOUT;CONFIG_ESPTOOLPY_FLASHMODE_DIO;CONFIG_FLASHMODE_DIO;CONFIG_ESPTOOLPY_FLASHMODE_DOUT;CONFIG_FLASHMODE_DOUT;CONFIG_ESPTOOLPY_FLASH_SAMPLE_MODE_STR;CONFIG_ESPTOOLPY_FLASHMODE;CONFIG_ESPTOOLPY_FLASHFREQ_60M;CONFIG_ESPTOOLPY_FLASHFREQ_30M;CONFIG_ESPTOOLPY_FLASHFREQ_20M;CONFIG_ESPTOOLPY_FLASHFREQ_15M;CONFIG_ESPTOOLPY_FLASHFREQ;CONFIG_ESPTOOLPY_FLASHSIZE_1MB;CONFIG_ESPTOOLPY_FLASHSIZE_2MB;CONFIG_ESPTOOLPY_FLASHSIZE_4MB;CONFIG_ESPTOOLPY_FLASHSIZE_8MB;CONFIG_ESPTOOLPY_FLASHSIZE_16MB;CONFIG_ESPTOOLPY_FLASHSIZE_32MB;CONFIG_ESPTOOLPY_FLASHSIZE_64MB;CONFIG_ESPTOOLPY_FLASHSIZE_128MB;CONFIG_ESPTOOLPY_FLASHSIZE;CONFIG_ESPTOOLPY_HEADER_FLASHSIZE_UPDATE;CONFIG_ESPTOOLPY_BEFORE_RESET;CONFIG_ESPTOOLPY_BEFORE_NORESET;CONFIG_ESPTOOLPY_BEFORE;CONFIG_ESPTOOLPY_AFTER_RESET;CONFIG_ESPTOOLPY_AFTER_NORESET;CONFIG_ESPTOOLPY_AFTER;CONFIG_ESPTOOLPY_MONITOR_BAUD;CONFIG_MONITOR_BAUD;CONFIG_PARTITION_TABLE_SINGLE_APP;CONFIG_PARTITION_TABLE_SINGLE_APP_LARGE;CONFIG_PARTITION_TABLE_TWO_OTA;CONFIG_PARTITION_TABLE_CUSTOM;CONFIG_PARTITION_TABLE_CUSTOM_FILENAME;CONFIG_PARTITION_TABLE_FILENAME;CONFIG_PARTITION_TABLE_OFFSET;CONFIG_PARTITION_TABLE_MD5;CONFIG_COMPILER_OPTIMIZATION_DEBUG;CONFIG_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG;CONFIG_COMPILER_OPTIMIZATION_DEFAULT;CONFIG_COMPILER_OPTIMIZATION_SIZE;CONFIG_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE;CONFIG_COMPILER_OPTIMIZATION_PERF;CONFIG_COMPILER_OPTIMIZATION_NONE;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_ENABLE;CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_OPTIMIZATION_ASSERTIONS_SILENT;CONFIG_COMPILER_OPTIMIZATION_ASSERTIONS_DISABLE;CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED;CONFIG_COMPILER_FLOAT_LIB_FROM_GCCLIB;CONFIG_COMPILER_FLOAT_LIB_FROM_RVFPLIB;CONFIG_COMPILER_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_OPTIMIZATION_ASSERTION_LEVEL;CONFIG_COMPILER_OPTIMIZATION_CHECKS_SILENT;CONFIG_COMPILER_HIDE_PATHS_MACROS;CONFIG_COMPILER_CXX_EXCEPTIONS;CONFIG_CXX_EXCEPTIONS;CONFIG_COMPILER_CXX_RTTI;CONFIG_COMPILER_STACK_CHECK_MODE_NONE;CONFIG_STACK_CHECK_NONE;CONFIG_COMPILER_STACK_CHECK_MODE_NORM;CONFIG_STACK_CHECK_NORM;CONFIG_COMPILER_STACK_CHECK_MODE_STRONG;CONFIG_STACK_CHECK_STRONG;CONFIG_COMPILER_STACK_CHECK_MODE_ALL;CONFIG_STACK_CHECK_ALL;CONFIG_COMPILER_WARN_WRITE_STRINGS;CONFIG_WARN_WRITE_STRINGS;CONFIG_COMPILER_SAVE_RESTORE_LIBCALLS;CONFIG_COMPILER_DISABLE_GCC12_WARNINGS;CONFIG_COMPILER_DISABLE_GCC13_WARNINGS;CONFIG_COMPILER_DUMP_RTL_FILES;CONFIG_COMPILER_RT_LIB_GCCLIB;CONFIG_COMPILER_RT_LIB_NAME;CONFIG_COMPILER_ORPHAN_SECTIONS_WARNING;CONFIG_COMPILER_ORPHAN_SECTIONS_PLACE;CONFIG_APPTRACE_DEST_JTAG;CONFIG_ESP32_APPTRACE_DEST_TRAX;CONFIG_APPTRACE_DEST_NONE;CONFIG_ESP32_APPTRACE_DEST_NONE;CONFIG_APPTRACE_DEST_UART1;CONFIG_APPTRACE_DEST_UART_NONE;CONFIG_APPTRACE_UART_TASK_PRIO;CONFIG_APPTRACE_LOCK_ENABLE;CONFIG_ESP32_APPTRACE_LOCK_ENABLE;CONFIG_BT_ENABLED;CONFIG_BT_BLUEDROID_ENABLED;CONFIG_BLUEDROID_ENABLED;CONFIG_BT_NIMBLE_ENABLED;CONFIG_NIMBLE_ENABLED;CONFIG_BT_CONTROLLER_ONLY;CONFIG_BT_CONTROLLER_ENABLED;CONFIG_BT_CONTROLLER_DISABLED;CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_INTERNAL;CONFIG_NIMBLE_MEM_ALLOC_MODE_INTERNAL;CONFIG_BT_NIMBLE_MEM_ALLOC_MODE_DEFAULT;CONFIG_NIMBLE_MEM_ALLOC_MODE_DEFAULT;CONFIG_BT_NIMBLE_LOG_LEVEL_NONE;CONFIG_BT_NIMBLE_LOG_LEVEL_ERROR;CONFIG_BT_NIMBLE_LOG_LEVEL_WARNING;CONFIG_BT_NIMBLE_LOG_LEVEL_INFO;CONFIG_BT_NIMBLE_LOG_LEVEL_DEBUG;CONFIG_BT_NIMBLE_LOG_LEVEL;CONFIG_BT_NIMBLE_MAX_CONNECTIONS;CONFIG_NIMBLE_MAX_CONNECTIONS;CONFIG_BT_NIMBLE_MAX_BONDS;CONFIG_NIMBLE_MAX_BONDS;CONFIG_BT_NIMBLE_MAX_CCCDS;CONFIG_NIMBLE_MAX_CCCDS;CONFIG_BT_NIMBLE_L2CAP_COC_MAX_NUM;CONFIG_NIMBLE_L2CAP_COC_MAX_NUM;CONFIG_BT_NIMBLE_PINNED_TO_CORE;CONFIG_NIMBLE_PINNED_TO_CORE;CONFIG_BT_NIMBLE_HOST_TASK_STACK_SIZE;CONFIG_NIMBLE_TASK_STACK_SIZE;CONFIG_BT_NIMBLE_TASK_STACK_SIZE;CONFIG_BT_NIMBLE_ROLE_CENTRAL;CONFIG_NIMBLE_ROLE_CENTRAL;CONFIG_BT_NIMBLE_ROLE_PERIPHERAL;CONFIG_NIMBLE_ROLE_PERIPHERAL;CONFIG_BT_NIMBLE_ROLE_BROADCASTER;CONFIG_NIMBLE_ROLE_BROADCASTER;CONFIG_BT_NIMBLE_ROLE_OBSERVER;CONFIG_NIMBLE_ROLE_OBSERVER;CONFIG_BT_NIMBLE_NVS_PERSIST;CONFIG_NIMBLE_NVS_PERSIST;CONFIG_BT_NIMBLE_SMP_ID_RESET;CONFIG_BT_NIMBLE_SECURITY_ENABLE;CONFIG_BT_NIMBLE_SM_LEGACY;CONFIG_NIMBLE_SM_LEGACY;CONFIG_BT_NIMBLE_SM_SC;CONFIG_NIMBLE_SM_SC;CONFIG_BT_NIMBLE_SM_SC_DEBUG_KEYS;CONFIG_NIMBLE_SM_SC_DEBUG_KEYS;CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_ENCRYPTION;CONFIG_BT_NIMBLE_SM_LVL;CONFIG_BT_NIMBLE_SM_SC_LVL;CONFIG_BT_NIMBLE_SM_SC_ONLY;CONFIG_BT_NIMBLE_DEBUG;CONFIG_NIMBLE_DEBUG;CONFIG_BT_NIMBLE_DYNAMIC_SERVICE;CONFIG_BT_NIMBLE_SVC_GAP_DEVICE_NAME;CONFIG_NIMBLE_SVC_GAP_DEVICE_NAME;CONFIG_BT_NIMBLE_GAP_DEVICE_NAME_MAX_LEN;CONFIG_NIMBLE_GAP_DEVICE_NAME_MAX_LEN;CONFIG_BT_NIMBLE_ATT_PREFERRED_MTU;CONFIG_NIMBLE_ATT_PREFERRED_MTU;CONFIG_BT_NIMBLE_SVC_GAP_APPEARANCE;CONFIG_NIMBLE_SVC_GAP_APPEARANCE;CONFIG_BT_NIMBLE_MSYS_1_BLOCK_COUNT;CONFIG_BT_NIMBLE_MSYS1_BLOCK_COUNT;CONFIG_BT_NIMBLE_MSYS_1_BLOCK_SIZE;CONFIG_BT_NIMBLE_MSYS_2_BLOCK_COUNT;CONFIG_BT_NIMBLE_MSYS_2_BLOCK_SIZE;CONFIG_BT_NIMBLE_TRANSPORT_ACL_FROM_LL_COUNT;CONFIG_BT_NIMBLE_ACL_BUF_COUNT;CONFIG_BT_NIMBLE_TRANSPORT_ACL_SIZE;CONFIG_BT_NIMBLE_ACL_BUF_SIZE;CONFIG_BT_NIMBLE_TRANSPORT_EVT_SIZE;CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE;CONFIG_BT_NIMBLE_TRANSPORT_EVT_COUNT;CONFIG_BT_NIMBLE_HCI_EVT_HI_BUF_COUNT;CONFIG_BT_NIMBLE_TRANSPORT_EVT_DISCARD_COUNT;CONFIG_BT_NIMBLE_HCI_EVT_LO_BUF_COUNT;CONFIG_BT_NIMBLE_L2CAP_COC_SDU_BUFF_COUNT;CONFIG_BT_NIMBLE_GATT_MAX_PROCS;CONFIG_BT_NIMBLE_HS_FLOW_CTRL;CONFIG_NIMBLE_HS_FLOW_CTRL;CONFIG_BT_NIMBLE_RPA_TIMEOUT;CONFIG_NIMBLE_RPA_TIMEOUT;CONFIG_BT_NIMBLE_MESH;CONFIG_NIMBLE_MESH;CONFIG_BT_NIMBLE_CRYPTO_STACK_MBEDTLS;CONFIG_NIMBLE_CRYPTO_STACK_MBEDTLS;CONFIG_BT_NIMBLE_HS_STOP_TIMEOUT_MS;CONFIG_BT_NIMBLE_ENABLE_CONN_REATTEMPT;CONFIG_BT_NIMBLE_MAX_CONN_REATTEMPT;CONFIG_BT_NIMBLE_50_FEATURE_SUPPORT;CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_2M_PHY;CONFIG_BT_NIMBLE_LL_CFG_FEAT_LE_CODED_PHY;CONFIG_BT_NIMBLE_EXT_ADV;CONFIG_BT_NIMBLE_EXT_SCAN;CONFIG_BT_NIMBLE_ENABLE_PERIODIC_SYNC;CONFIG_BT_NIMBLE_MAX_PERIODIC_SYNCS;CONFIG_BT_NIMBLE_MAX_PERIODIC_ADVERTISER_LIST;CONFIG_BT_NIMBLE_GATT_CACHING;CONFIG_BT_NIMBLE_WHITELIST_SIZE;CONFIG_BT_NIMBLE_TEST_THROUGHPUT_TEST;CONFIG_BT_NIMBLE_BLUFI_ENABLE;CONFIG_BT_NIMBLE_USE_ESP_TIMER;CONFIG_BT_NIMBLE_BLE_GATT_BLOB_TRANSFER;CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE;CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM;CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ENC;CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHN;CONFIG_BT_NIMBLE_SVC_GAP_APPEAR_WRITE_PERM_ATHR;CONFIG_BT_NIMBLE_SVC_GAP_CAR_CHAR_NOT_SUPP;CONFIG_BT_NIMBLE_SVC_GAP_CAR_NOT_SUPP;CONFIG_BT_NIMBLE_SVC_GAP_CAR_SUPP;CONFIG_BT_NIMBLE_SVC_GAP_CENT_ADDR_RESOLUTION;CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE;CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM;CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_ENC;CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHEN;CONFIG_BT_NIMBLE_SVC_GAP_NAME_WRITE_PERM_AUTHOR;CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL;CONFIG_BT_NIMBLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL;CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SLAVE_LATENCY;CONFIG_BT_NIMBLE_SVC_GAP_PPCP_SUPERVISION_TMO;CONFIG_BT_NIMBLE_SVC_GAP_GATT_SECURITY_LEVEL;CONFIG_BT_NIMBLE_HID_SERVICE;CONFIG_BT_NIMBLE_SVC_BAS_BATTERY_LEVEL_NOTIFY;CONFIG_BT_NIMBLE_SVC_DIS_MANUFACTURER_NAME;CONFIG_BT_NIMBLE_SVC_DIS_SERIAL_NUMBER;CONFIG_BT_NIMBLE_SVC_DIS_HARDWARE_REVISION;CONFIG_BT_NIMBLE_SVC_DIS_FIRMWARE_REVISION;CONFIG_BT_NIMBLE_SVC_DIS_SOFTWARE_REVISION;CONFIG_BT_NIMBLE_SVC_DIS_SYSTEM_ID;CONFIG_BT_NIMBLE_SVC_DIS_PNP_ID;CONFIG_BT_NIMBLE_SVC_DIS_INCLUDED;CONFIG_BT_NIMBLE_VS_SUPPORT;CONFIG_BT_NIMBLE_ENC_ADV_DATA;CONFIG_BT_NIMBLE_HIGH_DUTY_ADV_ITVL;CONFIG_BT_NIMBLE_HOST_ALLOW_CONNECT_WITH_SCAN;CONFIG_BT_NIMBLE_HOST_QUEUE_CONG_CHECK;CONFIG_BT_NIMBLE_GATTC_PROC_PREEMPTION_PROTECT;CONFIG_UART_HW_FLOWCTRL_DISABLE;CONFIG_UART_HW_FLOWCTRL_CTS_RTS;CONFIG_BT_NIMBLE_HCI_UART_FLOW_CTRL;CONFIG_BT_NIMBLE_HCI_UART_RTS_PIN;CONFIG_BT_NIMBLE_HCI_UART_CTS_PIN;CONFIG_BT_NIMBLE_EATT_CHAN_NUM;CONFIG_BT_NIMBLE_SUBRATE;CONFIG_BT_LE_HCI_INTERFACE_USE_RAM;CONFIG_BT_LE_HCI_INTERFACE_USE_UART;CONFIG_BT_LE_CONTROLLER_NPL_OS_PORTING_SUPPORT;CONFIG_BT_LE_CONTROLLER_TASK_STACK_SIZE;CONFIG_BT_LE_CONTROLLER_LOG_ENABLED;CONFIG_BT_LE_LL_RESOLV_LIST_SIZE;CONFIG_BT_LE_LL_DUP_SCAN_LIST_COUNT;CONFIG_BT_LE_LL_SCA;CONFIG_BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EN;CONFIG_BT_NIMBLE_COEX_PHY_CODED_TX_RX_TLIM_EN;CONFIG_BT_LE_COEX_PHY_CODED_TX_RX_TLIM_DIS;CONFIG_BT_NIMBLE_COEX_PHY_CODED_TX_RX_TLIM_DIS;CONFIG_BT_LE_COEX_PHY_CODED_TX_RX_TLIM_EFF;CONFIG_BT_LE_SLEEP_ENABLE;CONFIG_BT_LE_LP_CLK_SRC_MAIN_XTAL;CONFIG_BT_LE_LP_CLK_SRC_DEFAULT;CONFIG_BT_LE_RELEASE_IRAM_SUPPORTED;CONFIG_BT_LE_TX_CCA_ENABLED;CONFIG_BT_LE_DTM_ENABLED;CONFIG_BT_LE_FEAT_LL_ENCRYPTION;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N24;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N21;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N18;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N15;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N12;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N9;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N6;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N3;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_N0;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P3;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P6;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P9;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P12;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P15;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P18;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_P20;CONFIG_BT_LE_DFT_TX_POWER_LEVEL_DBM_EFF;CONFIG_BT_CTRL_RUN_IN_FLASH_ONLY;CONFIG_BT_LE_CTRL_CHECK_CONNECT_IND_ACCESS_ADDRESS;CONFIG_BT_RELEASE_IRAM;CONFIG_BT_ALARM_MAX_NUM;CONFIG_BT_BLE_LOG_SPI_OUT_ENABLED;CONFIG_BT_HCI_LOG_DEBUG_EN;CONFIG_BLE_MESH;CONFIG_CONSOLE_SORTED_HELP;CONFIG_ADC_SUPPRESS_DEPRECATE_WARN;CONFIG_ADC_SKIP_LEGACY_CONFLICT_CHECK;CONFIG_ADC_CALI_SUPPRESS_DEPRECATE_WARN;CONFIG_GPTIMER_SUPPRESS_DEPRECATE_WARN;CONFIG_GPTIMER_SKIP_LEGACY_CONFLICT_CHECK;CONFIG_TEMP_SENSOR_SUPPRESS_DEPRECATE_WARN;CONFIG_TEMP_SENSOR_SKIP_LEGACY_CONFLICT_CHECK;CONFIG_EFUSE_CUSTOM_TABLE;CONFIG_EFUSE_VIRTUAL;CONFIG_EFUSE_MAX_BLK_LEN;CONFIG_ESP_TLS_USING_MBEDTLS;CONFIG_ESP_TLS_CLIENT_SESSION_TICKETS;CONFIG_ESP_TLS_SERVER_SESSION_TICKETS;CONFIG_ESP_TLS_SERVER_CERT_SELECT_HOOK;CONFIG_ESP_TLS_SERVER_MIN_AUTH_MODE_OPTIONAL;CONFIG_ESP_TLS_PSK_VERIFICATION;CONFIG_ESP_TLS_INSECURE;CONFIG_ADC_ONESHOT_CTRL_FUNC_IN_IRAM;CONFIG_ADC_ENABLE_DEBUG_LOG;CONFIG_ESP_COEX_ENABLED;CONFIG_ESP_COEX_SW_COEXIST_ENABLE;CONFIG_SW_COEXIST_ENABLE;CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE;CONFIG_ESP_WIFI_SW_COEXIST_ENABLE;CONFIG_ESP_COEX_POWER_MANAGEMENT;CONFIG_ESP_COEX_GPIO_DEBUG;CONFIG_ESP_ERR_TO_NAME_LOOKUP;CONFIG_GPIO_CTRL_FUNC_IN_IRAM;CONFIG_GPTIMER_ISR_HANDLER_IN_IRAM;CONFIG_GPTIMER_CTRL_FUNC_IN_IRAM;CONFIG_GPTIMER_ISR_IRAM_SAFE;CONFIG_GPTIMER_OBJ_CACHE_SAFE;CONFIG_GPTIMER_ENABLE_DEBUG_LOG;CONFIG_I2C_ISR_IRAM_SAFE;CONFIG_I2C_ENABLE_DEBUG_LOG;CONFIG_LEDC_CTRL_FUNC_IN_IRAM;CONFIG_SPI_MASTER_IN_IRAM;CONFIG_SPI_MASTER_ISR_IN_IRAM;CONFIG_SPI_SLAVE_IN_IRAM;CONFIG_SPI_SLAVE_ISR_IN_IRAM;CONFIG_TEMP_SENSOR_ENABLE_DEBUG_LOG;CONFIG_UART_ISR_IN_IRAM;CONFIG_ETH_ENABLED;CONFIG_ETH_USE_SPI_ETHERNET;CONFIG_ETH_SPI_ETHERNET_DM9051;CONFIG_ETH_SPI_ETHERNET_W5500;CONFIG_ETH_SPI_ETHERNET_KSZ8851SNL;CONFIG_ETH_USE_OPENETH;CONFIG_ETH_TRANSMIT_MUTEX;CONFIG_ESP_EVENT_LOOP_PROFILING;CONFIG_EVENT_LOOP_PROFILING;CONFIG_ESP_EVENT_POST_FROM_ISR;CONFIG_POST_EVENTS_FROM_ISR;CONFIG_ESP_EVENT_POST_FROM_IRAM_ISR;CONFIG_POST_EVENTS_FROM_IRAM_ISR;CONFIG_ESP_GDBSTUB_ENABLED;CONFIG_ESP_SYSTEM_GDBSTUB_RUNTIME;CONFIG_ESP_GDBSTUB_SUPPORT_TASKS;CONFIG_GDBSTUB_SUPPORT_TASKS;CONFIG_ESP_GDBSTUB_MAX_TASKS;CONFIG_GDBSTUB_MAX_TASKS;CONFIG_ESPHID_TASK_SIZE_BT;CONFIG_ESPHID_TASK_SIZE_BLE;CONFIG_ESP_HTTP_CLIENT_ENABLE_HTTPS;CONFIG_ESP_HTTP_CLIENT_ENABLE_BASIC_AUTH;CONFIG_ESP_HTTP_CLIENT_ENABLE_DIGEST_AUTH;CONFIG_ESP_HTTP_CLIENT_ENABLE_CUSTOM_TRANSPORT;CONFIG_HTTPD_MAX_REQ_HDR_LEN;CONFIG_HTTPD_MAX_URI_LEN;CONFIG_HTTPD_ERR_RESP_NO_DELAY;CONFIG_HTTPD_PURGE_BUF_LEN;CONFIG_HTTPD_LOG_PURGE_DATA;CONFIG_HTTPD_WS_SUPPORT;CONFIG_HTTPD_QUEUE_WORK_BLOCKING;CONFIG_ESP_HTTPS_OTA_DECRYPT_CB;CONFIG_ESP_HTTPS_OTA_ALLOW_HTTP;CONFIG_OTA_ALLOW_HTTP;CONFIG_ESP_HTTPS_SERVER_ENABLE;CONFIG_ESP32C2_REV_MIN_1;CONFIG_ESP32C2_REV_MIN_1_1;CONFIG_ESP32C2_REV_MIN_200;CONFIG_ESP32C2_REV_MIN_FULL;CONFIG_ESP_REV_MIN_FULL;CONFIG_ESP32C2_REV_MAX_FULL;CONFIG_ESP_REV_MAX_FULL;CONFIG_ESP_EFUSE_BLOCK_REV_MIN_FULL;CONFIG_ESP_EFUSE_BLOCK_REV_MAX_FULL;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_STA;CONFIG_ESP_MAC_ADDR_UNIVERSE_WIFI_AP;CONFIG_ESP_MAC_ADDR_UNIVERSE_BT;CONFIG_ESP_MAC_ADDR_UNIVERSE_ETH;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_ESP_MAC_UNIVERSAL_MAC_ADDRESSES;CONFIG_ESP32C2_UNIVERSAL_MAC_ADDRESSES_TWO;CONFIG_ESP32C2_UNIVERSAL_MAC_ADDRESSES_FOUR;CONFIG_ESP32C2_UNIVERSAL_MAC_ADDRESSES;CONFIG_ESP_MAC_USE_CUSTOM_MAC_AS_BASE_MAC;CONFIG_ESP_SLEEP_POWER_DOWN_FLASH;CONFIG_ESP_SYSTEM_PD_FLASH;CONFIG_ESP_SLEEP_FLASH_LEAKAGE_WORKAROUND;CONFIG_ESP_SLEEP_MSPI_NEED_ALL_IO_PU;CONFIG_ESP_SLEEP_GPIO_RESET_WORKAROUND;CONFIG_ESP_SLEEP_WAIT_FLASH_READY_EXTRA_DELAY;CONFIG_ESP_SLEEP_CACHE_SAFE_ASSERTION;CONFIG_ESP_SLEEP_DEBUG;CONFIG_ESP_SLEEP_GPIO_ENABLE_INTERNAL_RESISTORS;CONFIG_RTC_CLK_SRC_INT_RC;CONFIG_RTC_CLK_SRC_EXT_OSC;CONFIG_RTC_CLK_SRC_INT_8MD256;CONFIG_RTC_CLK_CAL_CYCLES;CONFIG_PERIPH_CTRL_FUNC_IN_IRAM;CONFIG_GDMA_CTRL_FUNC_IN_IRAM;CONFIG_GDMA_ISR_IRAM_SAFE;CONFIG_GDMA_ENABLE_DEBUG_LOG;CONFIG_XTAL_FREQ_26;CONFIG_XTAL_FREQ_40;CONFIG_XTAL_FREQ;CONFIG_ESP_SPI_BUS_LOCK_ISR_FUNCS_IN_IRAM;CONFIG_LCD_ENABLE_DEBUG_LOG;CONFIG_ESP_NETIF_IP_LOST_TIMER_INTERVAL;CONFIG_ESP_NETIF_TCPIP_LWIP;CONFIG_ESP_NETIF_LOOPBACK;CONFIG_ESP_NETIF_USES_TCPIP_WITH_BSD_API;CONFIG_ESP_NETIF_RECEIVE_REPORT_ERRORS;CONFIG_ESP_NETIF_L2_TAP;CONFIG_ESP_NETIF_BRIDGE_EN;CONFIG_ESP_NETIF_SET_DNS_PER_DEFAULT_NETIF;CONFIG_ESP_PHY_ENABLED;CONFIG_ESP_PHY_CALIBRATION_AND_DATA_STORAGE;CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE;CONFIG_ESP_PHY_INIT_DATA_IN_PARTITION;CONFIG_ESP32_PHY_INIT_DATA_IN_PARTITION;CONFIG_ESP_PHY_MAX_WIFI_TX_POWER;CONFIG_ESP32_PHY_MAX_WIFI_TX_POWER;CONFIG_ESP_PHY_MAX_TX_POWER;CONFIG_ESP32_PHY_MAX_TX_POWER;CONFIG_ESP_PHY_REDUCE_TX_POWER;CONFIG_REDUCE_PHY_TX_POWER;CONFIG_ESP32_REDUCE_PHY_TX_POWER;CONFIG_ESP_PHY_ENABLE_USB;CONFIG_ESP_PHY_ENABLE_CERT_TEST;CONFIG_ESP_PHY_RF_CAL_PARTIAL;CONFIG_ESP_PHY_RF_CAL_NONE;CONFIG_ESP_PHY_RF_CAL_FULL;CONFIG_ESP_PHY_CALIBRATION_MODE;CONFIG_ESP_PHY_IMPROVE_RX_11B;CONFIG_ESP_PHY_PLL_TRACK_DEBUG;CONFIG_ESP_PHY_RECORD_USED_TIME;CONFIG_PM_ENABLE;CONFIG_PM_SLP_IRAM_OPT;CONFIG_RINGBUF_PLACE_FUNCTIONS_INTO_FLASH;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_80;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ_120;CONFIG_ESP_DEFAULT_CPU_FREQ_MHZ;CONFIG_ESP32C2_INSTRUCTION_CACHE_WRAP;CONFIG_ESP_SYSTEM_PANIC_PRINT_HALT;CONFIG_ESP_SYSTEM_PANIC_PRINT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_SILENT_REBOOT;CONFIG_ESP_SYSTEM_PANIC_GDBSTUB;CONFIG_ESP_SYSTEM_PANIC_REBOOT_DELAY_SECONDS;CONFIG_ESP_SYSTEM_SINGLE_CORE_MODE;CONFIG_ESP_SYSTEM_USE_EH_FRAME;CONFIG_ESP_SYSTEM_PMP_IDRAM_SPLIT;CONFIG_ESP_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_SYSTEM_EVENT_QUEUE_SIZE;CONFIG_ESP_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_STACK_SIZE;CONFIG_MAIN_TASK_STACK_SIZE;CONFIG_ESP_MAIN_TASK_AFFINITY_CPU0;CONFIG_ESP_MAIN_TASK_AFFINITY_NO_AFFINITY;CONFIG_ESP_MAIN_TASK_AFFINITY;CONFIG_ESP_MINIMAL_SHARED_STACK_SIZE;CONFIG_ESP_CONSOLE_UART_DEFAULT;CONFIG_CONSOLE_UART_DEFAULT;CONFIG_ESP_CONSOLE_UART_CUSTOM;CONFIG_CONSOLE_UART_CUSTOM;CONFIG_ESP_CONSOLE_NONE;CONFIG_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART_NONE;CONFIG_ESP_CONSOLE_UART;CONFIG_CONSOLE_UART;CONFIG_ESP_CONSOLE_UART_NUM;CONFIG_CONSOLE_UART_NUM;CONFIG_ESP_CONSOLE_ROM_SERIAL_PORT_NUM;CONFIG_ESP_CONSOLE_UART_BAUDRATE;CONFIG_CONSOLE_UART_BAUDRATE;CONFIG_ESP_INT_WDT;CONFIG_INT_WDT;CONFIG_ESP_INT_WDT_TIMEOUT_MS;CONFIG_INT_WDT_TIMEOUT_MS;CONFIG_ESP_TASK_WDT_EN;CONFIG_ESP_TASK_WDT_USE_ESP_TIMER;CONFIG_ESP_TASK_WDT_INIT;CONFIG_TASK_WDT;CONFIG_ESP_TASK_WDT;CONFIG_ESP_TASK_WDT_PANIC;CONFIG_TASK_WDT_PANIC;CONFIG_ESP_TASK_WDT_TIMEOUT_S;CONFIG_TASK_WDT_TIMEOUT_S;CONFIG_ESP_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0;CONFIG_ESP_PANIC_HANDLER_IRAM;CONFIG_ESP_DEBUG_STUBS_ENABLE;CONFIG_ESP32_DEBUG_STUBS_ENABLE;CONFIG_ESP_DEBUG_OCDAWARE;CONFIG_ESP_SYSTEM_CHECK_INT_LEVEL_4;CONFIG_ESP_BROWNOUT_DET;CONFIG_BROWNOUT_DET;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_7;CONFIG_BROWNOUT_DET_LVL_SEL_7;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_6;CONFIG_BROWNOUT_DET_LVL_SEL_6;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_5;CONFIG_BROWNOUT_DET_LVL_SEL_5;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_4;CONFIG_BROWNOUT_DET_LVL_SEL_4;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_3;CONFIG_BROWNOUT_DET_LVL_SEL_3;CONFIG_ESP_BROWNOUT_DET_LVL_SEL_2;CONFIG_BROWNOUT_DET_LVL_SEL_2;CONFIG_ESP_BROWNOUT_DET_LVL;CONFIG_BROWNOUT_DET_LVL;CONFIG_ESP_SYSTEM_BROWNOUT_INTR;CONFIG_ESP_SYSTEM_HW_STACK_GUARD;CONFIG_ESP_SYSTEM_BBPLL_RECALIB;CONFIG_ESP_SYSTEM_HW_PC_RECORD;CONFIG_ESP_IPC_TASK_STACK_SIZE;CONFIG_IPC_TASK_STACK_SIZE;CONFIG_ESP_TIMER_PROFILING;CONFIG_ESP_TIME_FUNCS_USE_RTC_TIMER;CONFIG_ESP_TIME_FUNCS_USE_ESP_TIMER;CONFIG_ESP_TIMER_TASK_STACK_SIZE;CONFIG_TIMER_TASK_STACK_SIZE;CONFIG_ESP_TIMER_INTERRUPT_LEVEL;CONFIG_ESP_TIMER_SHOW_EXPERIMENTAL;CONFIG_ESP_TIMER_TASK_AFFINITY;CONFIG_ESP_TIMER_TASK_AFFINITY_CPU0;CONFIG_ESP_TIMER_ISR_AFFINITY_CPU0;CONFIG_ESP_TIMER_SUPPORTS_ISR_DISPATCH_METHOD;CONFIG_ESP_TIMER_IMPL_SYSTIMER;CONFIG_ESP_WIFI_ENABLED;CONFIG_ESP32_WIFI_ENABLED;CONFIG_ESP_WIFI_STATIC_RX_BUFFER_NUM;CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM;CONFIG_ESP_WIFI_DYNAMIC_RX_BUFFER_NUM;CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM;CONFIG_ESP_WIFI_STATIC_TX_BUFFER;CONFIG_ESP32_WIFI_STATIC_TX_BUFFER;CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER;CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER;CONFIG_ESP_WIFI_TX_BUFFER_TYPE;CONFIG_ESP32_WIFI_TX_BUFFER_TYPE;CONFIG_ESP_WIFI_DYNAMIC_TX_BUFFER_NUM;CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM;CONFIG_ESP_WIFI_STATIC_RX_MGMT_BUFFER;CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUFFER;CONFIG_ESP_WIFI_DYNAMIC_RX_MGMT_BUF;CONFIG_ESP_WIFI_RX_MGMT_BUF_NUM_DEF;CONFIG_ESP_WIFI_AMPDU_TX_ENABLED;CONFIG_ESP32_WIFI_AMPDU_TX_ENABLED;CONFIG_ESP_WIFI_TX_BA_WIN;CONFIG_ESP32_WIFI_TX_BA_WIN;CONFIG_ESP_WIFI_AMPDU_RX_ENABLED;CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED;CONFIG_ESP_WIFI_RX_BA_WIN;CONFIG_ESP32_WIFI_RX_BA_WIN;CONFIG_ESP_WIFI_NVS_ENABLED;CONFIG_ESP32_WIFI_NVS_ENABLED;CONFIG_ESP_WIFI_SOFTAP_BEACON_MAX_LEN;CONFIG_ESP32_WIFI_SOFTAP_BEACON_MAX_LEN;CONFIG_ESP_WIFI_MGMT_SBUF_NUM;CONFIG_ESP32_WIFI_MGMT_SBUF_NUM;CONFIG_ESP_WIFI_IRAM_OPT;CONFIG_ESP32_WIFI_IRAM_OPT;CONFIG_ESP_WIFI_EXTRA_IRAM_OPT;CONFIG_ESP_WIFI_RX_IRAM_OPT;CONFIG_ESP32_WIFI_RX_IRAM_OPT;CONFIG_ESP_WIFI_ENABLE_WPA3_SAE;CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE;CONFIG_ESP_WIFI_ENABLE_SAE_PK;CONFIG_ESP_WIFI_SOFTAP_SAE_SUPPORT;CONFIG_ESP_WIFI_ENABLE_WPA3_OWE_STA;CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA;CONFIG_ESP_WIFI_SLP_IRAM_OPT;CONFIG_ESP_WIFI_SLP_DEFAULT_MIN_ACTIVE_TIME;CONFIG_ESP_WIFI_SLP_DEFAULT_MAX_ACTIVE_TIME;CONFIG_ESP_WIFI_SLP_DEFAULT_WAIT_BROADCAST_DATA_TIME;CONFIG_ESP_WIFI_FTM_ENABLE;CONFIG_ESP_WIFI_STA_DISCONNECTED_PM_ENABLE;CONFIG_ESP_WIFI_GMAC_SUPPORT;CONFIG_ESP_WIFI_SOFTAP_SUPPORT;CONFIG_ESP_WIFI_SLP_BEACON_LOST_OPT;CONFIG_ESP_WIFI_ESPNOW_MAX_ENCRYPT_NUM;CONFIG_ESP_WIFI_MBEDTLS_CRYPTO;CONFIG_WPA_MBEDTLS_CRYPTO;CONFIG_ESP_WIFI_MBEDTLS_TLS_CLIENT;CONFIG_WPA_MBEDTLS_TLS_CLIENT;CONFIG_ESP_WIFI_11KV_SUPPORT;CONFIG_WPA_11KV_SUPPORT;CONFIG_ESP_WIFI_MBO_SUPPORT;CONFIG_WPA_MBO_SUPPORT;CONFIG_ESP_WIFI_DPP_SUPPORT;CONFIG_WPA_DPP_SUPPORT;CONFIG_ESP_WIFI_11R_SUPPORT;CONFIG_WPA_11R_SUPPORT;CONFIG_ESP_WIFI_WPS_SOFTAP_REGISTRAR;CONFIG_WPA_WPS_SOFTAP_REGISTRAR;CONFIG_ESP_WIFI_WPS_STRICT;CONFIG_WPA_WPS_STRICT;CONFIG_ESP_WIFI_WPS_PASSPHRASE;CONFIG_ESP_WIFI_DEBUG_PRINT;CONFIG_WPA_DEBUG_PRINT;CONFIG_ESP_WIFI_TESTING_OPTIONS;CONFIG_WPA_TESTING_OPTIONS;CONFIG_ESP_WIFI_ENTERPRISE_SUPPORT;CONFIG_ESP_WIFI_ENT_FREE_DYNAMIC_BUFFER;CONFIG_ESP_COREDUMP_ENABLE_TO_FLASH;CONFIG_ESP32_ENABLE_COREDUMP_TO_FLASH;CONFIG_ESP_COREDUMP_ENABLE_TO_UART;CONFIG_ESP32_ENABLE_COREDUMP_TO_UART;CONFIG_ESP_COREDUMP_ENABLE_TO_NONE;CONFIG_ESP32_ENABLE_COREDUMP_TO_NONE;CONFIG_FATFS_VOLUME_COUNT;CONFIG_FATFS_LFN_NONE;CONFIG_FATFS_LFN_HEAP;CONFIG_FATFS_LFN_STACK;CONFIG_FATFS_SECTOR_512;CONFIG_FATFS_SECTOR_4096;CONFIG_FATFS_CODEPAGE_DYNAMIC;CONFIG_FATFS_CODEPAGE_437;CONFIG_FATFS_CODEPAGE_720;CONFIG_FATFS_CODEPAGE_737;CONFIG_FATFS_CODEPAGE_771;CONFIG_FATFS_CODEPAGE_775;CONFIG_FATFS_CODEPAGE_850;CONFIG_FATFS_CODEPAGE_852;CONFIG_FATFS_CODEPAGE_855;CONFIG_FATFS_CODEPAGE_857;CONFIG_FATFS_CODEPAGE_860;CONFIG_FATFS_CODEPAGE_861;CONFIG_FATFS_CODEPAGE_862;CONFIG_FATFS_CODEPAGE_863;CONFIG_FATFS_CODEPAGE_864;CONFIG_FATFS_CODEPAGE_865;CONFIG_FATFS_CODEPAGE_866;CONFIG_FATFS_CODEPAGE_869;CONFIG_FATFS_CODEPAGE_932;CONFIG_FATFS_CODEPAGE_936;CONFIG_FATFS_CODEPAGE_949;CONFIG_FATFS_CODEPAGE_950;CONFIG_FATFS_CODEPAGE;CONFIG_FATFS_FS_LOCK;CONFIG_FATFS_TIMEOUT_MS;CONFIG_FATFS_PER_FILE_CACHE;CONFIG_FATFS_USE_FASTSEEK;CONFIG_FATFS_VFS_FSTAT_BLKSIZE;CONFIG_FATFS_IMMEDIATE_FSYNC;CONFIG_FATFS_USE_LABEL;CONFIG_FATFS_LINK_LOCK;CONFIG_FREERTOS_SMP;CONFIG_FREERTOS_UNICORE;CONFIG_FREERTOS_HZ;CONFIG_FREERTOS_OPTIMIZED_SCHEDULER;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_NONE;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_PTRVAL;CONFIG_FREERTOS_CHECK_STACKOVERFLOW_CANARY;CONFIG_FREERTOS_THREAD_LOCAL_STORAGE_POINTERS;CONFIG_FREERTOS_IDLE_TASK_STACKSIZE;CONFIG_FREERTOS_USE_IDLE_HOOK;CONFIG_FREERTOS_USE_TICK_HOOK;CONFIG_FREERTOS_MAX_TASK_NAME_LEN;CONFIG_FREERTOS_ENABLE_BACKWARD_COMPATIBILITY;CONFIG_FREERTOS_TIMER_SERVICE_TASK_NAME;CONFIG_FREERTOS_TIMER_TASK_AFFINITY_CPU0;CONFIG_FREERTOS_TIMER_TASK_NO_AFFINITY;CONFIG_FREERTOS_TIMER_SERVICE_TASK_CORE_AFFINITY;CONFIG_FREERTOS_TIMER_TASK_PRIORITY;CONFIG_TIMER_TASK_PRIORITY;CONFIG_FREERTOS_TIMER_TASK_STACK_DEPTH;CONFIG_TIMER_TASK_STACK_DEPTH;CONFIG_FREERTOS_TIMER_QUEUE_LENGTH;CONFIG_TIMER_QUEUE_LENGTH;CONFIG_FREERTOS_QUEUE_REGISTRY_SIZE;CONFIG_FREERTOS_TASK_NOTIFICATION_ARRAY_ENTRIES;CONFIG_FREERTOS_USE_TRACE_FACILITY;CONFIG_FREERTOS_USE_LIST_DATA_INTEGRITY_CHECK_BYTES;CONFIG_FREERTOS_GENERATE_RUN_TIME_STATS;CONFIG_FREERTOS_USE_APPLICATION_TASK_TAG;CONFIG_FREERTOS_TASK_FUNCTION_WRAPPER;CONFIG_FREERTOS_WATCHPOINT_END_OF_STACK;CONFIG_FREERTOS_TLSP_DELETION_CALLBACKS;CONFIG_FREERTOS_TASK_PRE_DELETION_HOOK;CONFIG_FREERTOS_ENABLE_STATIC_TASK_CLEAN_UP;CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK;CONFIG_FREERTOS_CHECK_MUTEX_GIVEN_BY_OWNER;CONFIG_FREERTOS_ISR_STACKSIZE;CONFIG_FREERTOS_INTERRUPT_BACKTRACE;CONFIG_FREERTOS_TICK_SUPPORT_SYSTIMER;CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL1;CONFIG_FREERTOS_CORETIMER_SYSTIMER_LVL3;CONFIG_FREERTOS_SYSTICK_USES_SYSTIMER;CONFIG_FREERTOS_PLACE_FUNCTIONS_INTO_FLASH;CONFIG_FREERTOS_CHECK_PORT_CRITICAL_COMPLIANCE;CONFIG_FREERTOS_PORT;CONFIG_FREERTOS_NO_AFFINITY;CONFIG_FREERTOS_SUPPORT_STATIC_ALLOCATION;CONFIG_FREERTOS_DEBUG_OCDAWARE;CONFIG_FREERTOS_ENABLE_TASK_SNAPSHOT;CONFIG_FREERTOS_PLACE_SNAPSHOT_FUNS_INTO_FLASH;CONFIG_FREERTOS_NUMBER_OF_CORES;CONFIG_HAL_ASSERTION_EQUALS_SYSTEM;CONFIG_HAL_ASSERTION_DISABLE;CONFIG_HAL_ASSERTION_SILENT;CONFIG_HAL_ASSERTION_SILIENT;CONFIG_HAL_ASSERTION_ENABLE;CONFIG_HAL_DEFAULT_ASSERTION_LEVEL;CONFIG_HAL_SYSTIMER_USE_ROM_IMPL;CONFIG_HAL_WDT_USE_ROM_IMPL;CONFIG_HAL_SPI_MASTER_FUNC_IN_IRAM;CONFIG_HAL_SPI_SLAVE_FUNC_IN_IRAM;CONFIG_HEAP_POISONING_DISABLED;CONFIG_HEAP_POISONING_LIGHT;CONFIG_HEAP_POISONING_COMPREHENSIVE;CONFIG_HEAP_TRACING_OFF;CONFIG_HEAP_TRACING_STANDALONE;CONFIG_HEAP_TRACING_TOHOST;CONFIG_HEAP_USE_HOOKS;CONFIG_HEAP_TASK_TRACKING;CONFIG_HEAP_ABORT_WHEN_ALLOCATION_FAILS;CONFIG_HEAP_TLSF_USE_ROM_IMPL;CONFIG_LOG_DEFAULT_LEVEL_NONE;CONFIG_LOG_DEFAULT_LEVEL_ERROR;CONFIG_LOG_DEFAULT_LEVEL_WARN;CONFIG_LOG_DEFAULT_LEVEL_INFO;CONFIG_LOG_DEFAULT_LEVEL_DEBUG;CONFIG_LOG_DEFAULT_LEVEL_VERBOSE;CONFIG_LOG_DEFAULT_LEVEL;CONFIG_LOG_MAXIMUM_EQUALS_DEFAULT;CONFIG_LOG_MAXIMUM_LEVEL_DEBUG;CONFIG_LOG_MAXIMUM_LEVEL_VERBOSE;CONFIG_LOG_MAXIMUM_LEVEL;CONFIG_LOG_MASTER_LEVEL;CONFIG_LOG_COLORS;CONFIG_LOG_TIMESTAMP_SOURCE_RTOS;CONFIG_LOG_TIMESTAMP_SOURCE_SYSTEM;CONFIG_LWIP_ENABLE;CONFIG_LWIP_LOCAL_HOSTNAME;CONFIG_LWIP_NETIF_API;CONFIG_LWIP_TCPIP_TASK_PRIO;CONFIG_LWIP_TCPIP_CORE_LOCKING;CONFIG_LWIP_CHECK_THREAD_SAFETY;CONFIG_LWIP_DNS_SUPPORT_MDNS_QUERIES;CONFIG_LWIP_L2_TO_L3_COPY;CONFIG_L2_TO_L3_COPY;CONFIG_LWIP_IRAM_OPTIMIZATION;CONFIG_LWIP_EXTRA_IRAM_OPTIMIZATION;CONFIG_LWIP_TIMERS_ONDEMAND;CONFIG_LWIP_ND6;CONFIG_LWIP_FORCE_ROUTER_FORWARDING;CONFIG_LWIP_MAX_SOCKETS;CONFIG_LWIP_USE_ONLY_LWIP_SELECT;CONFIG_LWIP_SO_LINGER;CONFIG_LWIP_SO_REUSE;CONFIG_LWIP_SO_REUSE_RXTOALL;CONFIG_LWIP_SO_RCVBUF;CONFIG_LWIP_NETBUF_RECVINFO;CONFIG_LWIP_IP_DEFAULT_TTL;CONFIG_LWIP_IP4_FRAG;CONFIG_LWIP_IP6_FRAG;CONFIG_LWIP_IP4_REASSEMBLY;CONFIG_LWIP_IP6_REASSEMBLY;CONFIG_LWIP_IP_REASS_MAX_PBUFS;CONFIG_LWIP_IP_FORWARD;CONFIG_LWIP_STATS;CONFIG_LWIP_ESP_GRATUITOUS_ARP;CONFIG_ESP_GRATUITOUS_ARP;CONFIG_LWIP_GARP_TMR_INTERVAL;CONFIG_GARP_TMR_INTERVAL;CONFIG_LWIP_ESP_MLDV6_REPORT;CONFIG_LWIP_MLDV6_TMR_INTERVAL;CONFIG_LWIP_TCPIP_RECVMBOX_SIZE;CONFIG_TCPIP_RECVMBOX_SIZE;CONFIG_LWIP_DHCP_DOES_ARP_CHECK;CONFIG_LWIP_DHCP_DISABLE_CLIENT_ID;CONFIG_LWIP_DHCP_DISABLE_VENDOR_CLASS_ID;CONFIG_LWIP_DHCP_RESTORE_LAST_IP;CONFIG_LWIP_DHCP_OPTIONS_LEN;CONFIG_LWIP_NUM_NETIF_CLIENT_DATA;CONFIG_LWIP_DHCP_COARSE_TIMER_SECS;CONFIG_LWIP_DHCPS;CONFIG_LWIP_DHCPS_LEASE_UNIT;CONFIG_LWIP_DHCPS_MAX_STATION_NUM;CONFIG_LWIP_DHCPS_STATIC_ENTRIES;CONFIG_LWIP_AUTOIP;CONFIG_LWIP_IPV4;CONFIG_LWIP_IPV6;CONFIG_LWIP_IPV6_AUTOCONFIG;CONFIG_LWIP_IPV6_NUM_ADDRESSES;CONFIG_LWIP_IPV6_FORWARD;CONFIG_LWIP_NETIF_STATUS_CALLBACK;CONFIG_LWIP_NETIF_LOOPBACK;CONFIG_LWIP_LOOPBACK_MAX_PBUFS;CONFIG_LWIP_MAX_ACTIVE_TCP;CONFIG_LWIP_MAX_LISTENING_TCP;CONFIG_LWIP_TCP_HIGH_SPEED_RETRANSMISSION;CONFIG_LWIP_TCP_MAXRTX;CONFIG_TCP_MAXRTX;CONFIG_LWIP_TCP_SYNMAXRTX;CONFIG_TCP_SYNMAXRTX;CONFIG_LWIP_TCP_MSS;CONFIG_TCP_MSS;CONFIG_LWIP_TCP_TMR_INTERVAL;CONFIG_LWIP_TCP_MSL;CONFIG_TCP_MSL;CONFIG_LWIP_TCP_FIN_WAIT_TIMEOUT;CONFIG_LWIP_TCP_SND_BUF_DEFAULT;CONFIG_TCP_SND_BUF_DEFAULT;CONFIG_LWIP_TCP_WND_DEFAULT;CONFIG_TCP_WND_DEFAULT;CONFIG_LWIP_TCP_RECVMBOX_SIZE;CONFIG_TCP_RECVMBOX_SIZE;CONFIG_LWIP_TCP_ACCEPTMBOX_SIZE;CONFIG_LWIP_TCP_QUEUE_OOSEQ;CONFIG_TCP_QUEUE_OOSEQ;CONFIG_LWIP_TCP_OOSEQ_TIMEOUT;CONFIG_LWIP_TCP_OOSEQ_MAX_PBUFS;CONFIG_LWIP_TCP_SACK_OUT;CONFIG_LWIP_TCP_OVERSIZE_MSS;CONFIG_TCP_OVERSIZE_MSS;CONFIG_LWIP_TCP_OVERSIZE_QUARTER_MSS;CONFIG_TCP_OVERSIZE_QUARTER_MSS;CONFIG_LWIP_TCP_OVERSIZE_DISABLE;CONFIG_TCP_OVERSIZE_DISABLE;CONFIG_LWIP_TCP_RTO_TIME;CONFIG_LWIP_MAX_UDP_PCBS;CONFIG_LWIP_UDP_RECVMBOX_SIZE;CONFIG_UDP_RECVMBOX_SIZE;CONFIG_LWIP_CHECKSUM_CHECK_IP;CONFIG_LWIP_CHECKSUM_CHECK_UDP;CONFIG_LWIP_CHECKSUM_CHECK_ICMP;CONFIG_LWIP_TCPIP_TASK_STACK_SIZE;CONFIG_TCPIP_TASK_STACK_SIZE;CONFIG_LWIP_TCPIP_TASK_AFFINITY_NO_AFFINITY;CONFIG_TCPIP_TASK_AFFINITY_NO_AFFINITY;CONFIG_LWIP_TCPIP_TASK_AFFINITY_CPU0;CONFIG_TCPIP_TASK_AFFINITY_CPU0;CONFIG_LWIP_TCPIP_TASK_AFFINITY;CONFIG_TCPIP_TASK_AFFINITY;CONFIG_LWIP_IPV6_ND6_NUM_PREFIXES;CONFIG_LWIP_IPV6_ND6_NUM_ROUTERS;CONFIG_LWIP_IPV6_ND6_NUM_DESTINATIONS;CONFIG_LWIP_PPP_SUPPORT;CONFIG_PPP_SUPPORT;CONFIG_LWIP_IPV6_MEMP_NUM_ND6_QUEUE;CONFIG_LWIP_IPV6_ND6_NUM_NEIGHBORS;CONFIG_LWIP_SLIP_SUPPORT;CONFIG_LWIP_ICMP;CONFIG_LWIP_MULTICAST_PING;CONFIG_LWIP_BROADCAST_PING;CONFIG_LWIP_MAX_RAW_PCBS;CONFIG_LWIP_SNTP_MAX_SERVERS;CONFIG_LWIP_DHCP_GET_NTP_SRV;CONFIG_LWIP_SNTP_UPDATE_DELAY;CONFIG_LWIP_SNTP_STARTUP_DELAY;CONFIG_LWIP_SNTP_MAXIMUM_STARTUP_DELAY;CONFIG_LWIP_DNS_MAX_HOST_IP;CONFIG_LWIP_DNS_MAX_SERVERS;CONFIG_LWIP_FALLBACK_DNS_SERVER_SUPPORT;CONFIG_LWIP_DNS_SETSERVER_WITH_NETIF;CONFIG_LWIP_BRIDGEIF_MAX_PORTS;CONFIG_LWIP_ESP_LWIP_ASSERT;CONFIG_LWIP_HOOK_TCP_ISN_NONE;CONFIG_LWIP_HOOK_TCP_ISN_DEFAULT;CONFIG_LWIP_HOOK_TCP_ISN_CUSTOM;CONFIG_LWIP_HOOK_IP6_ROUTE_NONE;CONFIG_LWIP_HOOK_IP6_ROUTE_DEFAULT;CONFIG_LWIP_HOOK_IP6_ROUTE_CUSTOM;CONFIG_LWIP_HOOK_ND6_GET_GW_NONE;CONFIG_LWIP_HOOK_ND6_GET_GW_DEFAULT;CONFIG_LWIP_HOOK_ND6_GET_GW_CUSTOM;CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_NONE;CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_DEFAULT;CONFIG_LWIP_HOOK_IP6_SELECT_SRC_ADDR_CUSTOM;CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_NONE;CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_DEFAULT;CONFIG_LWIP_HOOK_NETCONN_EXT_RESOLVE_CUSTOM;CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_NONE;CONFIG_LWIP_HOOK_DNS_EXT_RESOLVE_CUSTOM;CONFIG_LWIP_HOOK_IP6_INPUT_NONE;CONFIG_LWIP_HOOK_IP6_INPUT_DEFAULT;CONFIG_LWIP_HOOK_IP6_INPUT_CUSTOM;CONFIG_LWIP_DEBUG;CONFIG_MBEDTLS_INTERNAL_MEM_ALLOC;CONFIG_MBEDTLS_DEFAULT_MEM_ALLOC;CONFIG_MBEDTLS_CUSTOM_MEM_ALLOC;CONFIG_MBEDTLS_ASYMMETRIC_CONTENT_LEN;CONFIG_MBEDTLS_SSL_IN_CONTENT_LEN;CONFIG_MBEDTLS_SSL_OUT_CONTENT_LEN;CONFIG_MBEDTLS_DYNAMIC_BUFFER;CONFIG_MBEDTLS_DEBUG;CONFIG_MBEDTLS_SSL_PROTO_TLS1_3;CONFIG_MBEDTLS_SSL_VARIABLE_BUFFER_LENGTH;CONFIG_MBEDTLS_X509_TRUSTED_CERT_CALLBACK;CONFIG_MBEDTLS_SSL_CONTEXT_SERIALIZATION;CONFIG_MBEDTLS_SSL_KEEP_PEER_CERTIFICATE;CONFIG_MBEDTLS_PKCS7_C;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_FULL;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_CMN;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEFAULT_NONE;CONFIG_MBEDTLS_CUSTOM_CERTIFICATE_BUNDLE;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_DEPRECATED_LIST;CONFIG_MBEDTLS_CERTIFICATE_BUNDLE_MAX_CERTS;CONFIG_MBEDTLS_ECP_RESTARTABLE;CONFIG_MBEDTLS_CMAC_C;CONFIG_MBEDTLS_HARDWARE_SHA;CONFIG_MBEDTLS_HARDWARE_ECC;CONFIG_MBEDTLS_ECC_OTHER_CURVES_SOFT_FALLBACK;CONFIG_MBEDTLS_ROM_MD5;CONFIG_MBEDTLS_ATCA_HW_ECDSA_SIGN;CONFIG_MBEDTLS_ATCA_HW_ECDSA_VERIFY;CONFIG_MBEDTLS_HAVE_TIME;CONFIG_MBEDTLS_PLATFORM_TIME_ALT;CONFIG_MBEDTLS_HAVE_TIME_DATE;CONFIG_MBEDTLS_ECDSA_DETERMINISTIC;CONFIG_MBEDTLS_SHA512_C;CONFIG_MBEDTLS_SHA3_C;CONFIG_MBEDTLS_TLS_SERVER_AND_CLIENT;CONFIG_MBEDTLS_TLS_SERVER_ONLY;CONFIG_MBEDTLS_TLS_CLIENT_ONLY;CONFIG_MBEDTLS_TLS_DISABLED;CONFIG_MBEDTLS_TLS_SERVER;CONFIG_MBEDTLS_TLS_CLIENT;CONFIG_MBEDTLS_TLS_ENABLED;CONFIG_MBEDTLS_PSK_MODES;CONFIG_MBEDTLS_KEY_EXCHANGE_RSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ELLIPTIC_CURVE;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_RSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDHE_ECDSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_ECDSA;CONFIG_MBEDTLS_KEY_EXCHANGE_ECDH_RSA;CONFIG_MBEDTLS_SSL_RENEGOTIATION;CONFIG_MBEDTLS_SSL_PROTO_TLS1_2;CONFIG_MBEDTLS_SSL_PROTO_GMTSSL1_1;CONFIG_MBEDTLS_SSL_PROTO_DTLS;CONFIG_MBEDTLS_SSL_ALPN;CONFIG_MBEDTLS_CLIENT_SSL_SESSION_TICKETS;CONFIG_MBEDTLS_SERVER_SSL_SESSION_TICKETS;CONFIG_MBEDTLS_AES_C;CONFIG_MBEDTLS_CAMELLIA_C;CONFIG_MBEDTLS_DES_C;CONFIG_MBEDTLS_BLOWFISH_C;CONFIG_MBEDTLS_XTEA_C;CONFIG_MBEDTLS_CCM_C;CONFIG_MBEDTLS_GCM_C;CONFIG_MBEDTLS_NIST_KW_C;CONFIG_MBEDTLS_RIPEMD160_C;CONFIG_MBEDTLS_PEM_PARSE_C;CONFIG_MBEDTLS_PEM_WRITE_C;CONFIG_MBEDTLS_X509_CRL_PARSE_C;CONFIG_MBEDTLS_X509_CSR_PARSE_C;CONFIG_MBEDTLS_ECP_C;CONFIG_MBEDTLS_DHM_C;CONFIG_MBEDTLS_ECDH_C;CONFIG_MBEDTLS_ECDSA_C;CONFIG_MBEDTLS_ECJPAKE_C;CONFIG_MBEDTLS_ECP_DP_SECP192R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP224R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP256R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP384R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP521R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP192K1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP224K1_ENABLED;CONFIG_MBEDTLS_ECP_DP_SECP256K1_ENABLED;CONFIG_MBEDTLS_ECP_DP_BP256R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_BP384R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_BP512R1_ENABLED;CONFIG_MBEDTLS_ECP_DP_CURVE25519_ENABLED;CONFIG_MBEDTLS_ECP_NIST_OPTIM;CONFIG_MBEDTLS_ECP_FIXED_POINT_OPTIM;CONFIG_MBEDTLS_POLY1305_C;CONFIG_MBEDTLS_CHACHA20_C;CONFIG_MBEDTLS_HKDF_C;CONFIG_MBEDTLS_THREADING_C;CONFIG_MBEDTLS_ERROR_STRINGS;CONFIG_MBEDTLS_USE_CRYPTO_ROM_IMPL;CONFIG_MBEDTLS_FS_IO;CONFIG_MQTT_PROTOCOL_311;CONFIG_MQTT_PROTOCOL_5;CONFIG_MQTT_TRANSPORT_SSL;CONFIG_MQTT_TRANSPORT_WEBSOCKET;CONFIG_MQTT_TRANSPORT_WEBSOCKET_SECURE;CONFIG_MQTT_MSG_ID_INCREMENTAL;CONFIG_MQTT_SKIP_PUBLISH_IF_DISCONNECTED;CONFIG_MQTT_REPORT_DELETED_MESSAGES;CONFIG_MQTT_USE_CUSTOM_CONFIG;CONFIG_MQTT_TASK_CORE_SELECTION_ENABLED;CONFIG_MQTT_CUSTOM_OUTBOX;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_LF;CONFIG_NEWLIB_STDOUT_LINE_ENDING_CR;CONFIG_NEWLIB_STDIN_LINE_ENDING_CRLF;CONFIG_NEWLIB_STDIN_LINE_ENDING_LF;CONFIG_NEWLIB_STDIN_LINE_ENDING_CR;CONFIG_NEWLIB_NANO_FORMAT;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC_HRT;CONFIG_NEWLIB_TIME_SYSCALL_USE_RTC;CONFIG_NEWLIB_TIME_SYSCALL_USE_HRT;CONFIG_NEWLIB_TIME_SYSCALL_USE_NONE;CONFIG_NVS_ASSERT_ERROR_CHECK;CONFIG_NVS_LEGACY_DUP_KEYS_COMPATIBILITY;CONFIG_OPENTHREAD_ENABLED;CONFIG_OPENTHREAD_SPINEL_ONLY;CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_0;CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_1;CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_VERSION_2;CONFIG_ESP_PROTOCOMM_SUPPORT_SECURITY_PATCH_VERSION;CONFIG_PTHREAD_TASK_PRIO_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_PRIO_DEFAULT;CONFIG_PTHREAD_TASK_STACK_SIZE_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_STACK_SIZE_DEFAULT;CONFIG_PTHREAD_STACK_MIN;CONFIG_ESP32_PTHREAD_STACK_MIN;CONFIG_PTHREAD_TASK_CORE_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_CORE_DEFAULT;CONFIG_PTHREAD_TASK_NAME_DEFAULT;CONFIG_ESP32_PTHREAD_TASK_NAME_DEFAULT;CONFIG_MMU_PAGE_SIZE_32KB;CONFIG_MMU_PAGE_MODE;CONFIG_MMU_PAGE_SIZE;CONFIG_SPI_FLASH_BROWNOUT_RESET_XMC;CONFIG_SPI_FLASH_BROWNOUT_RESET;CONFIG_SPI_FLASH_SUSPEND_QVL_SUPPORTED;CONFIG_SPI_FLASH_AUTO_SUSPEND;CONFIG_SPI_FLASH_SUSPEND_TSUS_VAL_US;CONFIG_SPI_FLASH_FORCE_ENABLE_XMC_C_SUSPEND;CONFIG_SPI_FLASH_VERIFY_WRITE;CONFIG_SPI_FLASH_ENABLE_COUNTERS;CONFIG_SPI_FLASH_ROM_DRIVER_PATCH;CONFIG_SPI_FLASH_ROM_IMPL;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ABORTS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_FAILS;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS;CONFIG_SPI_FLASH_DANGEROUS_WRITE_ALLOWED;CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED;CONFIG_SPI_FLASH_BYPASS_BLOCK_ERASE;CONFIG_SPI_FLASH_YIELD_DURING_ERASE;CONFIG_SPI_FLASH_ERASE_YIELD_DURATION_MS;CONFIG_SPI_FLASH_ERASE_YIELD_TICKS;CONFIG_SPI_FLASH_WRITE_CHUNK_SIZE;CONFIG_SPI_FLASH_SIZE_OVERRIDE;CONFIG_SPI_FLASH_CHECK_ERASE_TIMEOUT_DISABLED;CONFIG_SPI_FLASH_OVERRIDE_CHIP_DRIVER_LIST;CONFIG_SPI_FLASH_VENDOR_XMC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_GD_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_ISSI_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_MXIC_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_WINBOND_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_BOYA_SUPPORTED;CONFIG_SPI_FLASH_VENDOR_TH_SUPPORTED;CONFIG_SPI_FLASH_SUPPORT_ISSI_CHIP;CONFIG_SPI_FLASH_SUPPORT_MXIC_CHIP;CONFIG_SPI_FLASH_SUPPORT_GD_CHIP;CONFIG_SPI_FLASH_SUPPORT_WINBOND_CHIP;CONFIG_SPI_FLASH_SUPPORT_BOYA_CHIP;CONFIG_SPI_FLASH_SUPPORT_TH_CHIP;CONFIG_SPI_FLASH_ENABLE_ENCRYPTED_READ_WRITE;CONFIG_SPIFFS_MAX_PARTITIONS;CONFIG_SPIFFS_CACHE;CONFIG_SPIFFS_CACHE_WR;CONFIG_SPIFFS_CACHE_STATS;CONFIG_SPIFFS_PAGE_CHECK;CONFIG_SPIFFS_GC_MAX_RUNS;CONFIG_SPIFFS_GC_STATS;CONFIG_SPIFFS_PAGE_SIZE;CONFIG_SPIFFS_OBJ_NAME_LEN;CONFIG_SPIFFS_FOLLOW_SYMLINKS;CONFIG_SPIFFS_USE_MAGIC;CONFIG_SPIFFS_USE_MAGIC_LENGTH;CONFIG_SPIFFS_META_LENGTH;CONFIG_SPIFFS_USE_MTIME;CONFIG_SPIFFS_DBG;CONFIG_SPIFFS_API_DBG;CONFIG_SPIFFS_GC_DBG;CONFIG_SPIFFS_CACHE_DBG;CONFIG_SPIFFS_CHECK_DBG;CONFIG_SPIFFS_TEST_VISUALISATION;CONFIG_WS_TRANSPORT;CONFIG_WS_BUFFER_SIZE;CONFIG_WS_DYNAMIC_BUFFER;CONFIG_UNITY_ENABLE_FLOAT;CONFIG_UNITY_ENABLE_DOUBLE;CONFIG_UNITY_ENABLE_64BIT;CONFIG_UNITY_ENABLE_COLOR;CONFIG_UNITY_ENABLE_IDF_TEST_RUNNER;CONFIG_UNITY_ENABLE_FIXTURE;CONFIG_UNITY_ENABLE_BACKTRACE_ON_FAIL;CONFIG_VFS_SUPPORT_IO;CONFIG_VFS_SUPPORT_DIR;CONFIG_VFS_SUPPORT_SELECT;CONFIG_VFS_SUPPRESS_SELECT_DEBUG_OUTPUT;CONFIG_SUPPRESS_SELECT_DEBUG_OUTPUT;CONFIG_VFS_SELECT_IN_RAM;CONFIG_VFS_SUPPORT_TERMIOS;CONFIG_SUPPORT_TERMIOS;CONFIG_VFS_MAX_COUNT;CONFIG_VFS_SEMIHOSTFS_MAX_MOUNT_POINTS;CONFIG_SEMIHOSTFS_MAX_MOUNT_POINTS;CONFIG_WL_SECTOR_SIZE_512;CONFIG_WL_SECTOR_SIZE_4096;CONFIG_WL_SECTOR_SIZE;CONFIG_WIFI_PROV_SCAN_MAX_ENTRIES;CONFIG_WIFI_PROV_AUTOSTOP_TIMEOUT;CONFIG_WIFI_PROV_BLE_BONDING;CONFIG_WIFI_PROV_BLE_SEC_CONN;CONFIG_WIFI_PROV_BLE_FORCE_ENCRYPTION;CONFIG_WIFI_PROV_BLE_NOTIFY;CONFIG_WIFI_PROV_KEEP_BLE_ON_AFTER_PROV;CONFIG_WIFI_PROV_STA_ALL_CHANNEL_SCAN;CONFIG_WIFI_PROV_STA_FAST_SCAN;CONFIG_IDF_EXPERIMENTAL_FEATURES)
# List of deprecated options for backward compatibility
set(CONFIG_APP_BUILD_TYPE_ELF_RAM "")
set(CONFIG_NO_BLOBS "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_NONE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_ERROR "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_WARN "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_INFO "y")
set(CONFIG_LOG_BOOTLOADER_LEVEL_DEBUG "")
set(CONFIG_LOG_BOOTLOADER_LEVEL_VERBOSE "")
set(CONFIG_LOG_BOOTLOADER_LEVEL "3")
set(CONFIG_APP_ROLLBACK_ENABLE "")
set(CONFIG_FLASH_ENCRYPTION_ENABLED "")
set(CONFIG_FLASHMODE_QIO "")
set(CONFIG_FLASHMODE_QOUT "")
set(CONFIG_FLASHMODE_DIO "y")
set(CONFIG_FLASHMODE_DOUT "")
set(CONFIG_MONITOR_BAUD "115200")
set(CONFIG_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_DEBUG "y")
set(CONFIG_COMPILER_OPTIMIZATION_DEFAULT "y")
set(CONFIG_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_COMPILER_OPTIMIZATION_LEVEL_RELEASE "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_ENABLED "y")
set(CONFIG_OPTIMIZATION_ASSERTIONS_SILENT "")
set(CONFIG_OPTIMIZATION_ASSERTIONS_DISABLED "")
set(CONFIG_OPTIMIZATION_ASSERTION_LEVEL "2")
set(CONFIG_CXX_EXCEPTIONS "")
set(CONFIG_STACK_CHECK_NONE "y")
set(CONFIG_STACK_CHECK_NORM "")
set(CONFIG_STACK_CHECK_STRONG "")
set(CONFIG_STACK_CHECK_ALL "")
set(CONFIG_WARN_WRITE_STRINGS "")
set(CONFIG_ESP32_APPTRACE_DEST_TRAX "")
set(CONFIG_ESP32_APPTRACE_DEST_NONE "y")
set(CONFIG_ESP32_APPTRACE_LOCK_ENABLE "y")
set(CONFIG_BLUEDROID_ENABLED "")
set(CONFIG_NIMBLE_ENABLED "y")
set(CONFIG_NIMBLE_MEM_ALLOC_MODE_INTERNAL "y")
set(CONFIG_NIMBLE_MEM_ALLOC_MODE_DEFAULT "")
set(CONFIG_NIMBLE_MAX_CONNECTIONS "1")
set(CONFIG_NIMBLE_MAX_BONDS "3")
set(CONFIG_NIMBLE_MAX_CCCDS "8")
set(CONFIG_NIMBLE_L2CAP_COC_MAX_NUM "0")
set(CONFIG_NIMBLE_PINNED_TO_CORE "0")
set(CONFIG_NIMBLE_TASK_STACK_SIZE "4096")
set(CONFIG_BT_NIMBLE_TASK_STACK_SIZE "4096")
set(CONFIG_NIMBLE_ROLE_CENTRAL "y")
set(CONFIG_NIMBLE_ROLE_PERIPHERAL "y")
set(CONFIG_NIMBLE_ROLE_BROADCASTER "y")
set(CONFIG_NIMBLE_ROLE_OBSERVER "y")
set(CONFIG_NIMBLE_NVS_PERSIST "")
set(CONFIG_NIMBLE_SM_LEGACY "y")
set(CONFIG_NIMBLE_SM_SC "y")
set(CONFIG_NIMBLE_SM_SC_DEBUG_KEYS "")
set(CONFIG_BT_NIMBLE_SM_SC_LVL "0")
set(CONFIG_NIMBLE_DEBUG "")
set(CONFIG_NIMBLE_SVC_GAP_DEVICE_NAME "nimble")
set(CONFIG_NIMBLE_GAP_DEVICE_NAME_MAX_LEN "31")
set(CONFIG_NIMBLE_ATT_PREFERRED_MTU "256")
set(CONFIG_NIMBLE_SVC_GAP_APPEARANCE "0x0")
set(CONFIG_BT_NIMBLE_MSYS1_BLOCK_COUNT "24")
set(CONFIG_BT_NIMBLE_ACL_BUF_COUNT "24")
set(CONFIG_BT_NIMBLE_ACL_BUF_SIZE "255")
set(CONFIG_BT_NIMBLE_HCI_EVT_BUF_SIZE "70")
set(CONFIG_BT_NIMBLE_HCI_EVT_HI_BUF_COUNT "30")
set(CONFIG_BT_NIMBLE_HCI_EVT_LO_BUF_COUNT "8")
set(CONFIG_NIMBLE_HS_FLOW_CTRL "")
set(CONFIG_NIMBLE_RPA_TIMEOUT "900")
set(CONFIG_NIMBLE_MESH "")
set(CONFIG_NIMBLE_CRYPTO_STACK_MBEDTLS "y")
set(CONFIG_BT_NIMBLE_COEX_PHY_CODED_TX_RX_TLIM_EN "")
set(CONFIG_BT_NIMBLE_COEX_PHY_CODED_TX_RX_TLIM_DIS "y")
set(CONFIG_SW_COEXIST_ENABLE "y")
set(CONFIG_ESP32_WIFI_SW_COEXIST_ENABLE "y")
set(CONFIG_ESP_WIFI_SW_COEXIST_ENABLE "y")
set(CONFIG_EVENT_LOOP_PROFILING "")
set(CONFIG_POST_EVENTS_FROM_ISR "y")
set(CONFIG_POST_EVENTS_FROM_IRAM_ISR "y")
set(CONFIG_GDBSTUB_SUPPORT_TASKS "y")
set(CONFIG_GDBSTUB_MAX_TASKS "32")
set(CONFIG_OTA_ALLOW_HTTP "")
set(CONFIG_ESP_SYSTEM_PD_FLASH "")
set(CONFIG_ESP32_PHY_CALIBRATION_AND_DATA_STORAGE "y")
set(CONFIG_ESP32_PHY_INIT_DATA_IN_PARTITION "")
set(CONFIG_ESP32_PHY_MAX_WIFI_TX_POWER "20")
set(CONFIG_ESP32_PHY_MAX_TX_POWER "20")
set(CONFIG_REDUCE_PHY_TX_POWER "")
set(CONFIG_ESP32_REDUCE_PHY_TX_POWER "")
set(CONFIG_SYSTEM_EVENT_QUEUE_SIZE "32")
set(CONFIG_SYSTEM_EVENT_TASK_STACK_SIZE "2304")
set(CONFIG_MAIN_TASK_STACK_SIZE "3584")
set(CONFIG_CONSOLE_UART_DEFAULT "y")
set(CONFIG_CONSOLE_UART_CUSTOM "")
set(CONFIG_CONSOLE_UART_NONE "")
set(CONFIG_ESP_CONSOLE_UART_NONE "")
set(CONFIG_CONSOLE_UART "y")
set(CONFIG_CONSOLE_UART_NUM "0")
set(CONFIG_CONSOLE_UART_BAUDRATE "115200")
set(CONFIG_INT_WDT "y")
set(CONFIG_INT_WDT_TIMEOUT_MS "300")
set(CONFIG_TASK_WDT "y")
set(CONFIG_ESP_TASK_WDT "y")
set(CONFIG_TASK_WDT_PANIC "")
set(CONFIG_TASK_WDT_TIMEOUT_S "5")
set(CONFIG_TASK_WDT_CHECK_IDLE_TASK_CPU0 "y")
set(CONFIG_ESP32_DEBUG_STUBS_ENABLE "")
set(CONFIG_BROWNOUT_DET "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_7 "y")
set(CONFIG_BROWNOUT_DET_LVL_SEL_6 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_5 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_4 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_3 "")
set(CONFIG_BROWNOUT_DET_LVL_SEL_2 "")
set(CONFIG_BROWNOUT_DET_LVL "7")
set(CONFIG_IPC_TASK_STACK_SIZE "1024")
set(CONFIG_TIMER_TASK_STACK_SIZE "3584")
set(CONFIG_ESP32_WIFI_ENABLED "y")
set(CONFIG_ESP32_WIFI_STATIC_RX_BUFFER_NUM "10")
set(CONFIG_ESP32_WIFI_DYNAMIC_RX_BUFFER_NUM "32")
set(CONFIG_ESP32_WIFI_STATIC_TX_BUFFER "")
set(CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER "y")
set(CONFIG_ESP32_WIFI_TX_BUFFER_TYPE "1")
set(CONFIG_ESP32_WIFI_DYNAMIC_TX_BUFFER_NUM "32")
set(CONFIG_ESP32_WIFI_AMPDU_TX_ENABLED "y")
set(CONFIG_ESP32_WIFI_TX_BA_WIN "6")
set(CONFIG_ESP32_WIFI_AMPDU_RX_ENABLED "y")
set(CONFIG_ESP32_WIFI_RX_BA_WIN "6")
set(CONFIG_ESP32_WIFI_NVS_ENABLED "y")
set(CONFIG_ESP32_WIFI_SOFTAP_BEACON_MAX_LEN "752")
set(CONFIG_ESP32_WIFI_MGMT_SBUF_NUM "32")
set(CONFIG_ESP32_WIFI_IRAM_OPT "y")
set(CONFIG_ESP32_WIFI_RX_IRAM_OPT "y")
set(CONFIG_ESP32_WIFI_ENABLE_WPA3_SAE "y")
set(CONFIG_ESP32_WIFI_ENABLE_WPA3_OWE_STA "y")
set(CONFIG_WPA_MBEDTLS_CRYPTO "y")
set(CONFIG_WPA_MBEDTLS_TLS_CLIENT "y")
set(CONFIG_WPA_11KV_SUPPORT "")
set(CONFIG_WPA_MBO_SUPPORT "")
set(CONFIG_WPA_DPP_SUPPORT "")
set(CONFIG_WPA_11R_SUPPORT "")
set(CONFIG_WPA_WPS_SOFTAP_REGISTRAR "")
set(CONFIG_WPA_WPS_STRICT "")
set(CONFIG_WPA_DEBUG_PRINT "")
set(CONFIG_WPA_TESTING_OPTIONS "")
set(CONFIG_ESP32_ENABLE_COREDUMP_TO_FLASH "")
set(CONFIG_ESP32_ENABLE_COREDUMP_TO_UART "")
set(CONFIG_ESP32_ENABLE_COREDUMP_TO_NONE "y")
set(CONFIG_TIMER_TASK_PRIORITY "1")
set(CONFIG_TIMER_TASK_STACK_DEPTH "2048")
set(CONFIG_TIMER_QUEUE_LENGTH "10")
set(CONFIG_ENABLE_STATIC_TASK_CLEAN_UP_HOOK "")
set(CONFIG_HAL_ASSERTION_SILIENT "")
set(CONFIG_L2_TO_L3_COPY "")
set(CONFIG_ESP_GRATUITOUS_ARP "y")
set(CONFIG_GARP_TMR_INTERVAL "60")
set(CONFIG_TCPIP_RECVMBOX_SIZE "32")
set(CONFIG_TCP_MAXRTX "12")
set(CONFIG_TCP_SYNMAXRTX "12")
set(CONFIG_TCP_MSS "1440")
set(CONFIG_TCP_MSL "60000")
set(CONFIG_TCP_SND_BUF_DEFAULT "5760")
set(CONFIG_TCP_WND_DEFAULT "5760")
set(CONFIG_TCP_RECVMBOX_SIZE "6")
set(CONFIG_TCP_QUEUE_OOSEQ "y")
set(CONFIG_TCP_OVERSIZE_MSS "y")
set(CONFIG_TCP_OVERSIZE_QUARTER_MSS "")
set(CONFIG_TCP_OVERSIZE_DISABLE "")
set(CONFIG_UDP_RECVMBOX_SIZE "6")
set(CONFIG_TCPIP_TASK_STACK_SIZE "3072")
set(CONFIG_TCPIP_TASK_AFFINITY_NO_AFFINITY "y")
set(CONFIG_TCPIP_TASK_AFFINITY_CPU0 "")
set(CONFIG_TCPIP_TASK_AFFINITY "0x7fffffff")
set(CONFIG_PPP_SUPPORT "")
set(CONFIG_ESP32_PTHREAD_TASK_PRIO_DEFAULT "5")
set(CONFIG_ESP32_PTHREAD_TASK_STACK_SIZE_DEFAULT "3072")
set(CONFIG_ESP32_PTHREAD_STACK_MIN "768")
set(CONFIG_ESP32_PTHREAD_TASK_CORE_DEFAULT "-1")
set(CONFIG_ESP32_PTHREAD_TASK_NAME_DEFAULT "pthread")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ABORTS "y")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_FAILS "")
set(CONFIG_SPI_FLASH_WRITING_DANGEROUS_REGIONS_ALLOWED "")
set(CONFIG_SUPPRESS_SELECT_DEBUG_OUTPUT "y")
set(CONFIG_SUPPORT_TERMIOS "y")
set(CONFIG_SEMIHOSTFS_MAX_MOUNT_POINTS "1")
