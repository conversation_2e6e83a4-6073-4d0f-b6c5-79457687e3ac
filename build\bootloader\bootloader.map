Archive member included to satisfy reference by file (symbol)

esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                              (esp_bootloader_desc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                              (__assert_func)
esp-idf/main/libmain.a(bootloader_start.c.obj)
                              (call_start_cpu0)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_utility_load_partition_table)
esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_partition_table_verify)
esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_load_image)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_console_deinit)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_sha256_start)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_ana_clock_glitch_reset_config)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (bootloader_init)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_common_ota_select_crc)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj) (bootloader_clock_configure)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj) (bootloader_init_mem)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj) (bootloader_fill_random)
esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (esp_flash_encryption_enabled)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_random_disable)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (bootloader_mmap_get_free_pages)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj) (bootloader_flash_update_id)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj) (bootloader_clear_bss_section)
esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj) (bootloader_console_init)
esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (ESP_EFUSE_DIS_DIRECT_BOOT)
esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_enable_rom_secure_download_mode)
esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_read_field_blob)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj) (esp_efuse_utility_process)
esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj) (esp_efuse_get_key_dis_read)
esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (esp_efuse_utility_clear_program_registers)
esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj) (esp_cpu_configure_region_protection)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj) (rtc_clk_init)
esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj) (rtc_clk_8m_enable)
esp-idf/log/liblog.a(log_noos.c.obj)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (esp_log_timestamp)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj) (efuse_hal_chip_revision)
esp-idf/hal/libhal.a(efuse_hal.c.obj)
                              esp-idf/hal/libhal.a(efuse_hal.c.obj) (efuse_hal_get_major_chip_version)
esp-idf/hal/libhal.a(mmu_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (mmu_hal_unmap_all)
esp-idf/hal/libhal.a(cache_hal.c.obj)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj) (cache_hal_init)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__lshrdi3)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__ashldi3)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj) (__popcountsi2)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (__divdi3)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj) (__udivdi3)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                              D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o) (__clz_tab)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                              esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj) (memcmp)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (_impure_data)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                              esp-idf/main/libmain.a(bootloader_start.c.obj) (memset)
D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj) (memcpy)

Discarded input sections

 .text          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c2.c.obj
 .data          0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c2.c.obj
 .bss           0x00000000        0x0 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c2.c.obj
 .comment       0x00000000       0x30 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c2.c.obj
 .riscv.attributes
                0x00000000       0x44 CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c2.c.obj
 .text          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .text          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .data          0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .bss           0x00000000        0x0 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text.__getreent
                0x00000000        0xa esp-idf/main/libmain.a(bootloader_start.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_get_partition_description
                0x00000000       0x9e esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_atexit
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_hex_to_str
                0x00000000       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_sha256_flash_contents
                0x00000000       0xba esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image_no_verify
                0x00000000        0xe esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify
                0x00000000        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_metadata
                0x00000000       0xc2 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader_data
                0x00000000       0x26 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_verify_bootloader
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.esp_image_get_flash_size
                0x00000000       0x52 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_write_protect_crypt_cnt
                0x00000000       0x10 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_get_flash_encryption_mode
                0x00000000       0xa0 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_set_release_mode.str1.4
                0x00000000       0xd9 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_set_release_mode
                0x00000000      0x126 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .rodata.esp_flash_encryption_cfg_verify_release_mode.str1.4
                0x00000000      0x2ee esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text.esp_flash_encryption_cfg_verify_release_mode
                0x00000000      0x234 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.5       0x00000000       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_flash_erase_range
                0x00000000       0x7e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.3       0x00000000       0x4a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_spi_flash_reset
                0x00000000       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.6       0x00000000       0xfa esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.7       0x00000000       0x88 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .iram1.8       0x00000000        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.__func__.0
                0x00000000       0x1b esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .text.bootloader_flash_update_size
                0x00000000        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .iram1.1       0x00000000       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .iram1.3       0x00000000       0x1a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .text          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .data          0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .bss           0x00000000        0x0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY0_SB_128BIT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY0_FE_128BIT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY0_FE_256BIT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC_CALIBRATION_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RTC_LDO_ACT_DBIAS13
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RTC_LDO_ACT_DBIAS31
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RTC_LDO_SLP_DBIAS31
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RTC_LDO_SLP_DBIAS29
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RTC_LDO_SLP_DBIAS13
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIG_LDO_ACT_STEPD10
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIG_LDO_ACT_DBIAS26
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIG_LDO_SLP_DBIAS26
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIG_LDO_SLP_DBIAS2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIG_DBIAS_HVT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_TEMP_CALIB
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_OCODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WAFER_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .data.ESP_EFUSE_MAC
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA_MAC_CUSTOM
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_USER_DATA
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_CUSTOM_MAC_USED
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_XTS_KEY_LENGTH_256
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_KEY0_HI
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_KEY0_LOW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLOCK_KEY0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC_CALIBRATION_3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RTC_LDO_ACT_DBIAS13
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RTC_LDO_ACT_DBIAS31
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RTC_LDO_SLP_DBIAS31
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RTC_LDO_SLP_DBIAS29
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RTC_LDO_SLP_DBIAS13
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIG_LDO_ACT_STEPD10
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIG_LDO_ACT_DBIAS26
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIG_LDO_SLP_DBIAS26
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIG_LDO_SLP_DBIAS2
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIG_DBIAS_HVT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_TEMP_CALIB
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_OCODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_PKG_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_CUSTOM_MAC
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_CUSTOM_MAC_USED
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_VERSION
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FLASH_TPUW
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SECURE_BOOT_EN
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_XTS_KEY_LENGTH_256
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_DIS_PAD_JTAG
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_WDT_DELAY_SEL
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS_RD_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .sdata.ESP_EFUSE_WR_DIS
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY0_SB_128BIT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY0_FE_128BIT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY0_FE_256BIT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.KEY0  0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC_CALIBRATION_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RTC_LDO_ACT_DBIAS13
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RTC_LDO_ACT_DBIAS31
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RTC_LDO_SLP_DBIAS31
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RTC_LDO_SLP_DBIAS29
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RTC_LDO_SLP_DBIAS13
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIG_LDO_ACT_STEPD10
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIG_LDO_ACT_DBIAS26
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIG_LDO_SLP_DBIAS26
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIG_LDO_SLP_DBIAS2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIG_DBIAS_HVT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.TEMP_CALIB
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.OCODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WAFER_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .rodata.MAC    0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA_MAC_CUSTOM
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.USER_DATA
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.CUSTOM_MAC_USED
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.XTS_KEY_LENGTH_256
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_KEY0_HI
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_KEY0_LOW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLOCK_KEY0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC_CALIBRATION_3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RTC_LDO_ACT_DBIAS13
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RTC_LDO_ACT_DBIAS31
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RTC_LDO_SLP_DBIAS31
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RTC_LDO_SLP_DBIAS29
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RTC_LDO_SLP_DBIAS13
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIG_LDO_ACT_STEPD10
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIG_LDO_ACT_DBIAS26
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIG_LDO_SLP_DBIAS26
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIG_LDO_SLP_DBIAS2
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIG_DBIAS_HVT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_CAL_VOL_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN3
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ADC1_INIT_CODE_ATTEN0
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_TEMP_CALIB
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_OCODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_BLK_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_PKG_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WAFER_VERSION_MINOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_CUSTOM_MAC
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_BLK_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DISABLE_WAFER_VERSION_MAJOR
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_CUSTOM_MAC_USED
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_VERSION
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FLASH_TPUW
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_ENABLE_SECURITY_DOWNLOAD
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DIRECT_BOOT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MODE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_FORCE_SEND_RESUME
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_UART_PRINT_CONTROL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SECURE_BOOT_EN
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_XTS_KEY_LENGTH_256
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_SPI_BOOT_CRYPT_CNT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_DOWNLOAD_ICACHE
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_DIS_PAD_JTAG
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_WDT_DELAY_SEL
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS_RD_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .srodata.WR_DIS
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_info    0x00000000     0x13b9 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_abbrev  0x00000000      0x106 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_aranges
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_line    0x00000000      0x241 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .debug_str     0x00000000     0x15df esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_get_pkg_ver
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_set_rom_log_scheme
                0x00000000       0x4e esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_disable_rom_download_mode
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text.esp_efuse_enable_rom_secure_download_mode
                0x00000000       0x34 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_info    0x00000000      0x374 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_abbrev  0x00000000      0x18f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_loc     0x00000000       0x1f esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_aranges
                0x00000000       0x38 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_ranges  0x00000000       0x28 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_line    0x00000000      0x4e5 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_str     0x00000000      0x579 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .debug_frame   0x00000000       0x88 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_blob
                0x00000000       0x6e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_read_field_bit.str1.4
                0x00000000       0x3b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_bit
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_field_cnt
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_blob
                0x00000000       0x74 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_write_field_cnt.str1.4
                0x00000000       0x59 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_cnt
                0x00000000       0xac esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_field_bit
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_get_field_size
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_reg
                0x00000000       0x5a esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_read_reg
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_write_block
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_begin.str1.4
                0x00000000       0x5c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_begin
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_cancel.str1.4
                0x00000000       0x76 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_cancel
                0x00000000       0x72 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_batch_write_commit.str1.4
                0x00000000       0x42 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_batch_write_commit
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_check_errors
                0x00000000        0x8 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.esp_efuse_destroy_block.str1.4
                0x00000000      0x14b esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text.esp_efuse_destroy_block
                0x00000000      0x15e esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.0
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.1
                0x00000000       0x13 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .rodata.__func__.2
                0x00000000       0x19 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .sbss.s_batch_writing_mode
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_info    0x00000000     0x1140 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_abbrev  0x00000000      0x3cb esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_loc     0x00000000      0x9db esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_aranges
                0x00000000       0x98 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_ranges  0x00000000       0xf0 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_line    0x00000000     0x1239 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_str     0x00000000      0x881 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .debug_frame   0x00000000      0x220 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.write_reg.str1.4
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.write_reg
                0x00000000       0x7a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_process.str1.4
                0x00000000       0x69 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_process
                0x00000000      0x18a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_reset
                0x00000000       0x50 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_efuses
                0x00000000       0x2e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_erase_virt_blocks
                0x00000000        0x2 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_update_virt_blocks.str1.4
                0x00000000       0x32 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_update_virt_blocks
                0x00000000       0x2a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_single_block.str1.4
                0x00000000       0x12 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_single_block
                0x00000000       0x8e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_pending
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_debug_dump_blocks.str1.4
                0x00000000        0xd esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_debug_dump_blocks
                0x00000000       0x46 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_number_of_items
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_read_reg
                0x00000000       0x68 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_fill_buff
                0x00000000       0xde esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_count_once
                0x00000000       0x62 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_cnt.str1.4
                0x00000000       0x31 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_cnt
                0x00000000       0xce esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_write_reg.str1.4
                0x00000000       0x5e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_reg
                0x00000000       0x64 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_write_blob
                0x00000000       0x92 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_get_read_register_address.str1.4
                0x00000000       0x16 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_get_read_register_address
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_is_correct_written_data.str1.4
                0x00000000       0xdd esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_is_correct_written_data
                0x00000000       0xd6 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.0
                0x00000000       0x2c esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.1
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.2
                0x00000000        0xa esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.3
                0x00000000        0xf esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.__func__.4
                0x00000000       0x1a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .sbss.s_burn_counter
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000     0x1467 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x437 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000     0x145d esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0xa8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x2c8 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000     0x185d esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000      0x9cf esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000      0x2bc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_purpose.part.0
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_write_protect
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_read_protect
                0x00000000       0x24 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_coding_scheme
                0x00000000        0x6 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_block_is_empty.str1.4
                0x00000000       0xa0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_block_is_empty
                0x00000000       0x60 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_get_key_dis_read.str1.4
                0x00000000        0xe esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_read
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_read
                0x00000000       0x5e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_dis_write
                0x00000000       0x3e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_set_key_dis_write
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_key_block_unused
                0x00000000       0x3a esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_key_purpose
                0x00000000       0x12 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_get_keypurpose_dis_write
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_find_purpose
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_key
                0x00000000       0xf8 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.esp_efuse_write_keys.str1.4
                0x00000000       0xa0 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_efuse_write_keys
                0x00000000       0xea esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text.esp_secure_boot_read_key_digests
                0x00000000       0x26 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.0
                0x00000000       0x1c esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.1
                0x00000000       0x1b esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.__func__.2
                0x00000000       0x19 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .rodata.s_table
                0x00000000       0x10 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_info    0x00000000      0xecd esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_abbrev  0x00000000      0x3be esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_loc     0x00000000      0x8d6 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_aranges
                0x00000000       0x98 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_ranges  0x00000000      0x168 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_line    0x00000000      0xf4e esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_str     0x00000000      0x9a5 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .debug_frame   0x00000000      0x200 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
 .text          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .data          0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss           0x00000000        0x0 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_clear_program_registers
                0x00000000       0x18 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_check_errors
                0x00000000        0x4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_burn_chip_opt.str1.4
                0x00000000      0x20b esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip_opt
                0x00000000      0x2bc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_burn_chip
                0x00000000        0xc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.esp_efuse_utility_apply_new_coding_scheme.str1.4
                0x00000000       0x4a esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text.esp_efuse_utility_apply_new_coding_scheme
                0x00000000       0xb4 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_write_addr_blocks
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .bss.write_mass_blocks
                0x00000000       0x80 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .rodata.range_read_addr_blocks
                0x00000000       0x20 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_info    0x00000000      0x8a3 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_abbrev  0x00000000      0x287 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_loc     0x00000000      0x359 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_aranges
                0x00000000       0x40 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_ranges  0x00000000      0x120 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_line    0x00000000      0xc68 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_str     0x00000000      0x758 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .comment       0x00000000       0x30 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .debug_frame   0x00000000       0xdc esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .riscv.attributes
                0x00000000       0x44 esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .text          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .data          0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .bss           0x00000000        0x0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_32k_enable_external
                0x00000000       0x1e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_8m_enabled
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_8md256_enabled
                0x00000000       0x10 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_config_fast
                0x00000000       0x36 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_set_to_default_config
                0x00000000       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_set_xtal
                0x00000000       0x18 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_enable
                0x00000000       0x16 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_clk8m_disable
                0x00000000       0x16 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_dig_8m_enabled
                0x00000000        0xc esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text          0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .data          0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .bss           0x00000000        0x0 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.esp_log_impl_lock.str1.4
                0x00000000       0x2c esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_lock
                0x00000000       0x38 esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_lock_timeout
                0x00000000       0x14 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.esp_log_impl_unlock.str1.4
                0x00000000        0xc esp-idf/log/liblog.a(log_noos.c.obj)
 .text.esp_log_impl_unlock
                0x00000000       0x3a esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.__func__.1
                0x00000000       0x14 esp-idf/log/liblog.a(log_noos.c.obj)
 .rodata.__func__.0
                0x00000000       0x12 esp-idf/log/liblog.a(log_noos.c.obj)
 .sbss.s_lock   0x00000000        0x4 esp-idf/log/liblog.a(log_noos.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_get_mac
                0x00000000       0x14 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_set_timing
                0x00000000       0xac esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_read
                0x00000000       0x4c esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_clear_program_registers
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_program
                0x00000000       0x6a esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_rs_calculate
                0x00000000        0x8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text.efuse_hal_is_coding_error_in_block
                0x00000000       0x34 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_bytes_to_pages
                0x00000000       0x22 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_paddr_to_vaddr
                0x00000000       0xda esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_unmap_region
                0x00000000       0xa4 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_vaddr_to_paddr
                0x00000000       0xc8 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .data          0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .bss           0x00000000        0x0 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_vaddr_to_cache_level_id.part.0
                0x00000000       0x4c esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.s_cache_hal_init_ctx
                0x00000000        0x2 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_suspend
                0x00000000       0x1a esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_resume
                0x00000000       0x1c esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_is_cache_enabled
                0x00000000        0xa esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_vaddr_to_cache_level_id
                0x00000000       0x10 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_invalidate_addr
                0x00000000       0x38 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text.cache_hal_get_cache_line_size
                0x00000000       0x22 esp-idf/hal/libhal.a(cache_hal.c.obj)
 .text          0x00000000       0x28 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_info    0x00000000      0x1af D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_abbrev  0x00000000      0x10c D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_loclists
                0x00000000       0x6b D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line    0x00000000      0x107 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_str     0x00000000      0x1dd D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_line_str
                0x00000000      0x210 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .debug_frame   0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
 .text          0x00000000       0x28 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_info    0x00000000      0x1af D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_abbrev  0x00000000      0x10c D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_loclists
                0x00000000       0x6b D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line    0x00000000      0x107 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_str     0x00000000      0x1dd D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_line_str
                0x00000000      0x210 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .debug_frame   0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
 .text          0x00000000       0x42 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_info    0x00000000       0xdf D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_abbrev  0x00000000       0x65 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_loclists
                0x00000000       0xd6 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line    0x00000000       0xe9 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_str     0x00000000      0x1a4 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_line_str
                0x00000000      0x210 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .debug_frame   0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
 .text          0x00000000      0x39e D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_info    0x00000000      0x7a1 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_abbrev  0x00000000      0x1bf D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_loclists
                0x00000000      0x5ab D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_rnglists
                0x00000000       0x93 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_line    0x00000000      0x9ae D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_str     0x00000000      0x252 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .debug_line_str
                0x00000000      0x210 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .eh_frame      0x00000000       0x28 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
 .text          0x00000000      0x35e D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_info    0x00000000      0x76f D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_abbrev  0x00000000      0x1a6 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_loclists
                0x00000000      0x6ff D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_rnglists
                0x00000000       0x99 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line    0x00000000      0x905 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_str     0x00000000      0x253 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .debug_line_str
                0x00000000      0x210 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .eh_frame      0x00000000       0x28 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
 .text          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .rodata        0x00000000      0x100 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_info    0x00000000       0xe6 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_abbrev  0x00000000       0x70 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_aranges
                0x00000000       0x18 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line    0x00000000       0x3f D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_str     0x00000000      0x1a0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .debug_line_str
                0x00000000      0x210 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
 .text          0x00000000       0x4a D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_info    0x00000000      0x10f D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_abbrev  0x00000000       0x8a D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_loclists
                0x00000000      0x130 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line    0x00000000      0x15f D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_str     0x00000000      0x112 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_line_str
                0x00000000      0x2e9 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .debug_frame   0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
 .text          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .data          0x00000000       0xf0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .sdata         0x00000000        0x4 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_info    0x00000000      0x84c D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_abbrev  0x00000000      0x174 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_aranges
                0x00000000       0x18 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line    0x00000000       0x51 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_str     0x00000000      0x4e2 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .debug_line_str
                0x00000000      0x2f1 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
 .text          0x00000000       0xa8 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line    0x00000000      0x18e D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_line_str
                0x00000000      0x10b D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_info    0x00000000       0x33 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_abbrev  0x00000000       0x28 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .debug_str     0x00000000      0x11e D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .riscv.attributes
                0x00000000       0x42 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
 .text          0x00000000       0xe8 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .data          0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .bss           0x00000000        0x0 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_info    0x00000000      0x256 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_abbrev  0x00000000      0x107 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_loclists
                0x00000000      0x1d6 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_aranges
                0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line    0x00000000      0x31c D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_str     0x00000000      0x133 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_line_str
                0x00000000      0x3b6 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .comment       0x00000000       0x30 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .debug_frame   0x00000000       0x20 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
 .riscv.attributes
                0x00000000       0x44 D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)

Memory Configuration

Name             Origin             Length             Attributes
iram_seg         0x403acb70         0x00002000         xrw
iram_loader_seg  0x403aeb70         0x00007000         xrw
dram_seg         0x3fcd5b70         0x00005000         rw
*default*        0x00000000         0xffffffff

Linker script and memory map

                0x00000000                        IDF_TARGET_ESP32C2 = 0x0
LOAD CMakeFiles/bootloader.elf.dir/project_elf_src_esp32c2.c.obj
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/main/libmain.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD esp-idf/soc/libsoc.a
LOAD esp-idf/micro-ecc/libmicro-ecc.a
LOAD esp-idf/hal/libhal.a
LOAD esp-idf/spi_flash/libspi_flash.a
LOAD esp-idf/esp_bootloader_format/libesp_bootloader_format.a
LOAD esp-idf/bootloader_support/libbootloader_support.a
LOAD esp-idf/efuse/libefuse.a
LOAD esp-idf/esp_system/libesp_system.a
LOAD esp-idf/esp_hw_support/libesp_hw_support.a
LOAD esp-idf/esp_common/libesp_common.a
LOAD esp-idf/esp_rom/libesp_rom.a
LOAD esp-idf/log/liblog.a
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libnosys.a
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a
START GROUP
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a
LOAD D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libnosys.a
END GROUP
                [!provide]                        PROVIDE (esprv_int_set_priority = esprv_intc_int_set_priority)
                [!provide]                        PROVIDE (esprv_int_set_threshold = esprv_intc_int_set_threshold)
                [!provide]                        PROVIDE (esprv_int_enable = esprv_intc_int_enable)
                [!provide]                        PROVIDE (esprv_int_disable = esprv_intc_int_disable)
                [!provide]                        PROVIDE (esprv_int_set_type = esprv_intc_int_set_type)
                0x40000018                        rtc_get_reset_reason = 0x40000018
                0x4000001c                        analog_super_wdt_reset_happened = 0x4000001c
                0x40000020                        rtc_get_wakeup_cause = 0x40000020
                0x40000024                        rtc_select_apb_bridge = 0x40000024
                0x40000028                        rtc_unhold_all_pads = 0x40000028
                0x4000002c                        ets_is_print_boot = 0x4000002c
                0x40000030                        ets_vprintf = 0x40000030
                0x40000034                        ets_printf = 0x40000034
                0x40000038                        ets_install_putc1 = 0x40000038
                0x4000003c                        ets_install_uart_printf = 0x4000003c
                0x40000040                        ets_install_putc2 = 0x40000040
                0x40000044                        ets_delay_us = 0x40000044
                0x40000048                        ets_get_stack_info = 0x40000048
                0x4000004c                        ets_install_lock = 0x4000004c
                0x40000050                        UartRxString = 0x40000050
                0x40000054                        UartGetCmdLn = 0x40000054
                0x40000058                        uart_tx_one_char = 0x40000058
                0x4000005c                        uart_tx_one_char2 = 0x4000005c
                0x40000060                        uart_rx_one_char = 0x40000060
                0x40000064                        uart_rx_one_char_block = 0x40000064
                0x40000068                        uart_rx_readbuff = 0x40000068
                0x4000006c                        uartAttach = 0x4000006c
                0x40000070                        uart_tx_flush = 0x40000070
                0x40000074                        uart_tx_wait_idle = 0x40000074
                0x40000078                        uart_div_modify = 0x40000078
                0x4000007c                        ets_write_char_uart = 0x4000007c
                0x40000080                        uart_tx_switch = 0x40000080
                0x40000084                        multofup = 0x40000084
                0x40000088                        software_reset = 0x40000088
                0x4000008c                        software_reset_cpu = 0x4000008c
                0x40000090                        assist_debug_clock_enable = 0x40000090
                0x40000094                        assist_debug_record_enable = 0x40000094
                0x40000098                        clear_super_wdt_reset_flag = 0x40000098
                0x4000009c                        disable_default_watchdog = 0x4000009c
                0x400000a0                        send_packet = 0x400000a0
                0x400000a4                        recv_packet = 0x400000a4
                0x400000a8                        GetUartDevice = 0x400000a8
                0x400000ac                        UartDwnLdProc = 0x400000ac
                0x400000b0                        GetSecurityInfoProc = 0x400000b0
                0x400000b4                        Uart_Init = 0x400000b4
                0x400000b8                        ets_set_user_start = 0x400000b8
                0x3ff4fffc                        ets_rom_layout_p = 0x3ff4fffc
                0x3fcdfffc                        ets_ops_table_ptr = 0x3fcdfffc
                0x400000bc                        mz_adler32 = 0x400000bc
                0x400000c0                        mz_free = 0x400000c0
                0x400000c4                        tdefl_compress = 0x400000c4
                0x400000c8                        tdefl_compress_buffer = 0x400000c8
                0x400000cc                        tdefl_compress_mem_to_heap = 0x400000cc
                0x400000d0                        tdefl_compress_mem_to_mem = 0x400000d0
                0x400000d4                        tdefl_compress_mem_to_output = 0x400000d4
                0x400000d8                        tdefl_get_adler32 = 0x400000d8
                0x400000dc                        tdefl_get_prev_return_status = 0x400000dc
                0x400000e0                        tdefl_init = 0x400000e0
                0x400000e4                        tdefl_write_image_to_png_file_in_memory = 0x400000e4
                0x400000e8                        tdefl_write_image_to_png_file_in_memory_ex = 0x400000e8
                0x400000ec                        tinfl_decompress = 0x400000ec
                0x400000f0                        tinfl_decompress_mem_to_callback = 0x400000f0
                0x400000f4                        tinfl_decompress_mem_to_heap = 0x400000f4
                0x400000f8                        tinfl_decompress_mem_to_mem = 0x400000f8
                0x400000fc                        PROVIDE (esp_rom_spiflash_wait_idle = 0x400000fc)
                0x40000100                        PROVIDE (esp_rom_spiflash_write_encrypted = 0x40000100)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_encrypted_dest = 0x40000104)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_encrypted_enable = 0x40000108)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_encrypted_disable = 0x4000010c)
                [!provide]                        PROVIDE (esp_rom_spiflash_erase_chip = 0x40000110)
                [!provide]                        PROVIDE (_esp_rom_spiflash_erase_sector = 0x40000114)
                [!provide]                        PROVIDE (_esp_rom_spiflash_erase_block = 0x40000118)
                [!provide]                        PROVIDE (_esp_rom_spiflash_write = 0x4000011c)
                [!provide]                        PROVIDE (_esp_rom_spiflash_read = 0x40000120)
                [!provide]                        PROVIDE (_esp_rom_spiflash_unlock = 0x40000124)
                [!provide]                        PROVIDE (_SPIEraseArea = 0x40000128)
                [!provide]                        PROVIDE (_SPI_write_enable = 0x4000012c)
                0x40000130                        PROVIDE (esp_rom_spiflash_erase_sector = 0x40000130)
                0x40000134                        PROVIDE (esp_rom_spiflash_erase_block = 0x40000134)
                0x40000138                        PROVIDE (esp_rom_spiflash_write = 0x40000138)
                0x4000013c                        PROVIDE (esp_rom_spiflash_read = 0x4000013c)
                [!provide]                        PROVIDE (esp_rom_spiflash_unlock = 0x40000140)
                [!provide]                        PROVIDE (SPIEraseArea = 0x40000144)
                [!provide]                        PROVIDE (SPI_write_enable = 0x40000148)
                0x4000014c                        PROVIDE (esp_rom_spiflash_config_param = 0x4000014c)
                [!provide]                        PROVIDE (esp_rom_spiflash_read_user_cmd = 0x40000150)
                [!provide]                        PROVIDE (esp_rom_spiflash_select_qio_pins = 0x40000154)
                [!provide]                        PROVIDE (esp_rom_spi_flash_auto_sus_res = 0x40000158)
                [!provide]                        PROVIDE (esp_rom_spi_flash_send_resume = 0x4000015c)
                [!provide]                        PROVIDE (esp_rom_spi_flash_update_id = 0x40000160)
                0x40000164                        PROVIDE (esp_rom_spiflash_config_clk = 0x40000164)
                [!provide]                        PROVIDE (esp_rom_spiflash_config_readmode = 0x40000168)
                [!provide]                        PROVIDE (esp_rom_spiflash_read_status = 0x4000016c)
                [!provide]                        PROVIDE (esp_rom_spiflash_read_statushigh = 0x40000170)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_status = 0x40000174)
                [!provide]                        PROVIDE (spi_flash_attach = 0x40000178)
                [!provide]                        PROVIDE (spi_flash_get_chip_size = 0x4000017c)
                [!provide]                        PROVIDE (spi_flash_guard_set = 0x40000180)
                [!provide]                        PROVIDE (spi_flash_guard_get = 0x40000184)
                [!provide]                        PROVIDE (spi_flash_read_encrypted = 0x40000188)
                [!provide]                        PROVIDE (spi_flash_mmap_os_func_set = 0x4000018c)
                [!provide]                        PROVIDE (spi_flash_mmap_page_num_init = 0x40000190)
                [!provide]                        PROVIDE (spi_flash_mmap = 0x40000194)
                [!provide]                        PROVIDE (spi_flash_mmap_pages = 0x40000198)
                [!provide]                        PROVIDE (spi_flash_munmap = 0x4000019c)
                [!provide]                        PROVIDE (spi_flash_mmap_dump = 0x400001a0)
                [!provide]                        PROVIDE (spi_flash_check_and_flush_cache = 0x400001a4)
                [!provide]                        PROVIDE (spi_flash_mmap_get_free_pages = 0x400001a8)
                [!provide]                        PROVIDE (spi_flash_cache2phys = 0x400001ac)
                [!provide]                        PROVIDE (spi_flash_phys2cache = 0x400001b0)
                [!provide]                        PROVIDE (spi_flash_disable_cache = 0x400001b4)
                [!provide]                        PROVIDE (spi_flash_restore_cache = 0x400001b8)
                [!provide]                        PROVIDE (spi_flash_cache_enabled = 0x400001bc)
                [!provide]                        PROVIDE (spi_flash_enable_cache = 0x400001c0)
                [!provide]                        PROVIDE (spi_cache_mode_switch = 0x400001c4)
                [!provide]                        PROVIDE (spi_common_set_dummy_output = 0x400001c8)
                [!provide]                        PROVIDE (spi_common_set_flash_cs_timing = 0x400001cc)
                [!provide]                        PROVIDE (esp_rom_spi_set_address_bit_len = 0x400001d0)
                [!provide]                        PROVIDE (esp_enable_cache_flash_wrap = 0x400001d4)
                [!provide]                        PROVIDE (SPILock = 0x400001d8)
                [!provide]                        PROVIDE (SPIMasterReadModeCnfig = 0x400001dc)
                [!provide]                        PROVIDE (SPI_Common_Command = 0x400001e0)
                [!provide]                        PROVIDE (SPI_WakeUp = 0x400001e4)
                [!provide]                        PROVIDE (SPI_block_erase = 0x400001e8)
                [!provide]                        PROVIDE (SPI_chip_erase = 0x400001ec)
                [!provide]                        PROVIDE (SPI_init = 0x400001f0)
                [!provide]                        PROVIDE (SPI_page_program = 0x400001f4)
                [!provide]                        PROVIDE (SPI_read_data = 0x400001f8)
                [!provide]                        PROVIDE (SPI_sector_erase = 0x400001fc)
                [!provide]                        PROVIDE (SelectSpiFunction = 0x40000200)
                [!provide]                        PROVIDE (SetSpiDrvs = 0x40000204)
                [!provide]                        PROVIDE (Wait_SPI_Idle = 0x40000208)
                [!provide]                        PROVIDE (spi_dummy_len_fix = 0x4000020c)
                [!provide]                        PROVIDE (Disable_QMode = 0x40000210)
                [!provide]                        PROVIDE (Enable_QMode = 0x40000214)
                [!provide]                        PROVIDE (rom_spiflash_legacy_funcs = 0x3fcdfff4)
                0x3fcdfff0                        PROVIDE (rom_spiflash_legacy_data = 0x3fcdfff0)
                [!provide]                        PROVIDE (g_flash_guard_ops = 0x3fcdfff8)
                [!provide]                        PROVIDE (spi_flash_hal_poll_cmd_done = 0x40000218)
                [!provide]                        PROVIDE (spi_flash_hal_device_config = 0x4000021c)
                [!provide]                        PROVIDE (spi_flash_hal_configure_host_io_mode = 0x40000220)
                [!provide]                        PROVIDE (spi_flash_hal_common_command = 0x40000224)
                [!provide]                        PROVIDE (spi_flash_hal_read = 0x40000228)
                [!provide]                        PROVIDE (spi_flash_hal_erase_chip = 0x4000022c)
                [!provide]                        PROVIDE (spi_flash_hal_erase_sector = 0x40000230)
                [!provide]                        PROVIDE (spi_flash_hal_erase_block = 0x40000234)
                [!provide]                        PROVIDE (spi_flash_hal_program_page = 0x40000238)
                [!provide]                        PROVIDE (spi_flash_hal_set_write_protect = 0x4000023c)
                [!provide]                        PROVIDE (spi_flash_hal_host_idle = 0x40000240)
                [!provide]                        PROVIDE (spi_flash_hal_check_status = 0x40000244)
                [!provide]                        PROVIDE (spi_flash_hal_setup_read_suspend = 0x40000248)
                [!provide]                        PROVIDE (spi_flash_hal_setup_auto_suspend_mode = 0x4000024c)
                [!provide]                        PROVIDE (spi_flash_hal_setup_auto_resume_mode = 0x40000250)
                [!provide]                        PROVIDE (spi_flash_hal_disable_auto_suspend_mode = 0x40000254)
                [!provide]                        PROVIDE (spi_flash_hal_disable_auto_resume_mode = 0x40000258)
                [!provide]                        PROVIDE (spi_flash_hal_resume = 0x4000025c)
                [!provide]                        PROVIDE (spi_flash_hal_suspend = 0x40000260)
                [!provide]                        PROVIDE (spi_flash_encryption_hal_enable = 0x40000264)
                [!provide]                        PROVIDE (spi_flash_encryption_hal_disable = 0x40000268)
                [!provide]                        PROVIDE (spi_flash_encryption_hal_prepare = 0x4000026c)
                [!provide]                        PROVIDE (spi_flash_encryption_hal_done = 0x40000270)
                [!provide]                        PROVIDE (spi_flash_encryption_hal_destroy = 0x40000274)
                [!provide]                        PROVIDE (spi_flash_encryption_hal_check = 0x40000278)
                [!provide]                        PROVIDE (spi_flash_chip_generic_probe = 0x40000380)
                [!provide]                        PROVIDE (spi_flash_chip_generic_detect_size = 0x40000384)
                [!provide]                        PROVIDE (spi_flash_chip_generic_write = 0x40000388)
                [!provide]                        PROVIDE (spi_flash_chip_generic_write_encrypted = 0x4000038c)
                [!provide]                        PROVIDE (spi_flash_chip_generic_set_write_protect = 0x40000390)
                [!provide]                        PROVIDE (spi_flash_common_write_status_16b_wrsr = 0x40000394)
                [!provide]                        PROVIDE (spi_flash_chip_generic_reset = 0x40000398)
                [!provide]                        PROVIDE (spi_flash_chip_generic_erase_chip = 0x4000039c)
                [!provide]                        PROVIDE (spi_flash_chip_generic_erase_sector = 0x400003a0)
                [!provide]                        PROVIDE (spi_flash_chip_generic_erase_block = 0x400003a4)
                [!provide]                        PROVIDE (spi_flash_chip_generic_page_program = 0x400003a8)
                [!provide]                        PROVIDE (spi_flash_chip_generic_get_write_protect = 0x400003ac)
                [!provide]                        PROVIDE (spi_flash_common_read_status_16b_rdsr_rdsr2 = 0x400003b0)
                [!provide]                        PROVIDE (spi_flash_chip_generic_read_reg = 0x400003b4)
                [!provide]                        PROVIDE (spi_flash_chip_generic_yield = 0x400003b8)
                [!provide]                        PROVIDE (spi_flash_generic_wait_host_idle = 0x400003bc)
                [!provide]                        PROVIDE (spi_flash_chip_generic_wait_idle = 0x400003c0)
                [!provide]                        PROVIDE (spi_flash_chip_generic_config_host_io_mode = 0x400003c4)
                [!provide]                        PROVIDE (spi_flash_chip_generic_read = 0x400003c8)
                [!provide]                        PROVIDE (spi_flash_common_read_status_8b_rdsr2 = 0x400003cc)
                [!provide]                        PROVIDE (spi_flash_chip_generic_get_io_mode = 0x400003d0)
                [!provide]                        PROVIDE (spi_flash_common_read_status_8b_rdsr = 0x400003d4)
                [!provide]                        PROVIDE (spi_flash_common_write_status_8b_wrsr = 0x400003d8)
                [!provide]                        PROVIDE (spi_flash_common_write_status_8b_wrsr2 = 0x400003dc)
                [!provide]                        PROVIDE (spi_flash_common_set_io_mode = 0x400003e0)
                [!provide]                        PROVIDE (spi_flash_chip_generic_set_io_mode = 0x400003e4)
                [!provide]                        PROVIDE (spi_flash_chip_generic_read_unique_id = 0x400003e8)
                [!provide]                        PROVIDE (spi_flash_chip_generic_get_caps = 0x400003ec)
                [!provide]                        PROVIDE (spi_flash_chip_generic_suspend_cmd_conf = 0x400003f0)
                [!provide]                        PROVIDE (spi_flash_chip_gd_get_io_mode = 0x400003f4)
                [!provide]                        PROVIDE (spi_flash_chip_gd_probe = 0x400003f8)
                [!provide]                        PROVIDE (spi_flash_chip_gd_set_io_mode = 0x400003fc)
                [!provide]                        PROVIDE (spi_flash_chip_generic_config_data = 0x3fcdffe8)
                [!provide]                        PROVIDE (spi_flash_encryption = 0x3fcdffe4)
                [!provide]                        PROVIDE (memspi_host_read_id_hs = 0x40000400)
                [!provide]                        PROVIDE (memspi_host_read_status_hs = 0x40000404)
                [!provide]                        PROVIDE (memspi_host_flush_cache = 0x40000408)
                [!provide]                        PROVIDE (memspi_host_erase_chip = 0x4000040c)
                [!provide]                        PROVIDE (memspi_host_erase_sector = 0x40000410)
                [!provide]                        PROVIDE (memspi_host_erase_block = 0x40000414)
                [!provide]                        PROVIDE (memspi_host_program_page = 0x40000418)
                [!provide]                        PROVIDE (memspi_host_read = 0x4000041c)
                [!provide]                        PROVIDE (memspi_host_set_write_protect = 0x40000420)
                [!provide]                        PROVIDE (memspi_host_set_max_read_len = 0x40000424)
                [!provide]                        PROVIDE (memspi_host_read_data_slicer = 0x40000428)
                [!provide]                        PROVIDE (memspi_host_write_data_slicer = 0x4000042c)
                [!provide]                        PROVIDE (esp_flash_chip_driver_initialized = 0x40000430)
                [!provide]                        PROVIDE (esp_flash_read_id = 0x40000434)
                [!provide]                        PROVIDE (esp_flash_get_size = 0x40000438)
                [!provide]                        PROVIDE (esp_flash_erase_chip = 0x4000043c)
                [!provide]                        PROVIDE (esp_flash_erase_region = 0x40000440)
                [!provide]                        PROVIDE (esp_flash_get_chip_write_protect = 0x40000444)
                [!provide]                        PROVIDE (esp_flash_set_chip_write_protect = 0x40000448)
                [!provide]                        PROVIDE (esp_flash_get_protectable_regions = 0x4000044c)
                [!provide]                        PROVIDE (esp_flash_get_protected_region = 0x40000450)
                [!provide]                        PROVIDE (esp_flash_set_protected_region = 0x40000454)
                [!provide]                        PROVIDE (esp_flash_read = 0x40000458)
                [!provide]                        PROVIDE (esp_flash_write = 0x4000045c)
                [!provide]                        PROVIDE (esp_flash_write_encrypted = 0x40000460)
                [!provide]                        PROVIDE (esp_flash_read_encrypted = 0x40000464)
                [!provide]                        PROVIDE (esp_flash_get_io_mode = 0x40000468)
                [!provide]                        PROVIDE (esp_flash_set_io_mode = 0x4000046c)
                [!provide]                        PROVIDE (spi_flash_boot_attach = 0x40000470)
                [!provide]                        PROVIDE (esp_flash_read_chip_id = 0x40000474)
                [!provide]                        PROVIDE (detect_spi_flash_chip = 0x40000478)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_disable = 0x4000047c)
                [!provide]                        PROVIDE (esp_flash_suspend_cmd_init = 0x40000480)
                [!provide]                        PROVIDE (esp_flash_default_chip = 0x3fcdffe0)
                [!provide]                        PROVIDE (esp_flash_api_funcs = 0x3fcdffdc)
                0x400006e0                        PROVIDE (Cache_Get_ICache_Line_Size = 0x400006e0)
                [!provide]                        PROVIDE (Cache_Get_Mode = 0x400006e4)
                [!provide]                        PROVIDE (Cache_Address_Through_IBus = 0x400006e8)
                [!provide]                        PROVIDE (Cache_Address_Through_DBus = 0x400006ec)
                [!provide]                        PROVIDE (Cache_Set_Default_Mode = 0x400006f0)
                [!provide]                        PROVIDE (Cache_Enable_Defalut_ICache_Mode = 0x400006f4)
                0x400006f8                        PROVIDE (ROM_Boot_Cache_Init = 0x400006f8)
                [!provide]                        PROVIDE (MMU_Set_Page_Mode = 0x400006fc)
                [!provide]                        PROVIDE (MMU_Get_Page_Mode = 0x40000700)
                [!provide]                        PROVIDE (Cache_Invalidate_ICache_Items = 0x40000704)
                [!provide]                        PROVIDE (Cache_Op_Addr = 0x40000708)
                0x4000070c                        PROVIDE (Cache_Invalidate_Addr = 0x4000070c)
                [!provide]                        PROVIDE (Cache_Invalidate_ICache_All = 0x40000710)
                [!provide]                        PROVIDE (Cache_Mask_All = 0x40000714)
                [!provide]                        PROVIDE (Cache_UnMask_Dram0 = 0x40000718)
                0x4000071c                        PROVIDE (Cache_Disable_ICache = 0x4000071c)
                0x40000720                        PROVIDE (Cache_Enable_ICache = 0x40000720)
                0x40000724                        PROVIDE (Cache_Suspend_ICache = 0x40000724)
                0x40000728                        PROVIDE (Cache_Resume_ICache = 0x40000728)
                [!provide]                        PROVIDE (Cache_Freeze_ICache_Enable = 0x4000072c)
                [!provide]                        PROVIDE (Cache_Freeze_ICache_Disable = 0x40000730)
                [!provide]                        PROVIDE (Cache_Set_IDROM_MMU_Size = 0x40000734)
                [!provide]                        PROVIDE (Cache_Get_IROM_MMU_End = 0x40000738)
                [!provide]                        PROVIDE (Cache_Get_DROM_MMU_End = 0x4000073c)
                [!provide]                        PROVIDE (Cache_Owner_Init = 0x40000740)
                [!provide]                        PROVIDE (Cache_Occupy_ICache_MEMORY = 0x40000744)
                [!provide]                        PROVIDE (Cache_MMU_Init = 0x40000748)
                [!provide]                        PROVIDE (Cache_Ibus_MMU_Set = 0x4000074c)
                [!provide]                        PROVIDE (Cache_Dbus_MMU_Set = 0x40000750)
                [!provide]                        PROVIDE (Cache_Count_Flash_Pages = 0x40000754)
                [!provide]                        PROVIDE (Cache_Travel_Tag_Memory = 0x40000758)
                [!provide]                        PROVIDE (Cache_Get_Virtual_Addr = 0x4000075c)
                [!provide]                        PROVIDE (Cache_Get_Memory_BaseAddr = 0x40000760)
                [!provide]                        PROVIDE (Cache_Get_Memory_Addr = 0x40000764)
                [!provide]                        PROVIDE (Cache_Get_Memory_value = 0x40000768)
                [!provide]                        PROVIDE (rom_cache_op_cb = 0x3fcdffd0)
                [!provide]                        PROVIDE (rom_cache_internal_table_ptr = 0x3fcdffcc)
                0x4000076c                        ets_get_apb_freq = 0x4000076c
                0x40000770                        ets_get_cpu_frequency = 0x40000770
                0x40000774                        ets_update_cpu_frequency = 0x40000774
                0x40000778                        ets_get_printf_channel = 0x40000778
                0x4000077c                        ets_get_xtal_div = 0x4000077c
                0x40000780                        ets_set_xtal_div = 0x40000780
                0x40000784                        ets_get_xtal_freq = 0x40000784
                0x40000788                        gpio_input_get = 0x40000788
                0x4000078c                        gpio_matrix_in = 0x4000078c
                0x40000790                        gpio_matrix_out = 0x40000790
                0x40000794                        gpio_output_disable = 0x40000794
                0x40000798                        gpio_output_enable = 0x40000798
                0x4000079c                        gpio_output_set = 0x4000079c
                0x400007a0                        gpio_pad_hold = 0x400007a0
                0x400007a4                        gpio_pad_input_disable = 0x400007a4
                0x400007a8                        gpio_pad_input_enable = 0x400007a8
                0x400007ac                        gpio_pad_pulldown = 0x400007ac
                0x400007b0                        gpio_pad_pullup = 0x400007b0
                0x400007b4                        gpio_pad_select_gpio = 0x400007b4
                0x400007b8                        gpio_pad_set_drv = 0x400007b8
                0x400007bc                        gpio_pad_unhold = 0x400007bc
                0x400007c0                        gpio_pin_wakeup_disable = 0x400007c0
                0x400007c4                        gpio_pin_wakeup_enable = 0x400007c4
                0x400007c8                        gpio_bypass_matrix_in = 0x400007c8
                0x400007cc                        esprv_intc_int_set_priority = 0x400007cc
                0x400007d0                        esprv_intc_int_set_threshold = 0x400007d0
                0x400007d4                        esprv_intc_int_enable = 0x400007d4
                0x400007d8                        esprv_intc_int_disable = 0x400007d8
                0x400007dc                        esprv_intc_int_set_type = 0x400007dc
                [!provide]                        PROVIDE (intr_handler_set = 0x400007e0)
                0x400007e4                        intr_matrix_set = 0x400007e4
                0x400007e8                        ets_intr_lock = 0x400007e8
                0x400007ec                        ets_intr_unlock = 0x400007ec
                0x400007f0                        ets_isr_attach = 0x400007f0
                0x400007f4                        ets_isr_mask = 0x400007f4
                0x400007f8                        ets_isr_unmask = 0x400007f8
                0x400007fc                        crc32_le = 0x400007fc
                0x40000800                        crc16_le = 0x40000800
                0x40000804                        crc8_le = 0x40000804
                0x40000808                        crc32_be = 0x40000808
                0x4000080c                        crc16_be = 0x4000080c
                0x40000810                        crc8_be = 0x40000810
                0x40000814                        esp_crc8 = 0x40000814
                0x40000818                        ets_sha_enable = 0x40000818
                0x4000081c                        ets_sha_disable = 0x4000081c
                0x40000820                        ets_sha_get_state = 0x40000820
                0x40000824                        ets_sha_init = 0x40000824
                0x40000828                        ets_sha_process = 0x40000828
                0x4000082c                        ets_sha_starts = 0x4000082c
                0x40000830                        ets_sha_update = 0x40000830
                0x40000834                        ets_sha_finish = 0x40000834
                0x40000838                        ets_sha_clone = 0x40000838
                0x3ff4fff8                        crc32_le_table_ptr = 0x3ff4fff8
                0x3ff4fff4                        crc16_le_table_ptr = 0x3ff4fff4
                0x3ff4fff0                        crc8_le_table_ptr = 0x3ff4fff0
                0x3ff4ffec                        crc32_be_table_ptr = 0x3ff4ffec
                0x3ff4ffe8                        crc16_be_table_ptr = 0x3ff4ffe8
                0x3ff4ffe4                        crc8_be_table_ptr = 0x3ff4ffe4
                0x4000083c                        ets_efuse_read = 0x4000083c
                0x40000840                        ets_efuse_program = 0x40000840
                0x40000844                        ets_efuse_clear_program_registers = 0x40000844
                0x40000848                        ets_efuse_write_key = 0x40000848
                0x4000084c                        ets_efuse_get_read_register_address = 0x4000084c
                0x40000850                        ets_efuse_get_key_purpose = 0x40000850
                0x40000854                        ets_efuse_key_block_unused = 0x40000854
                0x40000858                        ets_efuse_find_unused_key_block = 0x40000858
                0x4000085c                        ets_efuse_rs_calculate = 0x4000085c
                0x40000860                        ets_efuse_count_unused_key_blocks = 0x40000860
                0x40000864                        ets_efuse_secure_boot_enabled = 0x40000864
                0x40000868                        ets_efuse_secure_boot_aggressive_revoke_enabled = 0x40000868
                0x4000086c                        ets_efuse_cache_encryption_enabled = 0x4000086c
                0x40000870                        ets_efuse_download_modes_disabled = 0x40000870
                0x40000874                        ets_efuse_find_purpose = 0x40000874
                0x40000878                        ets_efuse_force_send_resume = 0x40000878
                0x4000087c                        ets_efuse_get_flash_delay_us = 0x4000087c
                0x40000880                        ets_efuse_get_mac = 0x40000880
                0x40000884                        ets_efuse_get_uart_print_control = 0x40000884
                0x40000888                        ets_efuse_direct_boot_mode_disabled = 0x40000888
                0x4000088c                        ets_efuse_security_download_modes_enabled = 0x4000088c
                0x40000890                        ets_efuse_set_timing = 0x40000890
                0x40000894                        ets_efuse_jtag_disabled = 0x40000894
                0x40000898                        ets_ecdsa_verify = 0x40000898
                0x4000089c                        ets_secure_boot_verify_bootloader_with_keys = 0x4000089c
                0x400008a0                        ets_secure_boot_verify_signature = 0x400008a0
                0x400008a4                        ets_secure_boot_read_key_digests = 0x400008a4
                0x3fcdffc9                        g_uart_print = 0x3fcdffc9
                0x3fcdffc8                        g_usb_print = 0x3fcdffc8
                0x40001ad4                        esp_pp_rom_version_get = 0x40001ad4
                0x40001ad8                        RC_GetBlockAckTime = 0x40001ad8
                0x40001adc                        ebuf_list_remove = 0x40001adc
                0x40001aec                        GetAccess = 0x40001aec
                0x40001af0                        hal_mac_is_low_rate_enabled = 0x40001af0
                0x40001af4                        hal_mac_tx_get_blockack = 0x40001af4
                0x40001afc                        ic_get_trc = 0x40001afc
                0x40001b04                        ic_mac_init = 0x40001b04
                0x40001b08                        ic_interface_enabled = 0x40001b08
                0x40001b0c                        is_lmac_idle = 0x40001b0c
                0x40001b14                        lmacDiscardAgedMSDU = 0x40001b14
                0x40001b20                        lmacIsIdle = 0x40001b20
                0x40001b24                        lmacIsLongFrame = 0x40001b24
                0x40001b2c                        lmacPostTxComplete = 0x40001b2c
                0x40001b30                        lmacProcessAllTxTimeout = 0x40001b30
                0x40001b34                        lmacProcessCollisions = 0x40001b34
                0x40001b38                        lmacProcessRxSucData = 0x40001b38
                0x40001b3c                        lmacReachLongLimit = 0x40001b3c
                0x40001b40                        lmacReachShortLimit = 0x40001b40
                0x40001b44                        lmacRecycleMPDU = 0x40001b44
                0x40001b48                        lmacRxDone = 0x40001b48
                0x40001b50                        lmacTxDone = 0x40001b50
                0x40001b54                        lmacTxFrame = 0x40001b54
                0x40001b58                        mac_tx_set_duration = 0x40001b58
                0x40001b5c                        mac_tx_set_htsig = 0x40001b5c
                0x40001b60                        mac_tx_set_plcp0 = 0x40001b60
                0x40001b64                        mac_tx_set_plcp1 = 0x40001b64
                0x40001b68                        mac_tx_set_plcp2 = 0x40001b68
                0x40001b70                        pm_disable_dream_timer = 0x40001b70
                0x40001b74                        pm_disable_sleep_delay_timer = 0x40001b74
                0x40001b7c                        pm_mac_wakeup = 0x40001b7c
                0x40001b80                        pm_mac_sleep = 0x40001b80
                0x40001b84                        pm_enable_active_timer = 0x40001b84
                0x40001b88                        pm_enable_sleep_delay_timer = 0x40001b88
                0x40001b8c                        pm_local_tsf_process = 0x40001b8c
                0x40001b90                        pm_set_beacon_filter = 0x40001b90
                0x40001b94                        pm_is_in_wifi_slice_threshold = 0x40001b94
                0x40001b98                        pm_is_waked = 0x40001b98
                0x40001ba4                        pm_on_data_rx = 0x40001ba4
                0x40001ba8                        pm_on_tbtt = 0x40001ba8
                0x40001bc0                        pm_sleep_for = 0x40001bc0
                0x40001bc8                        ppAMPDU2Normal = 0x40001bc8
                0x40001bd0                        ppCalFrameTimes = 0x40001bd0
                0x40001bd4                        ppCalSubFrameLength = 0x40001bd4
                0x40001bdc                        ppCheckTxAMPDUlength = 0x40001bdc
                0x40001be0                        ppDequeueRxq_Locked = 0x40001be0
                0x40001be4                        ppDequeueTxQ = 0x40001be4
                0x40001be8                        ppEmptyDelimiterLength = 0x40001be8
                0x40001bec                        ppEnqueueRxq = 0x40001bec
                0x40001bf0                        ppEnqueueTxDone = 0x40001bf0
                0x40001bf4                        ppGetTxQFirstAvail_Locked = 0x40001bf4
                0x40001bf8                        ppGetTxframe = 0x40001bf8
                0x40001bfc                        ppMapTxQueue = 0x40001bfc
                0x40001c00                        ppProcTxSecFrame = 0x40001c00
                0x40001c04                        ppProcessRxPktHdr = 0x40001c04
                0x40001c0c                        ppRecordBarRRC = 0x40001c0c
                0x40001c10                        lmacRequestTxopQueue = 0x40001c10
                0x40001c14                        lmacReleaseTxopQueue = 0x40001c14
                0x40001c18                        ppRecycleAmpdu = 0x40001c18
                0x40001c1c                        ppRecycleRxPkt = 0x40001c1c
                0x40001c20                        ppResortTxAMPDU = 0x40001c20
                0x40001c24                        ppResumeTxAMPDU = 0x40001c24
                0x40001c30                        ppRxProtoProc = 0x40001c30
                0x40001c34                        ppSearchTxQueue = 0x40001c34
                0x40001c38                        ppSearchTxframe = 0x40001c38
                0x40001c3c                        ppSelectNextQueue = 0x40001c3c
                0x40001c40                        ppSubFromAMPDU = 0x40001c40
                0x40001c44                        ppTask = 0x40001c44
                0x40001c48                        ppTxPkt = 0x40001c48
                0x40001c4c                        ppTxProtoProc = 0x40001c4c
                0x40001c50                        ppTxqUpdateBitmap = 0x40001c50
                0x40001c58                        pp_hdrsize = 0x40001c58
                0x40001c5c                        pp_post = 0x40001c5c
                0x40001c60                        pp_process_hmac_waiting_txq = 0x40001c60
                0x40001c64                        rcGetAmpduSched = 0x40001c64
                0x40001c68                        rcUpdateRxDone = 0x40001c68
                0x40001c6c                        rc_get_trc = 0x40001c6c
                0x40001c70                        rc_get_trc_by_index = 0x40001c70
                0x40001c74                        rcAmpduLowerRate = 0x40001c74
                0x40001c78                        rcampduuprate = 0x40001c78
                0x40001c7c                        rcClearCurAMPDUSched = 0x40001c7c
                0x40001c80                        rcClearCurSched = 0x40001c80
                0x40001c84                        rcClearCurStat = 0x40001c84
                0x40001c8c                        rcLowerSched = 0x40001c8c
                0x40001c90                        rcSetTxAmpduLimit = 0x40001c90
                0x40001c98                        rcUpdateAckSnr = 0x40001c98
                0x40001ca8                        rcUpSched = 0x40001ca8
                0x40001cac                        rssi_margin = 0x40001cac
                0x40001cb0                        rx11NRate2AMPDULimit = 0x40001cb0
                0x40001cb4                        TRC_AMPDU_PER_DOWN_THRESHOLD = 0x40001cb4
                0x40001cb8                        TRC_AMPDU_PER_UP_THRESHOLD = 0x40001cb8
                0x40001cbc                        trc_calc_duration = 0x40001cbc
                0x40001cc0                        trc_isTxAmpduOperational = 0x40001cc0
                0x40001cc4                        trc_onAmpduOp = 0x40001cc4
                0x40001cc8                        TRC_PER_IS_GOOD = 0x40001cc8
                0x40001ccc                        trc_SetTxAmpduState = 0x40001ccc
                0x40001cd0                        trc_tid_isTxAmpduOperational = 0x40001cd0
                0x40001cd4                        trcAmpduSetState = 0x40001cd4
                0x40001cd8                        wDevCheckBlockError = 0x40001cd8
                0x40001ce0                        wDev_DiscardFrame = 0x40001ce0
                0x40001ce4                        wDev_GetNoiseFloor = 0x40001ce4
                0x40001ce8                        wDev_IndicateAmpdu = 0x40001ce8
                0x40001cf0                        wdev_mac_reg_load = 0x40001cf0
                0x40001cf4                        wdev_mac_reg_store = 0x40001cf4
                0x40001cf8                        wdev_mac_special_reg_load = 0x40001cf8
                0x40001cfc                        wdev_mac_special_reg_store = 0x40001cfc
                0x40001d00                        wdev_mac_wakeup = 0x40001d00
                0x40001d04                        wdev_mac_sleep = 0x40001d04
                0x40001d10                        wdevProcessRxSucDataAll = 0x40001d10
                0x40001d14                        wdev_csi_len_align = 0x40001d14
                0x40001d18                        ppDequeueTxDone_Locked = 0x40001d18
                0x40001d24                        config_is_cache_tx_buf_enabled = 0x40001d24
                0x40001d28                        //ppMapWaitTxq = 0x40001d28
                0x40001d2c                        ppProcessWaitingQueue = 0x40001d2c
                0x40001d30                        ppDisableQueue = 0x40001d30
                0x40001d34                        pm_allow_tx = 0x40001d34
                0x40001d38                        wdev_is_data_in_rxlist = 0x40001d38
                0x40001d3c                        ppProcTxCallback = 0x40001d3c
                0x40001d40                        pm_is_open = 0x40001d40
                0x40001d44                        pm_wake_up = 0x40001d44
                0x40001d48                        pm_wake_done = 0x40001d48
                0x40001d4c                        pm_disable_disconnected_sleep_delay_timer = 0x40001d4c
                0x40001d50                        pm_enable_disconnected_sleep_delay_timer = 0x40001d50
                0x40001d54                        hal_mac_get_txq_state = 0x40001d54
                0x40001d58                        hal_mac_clr_txq_state = 0x40001d58
                0x40001d5c                        hal_mac_tx_set_cca = 0x40001d5c
                0x40001d60                        hal_mac_set_txq_invalid = 0x40001d60
                0x40001d64                        hal_mac_txq_disable = 0x40001d64
                0x40001d68                        hal_mac_is_txq_enabled = 0x40001d68
                0x40001d6c                        hal_mac_get_txq_pmd = 0x40001d6c
                0x40001d80                        lmacProcessCollision = 0x40001d80
                0x40001d84                        lmacProcessTxRtsError = 0x40001d84
                0x40001d88                        lmacProcessCtsTimeout = 0x40001d88
                0x40001d90                        lmacProcessAckTimeout = 0x40001d90
                0x40001d94                        lmacProcessTxError = 0x40001d94
                0x40001d98                        lmacProcessTxseckiderr = 0x40001d98
                0x40001d9c                        rcReachRetryLimit = 0x40001d9c
                0x40001da0                        lmacProcessShortRetryFail = 0x40001da0
                0x40001da4                        lmacEndRetryAMPDUFail = 0x40001da4
                0x40001da8                        ppFillAMPDUBar = 0x40001da8
                0x40001dac                        rcGetRate = 0x40001dac
                0x40001db0                        ppReSendBar = 0x40001db0
                0x40001db4                        lmacProcessLongRetryFail = 0x40001db4
                0x40001db8                        lmacRetryTxFrame = 0x40001db8
                0x40001dbc                        lmacProcessCollisions_task = 0x40001dbc
                0x40001dc4                        lmacInitAc = 0x40001dc4
                0x40001dcc                        mac_tx_set_txop_q = 0x40001dcc
                0x40001dd4                        hal_mac_rx_set_policy = 0x40001dd4
                0x40001dd8                        hal_mac_set_bssid = 0x40001dd8
                0x40001ddc                        mac_rx_policy_init = 0x40001ddc
                0x40001de4                        mac_rxbuf_init = 0x40001de4
                0x40001de8                        mac_last_rxbuf_init = 0x40001de8
                0x40001dec                        hal_attenna_init = 0x40001dec
                0x40001df0                        hal_timer_update_by_rtc = 0x40001df0
                0x40001df4                        hal_coex_pti_init = 0x40001df4
                0x40001dfc                        ppDirectRecycleAmpdu = 0x40001dfc
                0x40001e00                        esp_wifi_internal_set_rts = 0x40001e00
                0x40001e04                        esp_wifi_internal_get_rts = 0x40001e04
                0x40001e10                        hal_agreement_add_rx_ba = 0x40001e10
                0x40001e14                        hal_agreement_del_rx_ba = 0x40001e14
                0x40001e1c                        hal_crypto_get_key_entry = 0x40001e1c
                0x40001e20                        hal_crypto_clr_key_entry = 0x40001e20
                0x40001e24                        config_get_wifi_task_stack_size = 0x40001e24
                0x40001e28                        pp_create_task = 0x40001e28
                0x40001e2c                        hal_set_sta_tsf_wakeup = 0x40001e2c
                0x40001e30                        hal_set_rx_beacon_pti = 0x40001e30
                0x40001e3c                        hal_disable_sta_tbtt = 0x40001e3c
                0x40001e40                        ppCalTxopDur = 0x40001e40
                0x40001e44                        wDev_IndicateCtrlFrame = 0x40001e44
                0x40001e48                        hal_enable_sta_tbtt = 0x40001e48
                0x40001e58                        wDev_Rxbuf_Init = 0x40001e58
                0x40001e5c                        wDev_Rxbuf_Deinit = 0x40001e5c
                0x40001e60                        ppCalTkipMic = 0x40001e60
                0x40001e64                        wDev_SnifferRxData = 0x40001e64
                0x40001e68                        hal_crypto_enable = 0x40001e68
                0x40001e6c                        hal_crypto_disable = 0x40001e6c
                0x40001e70                        wDev_Insert_KeyEntry = 0x40001e70
                0x40001e74                        wDev_remove_KeyEntry = 0x40001e74
                0x40001e78                        rc_enable_trc = 0x40001e78
                0x40001e7c                        rc_set_per_conn_fix_rate = 0x40001e7c
                0x40001e80                        wdev_csi_rx_process = 0x40001e80
                0x40001e84                        wDev_SnifferRxAmpdu = 0x40001e84
                0x40001e88                        hal_mac_tsf_reset = 0x40001e88
                0x40001e8c                        dbg_lmac_statis_dump = 0x40001e8c
                0x40001e90                        dbg_lmac_rxtx_statis_dump = 0x40001e90
                0x40001e94                        dbg_lmac_hw_statis_dump = 0x40001e94
                0x40001e98                        dbg_lmac_diag_statis_dump = 0x40001e98
                0x40001e9c                        dbg_lmac_ps_statis_dump = 0x40001e9c
                0x40001ea4                        rcUpdateAMPDUParam = 0x40001ea4
                0x40001ea8                        rcUpdatePhyMode = 0x40001ea8
                0x40001eac                        rcGetHighestRateIdx = 0x40001eac
                0x40001eb0                        //pm_tx_null_data_done_process = 0x40001eb0
                0x40001eb4                        //pm_tx_data_process = 0x40001eb4
                0x40001ec0                        ppInitTxq = 0x40001ec0
                0x40001ec4                        pp_attach = 0x40001ec4
                0x40001ec8                        pp_deattach = 0x40001ec8
                0x40001ecc                        //pm_on_probe_resp_rx = 0x40001ecc
                0x40001ed0                        hal_set_sta_tsf = 0x40001ed0
                0x40001ed4                        ic_update_sta_tsf = 0x40001ed4
                0x40001ed8                        ic_tx_pkt = 0x40001ed8
                0x40001edc                        //pm_send_probe_stop = 0x40001edc
                0x40001ee0                        pm_send_probe_start = 0x40001ee0
                0x40001ee4                        pm_on_coex_schm_process_restart = 0x40001ee4
                0x40001ee8                        hal_mac_set_rxq_policy = 0x40001ee8
                0x40001eec                        hal_sniffer_enable = 0x40001eec
                0x40001ef0                        hal_sniffer_disable = 0x40001ef0
                0x40001ef8                        hal_sniffer_rx_clr_statistics = 0x40001ef8
                0x40001f00                        tsf_hal_set_tsf_enable = 0x40001f00
                0x40001f04                        tsf_hal_set_tsf_disable = 0x40001f04
                0x40001f08                        tsf_hal_is_tsf_enabled = 0x40001f08
                0x40001f0c                        tsf_hal_set_modem_wakeup_early_time = 0x40001f0c
                0x40001f10                        tsf_hal_get_counter_value = 0x40001f10
                0x40001f14                        tsf_hal_set_counter_value = 0x40001f14
                0x40001f18                        tsf_hal_get_time = 0x40001f18
                0x40001f1c                        tsf_hal_set_time = 0x40001f1c
                0x40001f20                        tsf_hal_set_tbtt_enable = 0x40001f20
                0x40001f24                        tsf_hal_set_tbtt_disable = 0x40001f24
                0x40001f28                        tsf_hal_set_tbtt_intr_enable = 0x40001f28
                0x40001f2c                        tsf_hal_set_tbtt_intr_disable = 0x40001f2c
                0x40001f30                        tsf_hal_set_tbtt_soc_wakeup_enable = 0x40001f30
                0x40001f34                        tsf_hal_set_tbtt_soc_wakeup_disable = 0x40001f34
                0x40001f3c                        tsf_hal_set_tbtt_early_time = 0x40001f3c
                0x40001f40                        tsf_hal_set_tbtt_interval = 0x40001f40
                0x40001f44                        tsf_hal_get_tbtt_interval = 0x40001f44
                0x40001f48                        tsf_hal_set_timer_enable = 0x40001f48
                0x40001f4c                        tsf_hal_set_timer_disable = 0x40001f4c
                0x40001f50                        tsf_hal_set_timer_target = 0x40001f50
                0x40001f54                        tsf_hal_get_timer_target = 0x40001f54
                0x40001f58                        tsf_hal_set_timer_intr_enable = 0x40001f58
                0x40001f5c                        tsf_hal_set_timer_intr_disable = 0x40001f5c
                0x40001f60                        tsf_hal_set_timer_soc_wakeup_enable = 0x40001f60
                0x40001f64                        tsf_hal_set_timer_soc_wakeup_disable = 0x40001f64
                0x40001f68                        pm_disconnected_wake = 0x40001f68
                0x40001f6c                        pm_get_connectionless_status = 0x40001f6c
                0x40001f70                        pm_update_by_connectionless_status = 0x40001f70
                0x40001f74                        pm_connectionless_wake_interval_timeout_process = 0x40001f74
                0x40001f78                        pm_connectionless_wake_window_timeout_process = 0x40001f78
                0x3ff4fbbc                        our_instances_ptr = 0x3ff4fbbc
                0x3fcdfdec                        pTxRx = 0x3fcdfdec
                0x3fcdfde8                        lmacConfMib_ptr = 0x3fcdfde8
                0x3fcdfde4                        our_wait_eb = 0x3fcdfde4
                0x3fcdfde0                        our_tx_eb = 0x3fcdfde0
                0x3fcdfddc                        pp_wdev_funcs = 0x3fcdfddc
                0x3fcdfdd8                        g_osi_funcs_p = 0x3fcdfdd8
                0x3fcdfdd4                        wDevCtrl_ptr = 0x3fcdfdd4
                0x3ff4fbb8                        g_wdev_last_desc_reset_ptr = 0x3ff4fbb8
                0x3fcdfdd0                        wDevMacSleep_ptr = 0x3fcdfdd0
                0x3fcdfdcc                        g_lmac_cnt_ptr = 0x3fcdfdcc
                0x3ff4fbb4                        our_controls_ptr = 0x3ff4fbb4
                0x3fcdfdc8                        pp_sig_cnt_ptr = 0x3fcdfdc8
                0x3fcdfdc4                        g_eb_list_desc_ptr = 0x3fcdfdc4
                0x3fcdfdc0                        s_fragment_ptr = 0x3fcdfdc0
                0x3fcdfdbc                        if_ctrl_ptr = 0x3fcdfdbc
                0x3fcdfdb8                        g_intr_lock_mux = 0x3fcdfdb8
                0x3fcdfdb4                        g_wifi_global_lock = 0x3fcdfdb4
                0x3fcdfdb0                        s_wifi_queue = 0x3fcdfdb0
                0x3fcdfdac                        pp_task_hdl = 0x3fcdfdac
                0x3fcdfda8                        s_pp_task_create_sem = 0x3fcdfda8
                0x3fcdfda4                        s_pp_task_del_sem = 0x3fcdfda4
                0x3fcdfda0                        g_wifi_menuconfig_ptr = 0x3fcdfda0
                0x3fcdfd9c                        xphyQueue = 0x3fcdfd9c
                0x3fcdfd98                        ap_no_lr_ptr = 0x3fcdfd98
                0x3fcdfd94                        rc11BSchedTbl_ptr = 0x3fcdfd94
                0x3fcdfd90                        rc11NSchedTbl_ptr = 0x3fcdfd90
                0x3fcdfd8c                        rcLoRaSchedTbl_ptr = 0x3fcdfd8c
                0x3fcdfd88                        BasicOFDMSched_ptr = 0x3fcdfd88
                0x3fcdfd84                        trc_ctl_ptr = 0x3fcdfd84
                0x3fcdfd80                        g_pm_cnt_ptr = 0x3fcdfd80
                0x3fcdfd7c                        g_pm_ptr = 0x3fcdfd7c
                0x3fcdfd78                        g_pm_cfg_ptr = 0x3fcdfd78
                0x3fcdfd74                        g_esp_mesh_quick_funcs_ptr = 0x3fcdfd74
                0x3fcdfd70                        g_txop_queue_status_ptr = 0x3fcdfd70
                0x3fcdfd6c                        g_mac_sleep_en_ptr = 0x3fcdfd6c
                0x3fcdfd68                        g_mesh_is_root_ptr = 0x3fcdfd68
                0x3fcdfd64                        g_mesh_topology_ptr = 0x3fcdfd64
                0x3fcdfd60                        g_mesh_init_ps_type_ptr = 0x3fcdfd60
                0x3fcdfd5c                        g_mesh_is_started_ptr = 0x3fcdfd5c
                0x3fcdfd58                        g_config_func = 0x3fcdfd58
                0x3fcdfd54                        g_net80211_tx_func = 0x3fcdfd54
                0x3fcdfd50                        g_timer_func = 0x3fcdfd50
                0x3fcdfd4c                        s_michael_mic_failure_cb = 0x3fcdfd4c
                0x3fcdfd48                        wifi_sta_rx_probe_req = 0x3fcdfd48
                0x3fcdfd44                        g_tx_done_cb_func = 0x3fcdfd44
                0x3fcdfd28                        g_per_conn_trc = 0x3fcdfd28
                0x3fcdfd24                        s_encap_amsdu_func = 0x3fcdfd24
                0x3fcdfc84                        bars = 0x3fcdfc84
                0x3fcdfbf4                        eb_txdesc_space = 0x3fcdfbf4
                0x3fcdfb54                        eb_space = 0x3fcdfb54
                0x3fcdfb50                        g_pd_mac_in_light_sleep = 0x3fcdfb50
                0x3fcdfb4c                        s_fix_rate_mask = 0x3fcdfb4c
                0x3fcdfb44                        s_fix_rate = 0x3fcdfb44
                0x3fcdfb40                        g_wdev_csi_rx = 0x3fcdfb40
                0x3fcdfb3c                        g_wdev_csi_rx_ctx = 0x3fcdfb3c
                0x3fcdfb38                        BcnSendTick = 0x3fcdfb38
                0x3fcdfb34                        g_pp_timer_info_ptr = 0x3fcdfb34
                0x3fcdfb30                        rcP2P11NSchedTbl_ptr = 0x3fcdfb30
                0x3fcdfb2c                        rcP2P11GSchedTbl_ptr = 0x3fcdfb2c
                0x3fcdfb28                        rc11GSchedTbl_ptr = 0x3fcdfb28
                0x40001f7c                        esp_net80211_rom_version_get = 0x40001f7c
                0x40001f80                        ampdu_dispatch = 0x40001f80
                0x40001f84                        ampdu_dispatch_all = 0x40001f84
                0x40001f88                        ampdu_dispatch_as_many_as_possible = 0x40001f88
                0x40001f8c                        ampdu_dispatch_movement = 0x40001f8c
                0x40001f90                        ampdu_dispatch_upto = 0x40001f90
                0x40001f94                        chm_is_at_home_channel = 0x40001f94
                0x40001f98                        cnx_node_is_existing = 0x40001f98
                0x40001f9c                        cnx_node_search = 0x40001f9c
                0x40001fa0                        ic_ebuf_recycle_rx = 0x40001fa0
                0x40001fa4                        ic_ebuf_recycle_tx = 0x40001fa4
                0x40001fa8                        ic_reset_rx_ba = 0x40001fa8
                0x40001fac                        ieee80211_align_eb = 0x40001fac
                0x40001fb4                        ieee80211_ampdu_start_age_timer = 0x40001fb4
                0x40001fbc                        ieee80211_is_tx_allowed = 0x40001fbc
                0x40001fc0                        ieee80211_output_pending_eb = 0x40001fc0
                0x40001fc8                        ieee80211_set_tx_desc = 0x40001fc8
                0x40001fd0                        wifi_get_macaddr = 0x40001fd0
                0x40001fd4                        wifi_rf_phy_disable = 0x40001fd4
                0x40001fd8                        wifi_rf_phy_enable = 0x40001fd8
                0x40001fdc                        ic_ebuf_alloc = 0x40001fdc
                0x40001fe4                        ieee80211_copy_eb_header = 0x40001fe4
                0x40001fe8                        ieee80211_recycle_cache_eb = 0x40001fe8
                0x40001fec                        ieee80211_search_node = 0x40001fec
                0x40001ff0                        roundup2 = 0x40001ff0
                0x40001ff4                        ieee80211_crypto_encap = 0x40001ff4
                0x40001ffc                        ieee80211_decap = 0x40001ffc
                0x40002000                        ieee80211_set_tx_pti = 0x40002000
                0x40002004                        wifi_is_started = 0x40002004
                0x40002008                        ieee80211_gettid = 0x40002008
                0x40002014                        ccmp_encap = 0x40002014
                0x40002018                        ccmp_decap = 0x40002018
                0x4000201c                        tkip_encap = 0x4000201c
                0x40002020                        tkip_decap = 0x40002020
                0x40002024                        wep_encap = 0x40002024
                0x40002028                        wep_decap = 0x40002028
                0x4000202c                        dbg_hmac_rxtx_statis_dump = 0x4000202c
                0x40002030                        dbg_hmac_statis_dump = 0x40002030
                0x40002048                        ieee80211_vnd_lora_ie_size = 0x40002048
                0x4000204c                        ieee80211_vnd_ie_size = 0x4000204c
                0x40002050                        ieee80211_add_ssid = 0x40002050
                0x40002054                        ieee80211_add_rates = 0x40002054
                0x4000205c                        ieee80211_is_ht_cipher = 0x4000205c
                0x40002068                        ieee80211_setup_lr_rates = 0x40002068
                0x4000206c                        ieee80211_ht_node_init = 0x4000206c
                0x40002070                        ieee80211_is_support_rate = 0x40002070
                0x40002074                        ieee80211_setup_rates = 0x40002074
                0x40002078                        ieee80211_is_lr_only = 0x40002078
                0x4000207c                        ieee80211_setup_phy_mode = 0x4000207c
                0x40002080                        ieee80211_sta_is_connected = 0x40002080
                0x40002084                        current_task_is_wifi_task = 0x40002084
                0x40002088                        wifi_get_init_state = 0x40002088
                0x40002098                        ieee80211_send_setup = 0x40002098
                0x4000209c                        //ieee80211_send_probereq = 0x4000209c
                0x400020a4                        sta_auth_shared = 0x400020a4
                0x400020b0                        ieee80211_alloc_challenge = 0x400020b0
                0x400020b4                        cnx_assoc_timeout = 0x400020b4
                0x400020b8                        ieee80211_vnd_ie_set = 0x400020b8
                0x400020bc                        ieee80211_vnd_lora_ie_set = 0x400020bc
                0x400020c0                        ieee80211_add_wme_param = 0x400020c0
                0x400020c4                        ieee80211_add_dsparams = 0x400020c4
                0x400020c8                        ieee80211_add_csa = 0x400020c8
                0x400020d0                        ieee80211_regdomain_get_country = 0x400020d0
                0x400020d4                        ieee80211_add_countryie = 0x400020d4
                0x400020dc                        ieee80211_amsdu_adjust_head = 0x400020dc
                0x400020e0                        ieee80211_amsdu_adjust_last_length = 0x400020e0
                0x400020e4                        ieee80211_amsdu_send_check = 0x400020e4
                0x400020e8                        ieee80211_amsdu_encap_check = 0x400020e8
                0x400020ec                        ieee80211_amsdu_length_check = 0x400020ec
                0x400020f0                        ieee80211_encap_amsdu = 0x400020f0
                0x400020f4                        ieee80211_output_raw_process = 0x400020f4
                0x400020fc                        ieee80211_raw_frame_sanity_check = 0x400020fc
                0x40002100                        ieee80211_crypto_aes_128_cmac_encrypt = 0x40002100
                0x40002108                        ieee80211_alloc_tx_buf = 0x40002108
                0x4000211c                        ieee80211_encap_null_data = 0x4000211c
                0x40002120                        //ieee80211_send_deauth_no_bss = 0x40002120
                0x40002124                        ieee80211_alloc_deauth = 0x40002124
                0x40002128                        ieee80211_send_proberesp = 0x40002128
                0x40002130                        ieee80211_getcapinfo = 0x40002130
                0x4000214c                        ieee80211_set_max_rate = 0x4000214c
                0x40002150                        ic_set_sta = 0x40002150
                0x40002158                        ieee80211_parse_wpa = 0x40002158
                0x40002160                        ieee80211_add_assoc_req_ies = 0x40002160
                0x40002164                        ieee80211_add_probe_req_ies = 0x40002164
                0x3fcdfb24                        net80211_funcs = 0x3fcdfb24
                0x3fcdfb20                        g_scan = 0x3fcdfb20
                0x3fcdfb1c                        g_chm = 0x3fcdfb1c
                0x3fcdfb18                        g_ic_ptr = 0x3fcdfb18
                0x3fcdfaf4                        g_hmac_cnt_ptr = 0x3fcdfaf4
                0x3fcdfb14                        g_tx_cacheq_ptr = 0x3fcdfb14
                0x3fcdfb10                        s_netstack_free = 0x3fcdfb10
                0x3fcdfb0c                        mesh_rxcb = 0x3fcdfb0c
                0x3fcdfb08                        sta_rxcb = 0x3fcdfb08
                0x3fcdfb04                        ccmp_ptr = 0x3fcdfb04
                0x3fcdfb00                        s_wifi_nvs_ptr = 0x3fcdfb00
                0x3fcdfafc                        tkip_ptr = 0x3fcdfafc
                0x3fcdfaf8                        wep_ptr = 0x3fcdfaf8
                0x3fcdfaf4                        g_hmac_cnt_ptr = 0x3fcdfaf4
                0x3fcdfaf0                        g_misc_nvs = 0x3fcdfaf0
                0x3fcdfac0                        s_wifi_init_state = 0x3fcdfac0
                0x3fcdfaec                        s_wifi_task_hdl = 0x3fcdfaec
                0x3fcdfae8                        in_rssi_adjust = 0x3fcdfae8
                0x3fcdfae0                        rssi_saved = 0x3fcdfae0
                0x3fcdfadc                        rssi_index = 0x3fcdfadc
                0x3fcdfad4                        g_sta_connected_flag = 0x3fcdfad4
                0x3fcdfad0                        wpa_crypto_funcs_ptr = 0x3fcdfad0
                0x3fcdfacc                        s_netstack_ref = 0x3fcdfacc
                0x3fcdfac8                        sta_csa_timer_ptr = 0x3fcdfac8
                0x40002168                        esp_coex_rom_version_get = 0x40002168
                0x4000216c                        coex_bt_release = 0x4000216c
                0x40002170                        coex_bt_request = 0x40002170
                0x40002174                        coex_core_ble_conn_dyn_prio_get = 0x40002174
                0x4000217c                        coex_core_pti_get = 0x4000217c
                0x40002180                        coex_core_release = 0x40002180
                0x40002184                        coex_core_request = 0x40002184
                0x40002188                        coex_core_status_get = 0x40002188
                0x40002190                        coex_event_duration_get = 0x40002190
                0x40002194                        coex_hw_timer_disable = 0x40002194
                0x40002198                        coex_hw_timer_enable = 0x40002198
                0x4000219c                        coex_hw_timer_set = 0x4000219c
                0x400021a0                        coex_schm_interval_set = 0x400021a0
                0x400021a4                        coex_schm_lock = 0x400021a4
                0x400021a8                        coex_schm_unlock = 0x400021a8
                0x400021b0                        coex_wifi_release = 0x400021b0
                0x400021b4                        esp_coex_ble_conn_dynamic_prio_get = 0x400021b4
                0x3fcdfabc                        coex_env_ptr = 0x3fcdfabc
                0x3fcdfab8                        coex_pti_tab_ptr = 0x3fcdfab8
                0x3fcdfab4                        coex_schm_env_ptr = 0x3fcdfab4
                0x3fcdfab0                        coexist_funcs = 0x3fcdfab0
                0x3fcdfaac                        g_coa_funcs_p = 0x3fcdfaac
                0x3fcdfaa8                        g_coex_param_ptr = 0x3fcdfaa8
                0x400021bc                        phy_param_addr = 0x400021bc
                0x400021c0                        phy_get_romfuncs = 0x400021c0
                0x400021c4                        chip729_phyrom_version = 0x400021c4
                0x400021c8                        chip729_phyrom_version_num = 0x400021c8
                0x400021cc                        rom_get_rc_dout = 0x400021cc
                0x400021d0                        rc_cal = 0x400021d0
                0x400021d4                        phy_analog_delay_cal = 0x400021d4
                0x400021d8                        phy_rx_rifs_en = 0x400021d8
                0x400021dc                        phy_current_level_set = 0x400021dc
                0x400021e0                        phy_bbpll_en_usb = 0x400021e0
                0x400021e4                        phy_bt_power_track = 0x400021e4
                0x400021ec                        bb_wdt_rst_enable = 0x400021ec
                0x400021f0                        bb_wdt_int_enable = 0x400021f0
                0x400021f4                        bb_wdt_timeout_clear = 0x400021f4
                0x400021f8                        bb_wdt_get_status = 0x400021f8
                0x400021fc                        rom_enter_critical_phy = 0x400021fc
                0x40002200                        rom_exit_critical_phy = 0x40002200
                0x40002204                        rom_bb_bss_cbw40 = 0x40002204
                0x40002208                        rom_set_chan_reg = 0x40002208
                0x4000220c                        abs_temp = 0x4000220c
                0x40002210                        set_chan_cal_interp = 0x40002210
                0x40002214                        loopback_mode_en = 0x40002214
                0x40002218                        get_data_sat = 0x40002218
                0x4000221c                        phy_byte_to_word = 0x4000221c
                0x40002220                        phy_get_rx_freq = 0x40002220
                0x40002224                        i2c_master_reset = 0x40002224
                0x40002228                        chan14_mic_enable = 0x40002228
                0x4000222c                        chan14_mic_cfg = 0x4000222c
                0x40002230                        set_adc_rand = 0x40002230
                0x40002234                        phy_set_most_tpw = 0x40002234
                0x40002238                        phy_get_most_tpw = 0x40002238
                0x4000223c                        esp_tx_state_out = 0x4000223c
                0x40002240                        phy_get_adc_rand = 0x40002240
                0x40002244                        phy_internal_delay = 0x40002244
                0x40002248                        phy_ftm_comp = 0x40002248
                0x4000224c                        phy_11p_set = 0x4000224c
                0x40002250                        phy_freq_mem_backup = 0x40002250
                0x40002254                        ant_dft_cfg = 0x40002254
                0x40002258                        ant_wifitx_cfg = 0x40002258
                0x4000225c                        ant_wifirx_cfg = 0x4000225c
                0x40002260                        ant_bttx_cfg = 0x40002260
                0x40002264                        ant_btrx_cfg = 0x40002264
                0x40002268                        phy_chan_dump_cfg = 0x40002268
                0x4000226c                        phy_enable_low_rate = 0x4000226c
                0x40002270                        phy_disable_low_rate = 0x40002270
                0x40002274                        phy_dig_reg_backup = 0x40002274
                0x40002278                        phy_chan_filt_set = 0x40002278
                0x4000227c                        phy_rx11blr_cfg = 0x4000227c
                0x40002280                        set_cca = 0x40002280
                0x40002284                        set_rx_sense = 0x40002284
                0x40002288                        rx_gain_force = 0x40002288
                0x4000228c                        rom_phy_en_hw_set_freq = 0x4000228c
                0x40002290                        rom_phy_dis_hw_set_freq = 0x40002290
                0x40002294                        wr_rf_freq_mem = 0x40002294
                0x40002298                        freq_i2c_write_set = 0x40002298
                0x4000229c                        write_pll_cap_mem = 0x4000229c
                0x400022a0                        pll_dac_mem_update = 0x400022a0
                0x400022a4                        pll_cap_mem_update = 0x400022a4
                0x400022a8                        get_rf_freq_cap = 0x400022a8
                0x400022ac                        get_rf_freq_init = 0x400022ac
                0x400022b0                        freq_get_i2c_data = 0x400022b0
                0x400022b4                        freq_i2c_data_write = 0x400022b4
                0x400022b8                        set_chan_freq_hw_init = 0x400022b8
                0x400022bc                        set_chan_freq_sw_start = 0x400022bc
                0x400022c0                        rom_get_i2c_read_mask = 0x400022c0
                0x400022c4                        rom_get_i2c_mst0_mask = 0x400022c4
                0x400022c8                        rom_get_i2c_hostid = 0x400022c8
                0x400022cc                        rom_chip_i2c_readReg_org = 0x400022cc
                0x400022d0                        rom_chip_i2c_readReg = 0x400022d0
                0x400022d4                        rom_i2c_paral_set_mst0 = 0x400022d4
                0x400022d8                        rom_i2c_paral_set_read = 0x400022d8
                0x400022dc                        rom_i2c_paral_read = 0x400022dc
                0x400022e0                        rom_i2c_paral_write = 0x400022e0
                0x400022e4                        rom_i2c_paral_write_num = 0x400022e4
                0x400022e8                        rom_i2c_paral_write_mask = 0x400022e8
                0x400022ec                        rom_i2c_readReg = 0x400022ec
                0x400022f0                        rom_chip_i2c_writeReg = 0x400022f0
                0x400022f4                        rom_i2c_writeReg = 0x400022f4
                0x400022f8                        rom_i2c_readReg_Mask = 0x400022f8
                0x400022fc                        rom_i2c_writeReg_Mask = 0x400022fc
                0x40002300                        rom_set_txcap_reg = 0x40002300
                0x40002304                        i2c_sar2_init_code = 0x40002304
                0x40002308                        phy_i2c_init1 = 0x40002308
                0x4000230c                        phy_i2c_init2 = 0x4000230c
                0x40002310                        phy_get_i2c_data = 0x40002310
                0x40002314                        bias_reg_set = 0x40002314
                0x40002318                        i2c_rc_cal_set = 0x40002318
                0x4000231c                        i2c_bbpll_set = 0x4000231c
                0x40002320                        rom_phy_xpd_rf = 0x40002320
                0x40002324                        phy_wakeup_init_rom = 0x40002324
                0x40002328                        register_chipv7_phy_init_param = 0x40002328
                0x4000232c                        phy_reg_init = 0x4000232c
                0x40002330                        phy_close_rf_rom = 0x40002330
                0x40002334                        rom_pbus_force_mode = 0x40002334
                0x40002338                        rom_pbus_rd_addr = 0x40002338
                0x4000233c                        rom_pbus_rd_shift = 0x4000233c
                0x40002340                        rom_pbus_force_test = 0x40002340
                0x40002344                        rom_pbus_rd = 0x40002344
                0x40002348                        rom_pbus_debugmode = 0x40002348
                0x4000234c                        rom_pbus_workmode = 0x4000234c
                0x40002350                        rom_pbus_set_rxgain = 0x40002350
                0x40002354                        rom_pbus_xpd_rx_off = 0x40002354
                0x40002358                        rom_pbus_xpd_rx_on = 0x40002358
                0x4000235c                        rom_pbus_xpd_tx_off = 0x4000235c
                0x40002360                        rom_pbus_xpd_tx_on = 0x40002360
                0x40002364                        rom_pbus_set_dco = 0x40002364
                0x40002368                        rom_set_loopback_gain = 0x40002368
                0x4000236c                        rom_txcal_debuge_mode = 0x4000236c
                0x40002370                        rom_txcal_work_mode = 0x40002370
                0x40002374                        set_pbus_mem = 0x40002374
                0x40002378                        rom_pwdet_sar2_init = 0x40002378
                0x4000237c                        rom_en_pwdet = 0x4000237c
                0x40002380                        rom_get_sar_sig_ref = 0x40002380
                0x40002384                        rom_pwdet_tone_start = 0x40002384
                0x40002388                        rom_get_tone_sar_dout = 0x40002388
                0x4000238c                        rom_get_fm_sar_dout = 0x4000238c
                0x40002390                        rom_txtone_linear_pwr = 0x40002390
                0x40002394                        rom_get_power_db = 0x40002394
                0x40002398                        rom_meas_tone_pwr_db = 0x40002398
                0x4000239c                        rom_pkdet_vol_start = 0x4000239c
                0x400023a0                        rom_read_sar_dout = 0x400023a0
                0x400023a4                        rom_read_sar2_code = 0x400023a4
                0x400023a8                        rom_get_sar2_vol = 0x400023a8
                0x400023ac                        rom_get_pll_vol = 0x400023ac
                0x400023b0                        rom_tx_pwctrl_bg_init = 0x400023b0
                0x400023b4                        rom_phy_pwdet_always_en = 0x400023b4
                0x400023b8                        rom_phy_pwdet_onetime_en = 0x400023b8
                0x400023bc                        linear_to_db = 0x400023bc
                0x400023c0                        rom_disable_agc = 0x400023c0
                0x400023c4                        rom_enable_agc = 0x400023c4
                0x400023c8                        rom_disable_wifi_agc = 0x400023c8
                0x400023cc                        rom_enable_wifi_agc = 0x400023cc
                0x400023d0                        rom_write_gain_mem = 0x400023d0
                0x400023d4                        rom_bb_bss_cbw40_dig = 0x400023d4
                0x400023d8                        rom_cbw2040_cfg = 0x400023d8
                0x400023dc                        rom_mac_tx_chan_offset = 0x400023dc
                0x400023e0                        rom_tx_paon_set = 0x400023e0
                0x400023e4                        rom_i2cmst_reg_init = 0x400023e4
                0x400023e8                        rom_bt_gain_offset = 0x400023e8
                0x400023ec                        rom_fe_reg_init = 0x400023ec
                0x400023f0                        rom_mac_enable_bb = 0x400023f0
                0x400023f4                        rom_bb_wdg_cfg = 0x400023f4
                0x400023f8                        rom_fe_txrx_reset = 0x400023f8
                0x400023fc                        rom_set_rx_comp = 0x400023fc
                0x40002400                        rom_write_chan_freq = 0x40002400
                0x40002404                        rom_agc_reg_init = 0x40002404
                0x40002408                        rom_bb_reg_init = 0x40002408
                0x4000240c                        rom_write_txrate_power_offset = 0x4000240c
                0x40002410                        rom_open_i2c_xpd = 0x40002410
                0x40002414                        rom_txiq_set_reg = 0x40002414
                0x40002418                        rom_rxiq_set_reg = 0x40002418
                0x4000241c                        rom_phy_bbpll_cal = 0x4000241c
                0x40002420                        phy_disable_cca = 0x40002420
                0x40002424                        phy_enable_cca = 0x40002424
                0x40002428                        force_txon = 0x40002428
                0x4000242c                        set_txclk_en = 0x4000242c
                0x40002430                        set_rxclk_en = 0x40002430
                0x40002434                        start_tx_tone_step = 0x40002434
                0x40002438                        stop_tx_tone = 0x40002438
                0x4000243c                        bb_wdg_test_en = 0x4000243c
                0x40002440                        noise_floor_auto_set = 0x40002440
                0x40002444                        read_hw_noisefloor = 0x40002444
                0x40002448                        iq_corr_enable = 0x40002448
                0x4000244c                        bt_tx_dig_gain = 0x4000244c
                0x40002450                        wifi_tx_dig_reg = 0x40002450
                0x40002454                        wifi_agc_sat_gain = 0x40002454
                0x40002458                        phy_ant_init = 0x40002458
                0x4000245c                        phy_set_bbfreq_init = 0x4000245c
                0x40002460                        wifi_fbw_sel = 0x40002460
                0x40002464                        phy_rx_sense_set = 0x40002464
                0x40002468                        tx_state_set = 0x40002468
                0x4000246c                        phy_close_pa = 0x4000246c
                0x40002470                        bt_filter_reg = 0x40002470
                0x40002474                        phy_freq_correct = 0x40002474
                0x40002478                        set_pbus_reg = 0x40002478
                0x4000247c                        wifi_rifs_mode_en = 0x4000247c
                0x40002480                        rfagc_disable = 0x40002480
                0x40002484                        rom_restart_cal = 0x40002484
                0x40002488                        rom_write_rfpll_sdm = 0x40002488
                0x4000248c                        rom_wait_rfpll_cal_end = 0x4000248c
                0x40002490                        rom_rfpll_set_freq = 0x40002490
                0x40002494                        rom_rfpll_cap_init_cal = 0x40002494
                0x40002498                        rom_set_rfpll_freq = 0x40002498
                0x4000249c                        rom_write_pll_cap = 0x4000249c
                0x400024a0                        rom_read_pll_cap = 0x400024a0
                0x400024a4                        mhz2ieee = 0x400024a4
                0x400024a8                        chan_to_freq = 0x400024a8
                0x400024ac                        set_rf_freq_offset = 0x400024ac
                0x400024b0                        set_channel_rfpll_freq = 0x400024b0
                0x400024b4                        rfpll_cap_correct = 0x400024b4
                0x400024b8                        phy_set_freq = 0x400024b8
                0x400024bc                        correct_rfpll_offset = 0x400024bc
                0x400024c0                        pll_vol_cal = 0x400024c0
                0x400024c4                        chip_v7_set_chan_misc = 0x400024c4
                0x400024c8                        chip_v7_set_chan = 0x400024c8
                0x400024cc                        chip_v7_set_chan_offset = 0x400024cc
                0x400024d0                        chip_v7_set_chan_ana = 0x400024d0
                0x400024d4                        set_chanfreq = 0x400024d4
                0x400024d8                        rom_rxiq_cover_mg_mp = 0x400024d8
                0x400024dc                        rom_rfcal_rxiq = 0x400024dc
                0x400024e0                        rom_get_rfcal_rxiq_data = 0x400024e0
                0x400024e4                        rom_pbus_rx_dco_cal = 0x400024e4
                0x400024e8                        rom_rxdc_est_min = 0x400024e8
                0x400024ec                        rom_pbus_rx_dco_cal_1step = 0x400024ec
                0x400024f0                        rom_set_lb_txiq = 0x400024f0
                0x400024f4                        rom_set_rx_gain_cal_iq = 0x400024f4
                0x400024f8                        rom_set_rx_gain_cal_dc = 0x400024f8
                0x400024fc                        iq_est_enable = 0x400024fc
                0x40002500                        iq_est_disable = 0x40002500
                0x40002504                        dc_iq_est = 0x40002504
                0x40002508                        set_cal_rxdc = 0x40002508
                0x4000250c                        rxiq_get_mis = 0x4000250c
                0x40002510                        spur_reg_write_one_tone = 0x40002510
                0x40002514                        spur_cal = 0x40002514
                0x40002518                        spur_coef_cfg = 0x40002518
                0x4000251c                        gen_rx_gain_table = 0x4000251c
                0x40002520                        wr_rx_gain_mem = 0x40002520
                0x40002524                        set_rx_gain_param = 0x40002524
                0x40002528                        set_rx_gain_table = 0x40002528
                0x4000252c                        rom_tester_wifi_cali = 0x4000252c
                0x40002530                        esp_recover_efuse_data = 0x40002530
                0x40002538                        rfpll_cap_track = 0x40002538
                0x4000253c                        phy_param_track = 0x4000253c
                0x40002540                        txpwr_correct = 0x40002540
                0x40002544                        txpwr_cal_track = 0x40002544
                0x4000254c                        bt_track_tx_power = 0x4000254c
                0x40002550                        wifi_track_tx_power = 0x40002550
                0x40002554                        rom_code_to_temp = 0x40002554
                0x40002558                        rom_tsens_index_to_dac = 0x40002558
                0x4000255c                        rom_tsens_index_to_offset = 0x4000255c
                0x40002560                        rom_tsens_dac_cal = 0x40002560
                0x40002564                        rom_tsens_code_read = 0x40002564
                0x40002568                        rom_tsens_temp_read = 0x40002568
                0x4000256c                        rom_temp_to_power = 0x4000256c
                0x40002570                        tsens_read_init = 0x40002570
                0x40002574                        get_temp_init = 0x40002574
                0x40002578                        rom_txiq_cover = 0x40002578
                0x4000257c                        rom_rfcal_txiq = 0x4000257c
                0x40002580                        rom_get_power_atten = 0x40002580
                0x40002584                        rom_tx_pwctrl_init_cal = 0x40002584
                0x40002588                        bt_txdc_cal = 0x40002588
                0x4000258c                        bt_txiq_cal = 0x4000258c
                0x40002590                        txiq_cal_init = 0x40002590
                0x40002594                        txdc_cal_init = 0x40002594
                0x40002598                        txdc_cal_v70 = 0x40002598
                0x4000259c                        txiq_get_mis_pwr = 0x4000259c
                0x400025a0                        pwdet_ref_code = 0x400025a0
                0x400025a4                        pwdet_code_cal = 0x400025a4
                0x400025a8                        rfcal_txcap = 0x400025a8
                0x400025ac                        tx_cap_init = 0x400025ac
                0x400025b0                        rfcal_pwrctrl = 0x400025b0
                0x400025b4                        tx_pwctrl_init = 0x400025b4
                0x400025b8                        bt_tx_pwctrl_init = 0x400025b8
                0x400025bc                        bt_txpwr_freq = 0x400025bc
                0x400025c0                        rom_txbbgain_to_index = 0x400025c0
                0x400025c4                        rom_index_to_txbbgain = 0x400025c4
                0x400025c8                        rom_bt_index_to_bb = 0x400025c8
                0x400025cc                        rom_bt_bb_to_index = 0x400025cc
                0x400025d0                        rom_bt_get_tx_gain = 0x400025d0
                0x400025d4                        rom_get_tx_gain_value = 0x400025d4
                0x400025d8                        rom_wifi_get_tx_gain = 0x400025d8
                0x400025dc                        rom_set_tx_gain_mem = 0x400025dc
                0x400025e0                        rom_get_rate_fcc_index = 0x400025e0
                0x400025e4                        rom_get_chan_target_power = 0x400025e4
                0x400025e8                        rom_wifi_tx_dig_gain = 0x400025e8
                0x400025ec                        rom_wifi_set_tx_gain = 0x400025ec
                0x400025f0                        rom_bt_set_tx_gain = 0x400025f0
                0x400025f4                        wifi_11g_rate_chg = 0x400025f4
                0x400025f8                        bt_chan_pwr_interp = 0x400025f8
                0x400025fc                        bt_tx_gain_init = 0x400025fc
                0x3fcdfaa4                        phy_param_rom = 0x3fcdfaa4
                0x40002600                        bt_agc_gain_offset = 0x40002600
                0x40002604                        bt_agc_gain_max = 0x40002604
                0x40002608                        bt_set_rx_comp = 0x40002608
                0x4000260c                        bt_agc_gain_set = 0x4000260c
                0x40002610                        bt_agc_rssi_thresh = 0x40002610
                0x40002614                        bt_agc_target_set = 0x40002614
                0x40002618                        bt_agc_restart_set = 0x40002618
                0x4000261c                        bt_agc_recorrect_set = 0x4000261c
                0x40002620                        bt_agc_detect_set = 0x40002620
                0x40002624                        bt_bb_rx_correlator_set = 0x40002624
                0x40002628                        bt_bb_rx_dpo_set = 0x40002628
                0x4000262c                        bt_bb_rx_filter_sel = 0x4000262c
                0x40002630                        bt_bb_rx_set1 = 0x40002630
                0x40002634                        bt_bb_v2_rx_set = 0x40002634
                0x40002638                        bt_bb_v2_tx_set = 0x40002638
                0x4000263c                        bt_bb_tx_cca_set = 0x4000263c
                0x40002640                        bt_bb_tx_cca_period = 0x40002640
                0x40002644                        bt_bb_tx_cca_fifo_reset = 0x40002644
                0x40002648                        bt_bb_tx_cca_fifo_empty = 0x40002648
                0x4000264c                        bt_bb_tx_cca_fifo_full = 0x4000264c
                0x40002650                        bt_bb_tx_cca_fifo_count = 0x40002650
                0x40002654                        bt_bb_tx_cca_fifo_read = 0x40002654
                0x40002658                        coex_pti_v2 = 0x40002658
                0x4000265c                        bt_bb_set_le_tx_on_delay = 0x4000265c
                0x40002660                        bt_bb_set_corr_thresh_le = 0x40002660
                0x40002be4                        mbedtls_md5_starts_ret = 0x40002be4
                0x40002be8                        mbedtls_md5_update_ret = 0x40002be8
                0x40002bec                        mbedtls_md5_finish_ret = 0x40002bec
                0x400007fc                        PROVIDE (esp_rom_crc32_le = crc32_le)
                [!provide]                        PROVIDE (esp_rom_crc16_le = crc16_le)
                [!provide]                        PROVIDE (esp_rom_crc8_le = crc8_le)
                [!provide]                        PROVIDE (esp_rom_crc32_be = crc32_be)
                [!provide]                        PROVIDE (esp_rom_crc16_be = crc16_be)
                [!provide]                        PROVIDE (esp_rom_crc8_be = crc8_be)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_select_gpio = gpio_pad_select_gpio)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_pullup_only = gpio_pad_pullup)
                0x400007b8                        PROVIDE (esp_rom_gpio_pad_set_drv = gpio_pad_set_drv)
                [!provide]                        PROVIDE (esp_rom_gpio_pad_unhold = gpio_pad_unhold)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_in_signal = gpio_matrix_in)
                [!provide]                        PROVIDE (esp_rom_gpio_connect_out_signal = gpio_matrix_out)
                [!provide]                        PROVIDE (esp_rom_efuse_mac_address_crc8 = esp_crc8)
                [!provide]                        PROVIDE (esp_rom_efuse_is_secure_boot_enabled = ets_efuse_secure_boot_enabled)
                [!provide]                        PROVIDE (esp_rom_uart_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_uart_tx_one_char = uart_tx_one_char)
                [!provide]                        PROVIDE (esp_rom_uart_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_uart_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_uart_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_uart_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_uart_putc = ets_write_char_uart)
                0x40000070                        PROVIDE (esp_rom_output_flush_tx = uart_tx_flush)
                [!provide]                        PROVIDE (esp_rom_output_tx_one_char = uart_tx_one_char)
                0x40000074                        PROVIDE (esp_rom_output_tx_wait_idle = uart_tx_wait_idle)
                [!provide]                        PROVIDE (esp_rom_output_rx_one_char = uart_rx_one_char)
                [!provide]                        PROVIDE (esp_rom_output_rx_string = UartRxString)
                [!provide]                        PROVIDE (esp_rom_output_set_as_console = uart_tx_switch)
                [!provide]                        PROVIDE (esp_rom_output_putc = ets_write_char_uart)
                0x40002be4                        PROVIDE (esp_rom_mbedtls_md5_starts_ret = mbedtls_md5_starts_ret)
                0x40002be8                        PROVIDE (esp_rom_mbedtls_md5_update_ret = mbedtls_md5_update_ret)
                0x40002bec                        PROVIDE (esp_rom_mbedtls_md5_finish_ret = mbedtls_md5_finish_ret)
                0x40000088                        PROVIDE (esp_rom_software_reset_system = software_reset)
                [!provide]                        PROVIDE (esp_rom_software_reset_cpu = software_reset_cpu)
                0x40000034                        PROVIDE (esp_rom_printf = ets_printf)
                0x4000003c                        PROVIDE (esp_rom_install_uart_printf = ets_install_uart_printf)
                0x40000044                        PROVIDE (esp_rom_delay_us = ets_delay_us)
                0x40000018                        PROVIDE (esp_rom_get_reset_reason = rtc_get_reset_reason)
                [!provide]                        PROVIDE (esp_rom_route_intr_matrix = intr_matrix_set)
                0x40000770                        PROVIDE (esp_rom_get_cpu_ticks_per_us = ets_get_cpu_frequency)
                0x40000774                        PROVIDE (esp_rom_set_cpu_ticks_per_us = ets_update_cpu_frequency)
                [!provide]                        PROVIDE (esp_rom_spiflash_attach = spi_flash_attach)
                [!provide]                        PROVIDE (esp_rom_spiflash_clear_bp = esp_rom_spiflash_unlock)
                [!provide]                        PROVIDE (esp_rom_spiflash_write_enable = SPI_write_enable)
                [!provide]                        PROVIDE (esp_rom_spiflash_erase_area = SPIEraseArea)
                [!provide]                        PROVIDE (esp_rom_spiflash_fix_dummylen = spi_dummy_len_fix)
                [!provide]                        PROVIDE (esp_rom_spiflash_set_drvs = SetSpiDrvs)
                [!provide]                        PROVIDE (esp_rom_spiflash_select_padsfunc = SelectSpiFunction)
                [!provide]                        PROVIDE (esp_rom_spiflash_common_cmd = SPI_Common_Command)
                [!provide]                        PROVIDE (esp_rom_regi2c_read = rom_i2c_readReg)
                [!provide]                        PROVIDE (esp_rom_regi2c_read_mask = rom_i2c_readReg_Mask)
                0x400022f4                        PROVIDE (esp_rom_regi2c_write = rom_i2c_writeReg)
                0x400022fc                        PROVIDE (esp_rom_regi2c_write_mask = rom_i2c_writeReg_Mask)
                0x40000a18                        __adddf3 = 0x40000a18
                0x40000a1c                        __addsf3 = 0x40000a1c
                0x40000a20                        __divsf3 = 0x40000a20
                0x40000a24                        __eqdf2 = 0x40000a24
                0x40000a28                        __eqsf2 = 0x40000a28
                0x40000a2c                        __extendsfdf2 = 0x40000a2c
                0x40000a30                        __fixdfdi = 0x40000a30
                0x40000a34                        __fixdfsi = 0x40000a34
                0x40000a38                        __fixsfdi = 0x40000a38
                0x40000a3c                        __fixsfsi = 0x40000a3c
                0x40000a40                        __fixunsdfsi = 0x40000a40
                0x40000a44                        __fixunssfdi = 0x40000a44
                0x40000a48                        __fixunssfsi = 0x40000a48
                0x40000a4c                        __floatdidf = 0x40000a4c
                0x40000a50                        __floatdisf = 0x40000a50
                0x40000a54                        __floatsidf = 0x40000a54
                0x40000a58                        __floatsisf = 0x40000a58
                0x40000a5c                        __floatundidf = 0x40000a5c
                0x40000a60                        __floatundisf = 0x40000a60
                0x40000a64                        __floatunsidf = 0x40000a64
                0x40000a68                        __floatunsisf = 0x40000a68
                0x40000a6c                        __gedf2 = 0x40000a6c
                0x40000a70                        __gesf2 = 0x40000a70
                0x40000a74                        __gtdf2 = 0x40000a74
                0x40000a78                        __gtsf2 = 0x40000a78
                0x40000a7c                        __ledf2 = 0x40000a7c
                0x40000a80                        __lesf2 = 0x40000a80
                0x40000a84                        __ltdf2 = 0x40000a84
                0x40000a88                        __ltsf2 = 0x40000a88
                0x40000a8c                        __muldf3 = 0x40000a8c
                0x40000a90                        __mulsf3 = 0x40000a90
                0x40000a94                        __nedf2 = 0x40000a94
                0x40000a98                        __nesf2 = 0x40000a98
                0x40000a9c                        __subdf3 = 0x40000a9c
                0x40000aa0                        __subsf3 = 0x40000aa0
                0x40000aa4                        __truncdfsf2 = 0x40000aa4
                0x400008a8                        __absvdi2 = 0x400008a8
                0x400008ac                        __absvsi2 = 0x400008ac
                0x400008b8                        __addvdi3 = 0x400008b8
                0x400008bc                        __addvsi3 = 0x400008bc
                0x400008c0                        __ashldi3 = 0x400008c0
                0x400008c4                        __ashrdi3 = 0x400008c4
                0x400008c8                        __bswapdi2 = 0x400008c8
                0x400008cc                        __bswapsi2 = 0x400008cc
                0x400008d0                        __clear_cache = 0x400008d0
                0x400008d4                        __clrsbdi2 = 0x400008d4
                0x400008d8                        __clrsbsi2 = 0x400008d8
                0x400008dc                        __clzdi2 = 0x400008dc
                0x400008e0                        __clzsi2 = 0x400008e0
                0x400008e4                        __cmpdi2 = 0x400008e4
                0x400008e8                        __ctzdi2 = 0x400008e8
                0x400008ec                        __ctzsi2 = 0x400008ec
                0x400008f0                        __divdc3 = 0x400008f0
                0x400008f4                        __divdf3 = 0x400008f4
                0x400008f8                        __divdi3 = 0x400008f8
                0x400008fc                        __divsc3 = 0x400008fc
                0x40000904                        __divsi3 = 0x40000904
                0x40000914                        __ffsdi2 = 0x40000914
                0x40000918                        __ffssi2 = 0x40000918
                0x40000958                        __gcc_bcmp = 0x40000958
                0x40000974                        __lshrdi3 = 0x40000974
                0x40000980                        __moddi3 = 0x40000980
                0x40000984                        __modsi3 = 0x40000984
                0x40000988                        __muldc3 = 0x40000988
                0x40000990                        __muldi3 = 0x40000990
                0x40000994                        __mulsc3 = 0x40000994
                0x4000099c                        __mulsi3 = 0x4000099c
                0x400009a0                        __mulvdi3 = 0x400009a0
                0x400009a4                        __mulvsi3 = 0x400009a4
                0x400009ac                        __negdf2 = 0x400009ac
                0x400009b0                        __negdi2 = 0x400009b0
                0x400009b4                        __negsf2 = 0x400009b4
                0x400009b8                        __negvdi2 = 0x400009b8
                0x400009bc                        __negvsi2 = 0x400009bc
                0x400009c4                        __paritysi2 = 0x400009c4
                0x400009c8                        __popcountdi2 = 0x400009c8
                0x400009cc                        __popcountsi2 = 0x400009cc
                0x400009d0                        __powidf2 = 0x400009d0
                0x400009d4                        __powisf2 = 0x400009d4
                0x400009e0                        __subvdi3 = 0x400009e0
                0x400009e4                        __subvsi3 = 0x400009e4
                0x400009ec                        __ucmpdi2 = 0x400009ec
                0x400009f0                        __udivdi3 = 0x400009f0
                0x400009f4                        __udivmoddi4 = 0x400009f4
                0x400009f8                        __udivsi3 = 0x400009f8
                0x400009fc                        __udiv_w_sdiv = 0x400009fc
                0x40000a00                        __umoddi3 = 0x40000a00
                0x40000a04                        __umodsi3 = 0x40000a04
                0x40000a08                        __unorddf2 = 0x40000a08
                0x40000a0c                        __unordsf2 = 0x40000a0c
                0x40000a10                        __extenddftf2 = 0x40000a10
                0x40000a14                        __trunctfdf2 = 0x40000a14
                0x4000027c                        PROVIDE (wdt_hal_init = 0x4000027c)
                [!provide]                        PROVIDE (wdt_hal_deinit = 0x40000280)
                0x40000284                        PROVIDE (wdt_hal_config_stage = 0x40000284)
                0x40000288                        PROVIDE (wdt_hal_write_protect_disable = 0x40000288)
                0x4000028c                        PROVIDE (wdt_hal_write_protect_enable = 0x4000028c)
                0x40000290                        PROVIDE (wdt_hal_enable = 0x40000290)
                [!provide]                        PROVIDE (wdt_hal_disable = 0x40000294)
                [!provide]                        PROVIDE (wdt_hal_handle_intr = 0x40000298)
                [!provide]                        PROVIDE (wdt_hal_feed = 0x4000029c)
                0x400002a0                        PROVIDE (wdt_hal_set_flashboot_en = 0x400002a0)
                [!provide]                        PROVIDE (wdt_hal_is_enabled = 0x400002a4)
                [!provide]                        PROVIDE (systimer_hal_get_counter_value = 0x400002ac)
                [!provide]                        PROVIDE (systimer_hal_get_alarm_value = 0x400002bc)
                [!provide]                        PROVIDE (systimer_hal_enable_alarm_int = 0x400002c0)
                [!provide]                        PROVIDE (systimer_hal_enable_counter = 0x400002cc)
                [!provide]                        PROVIDE (systimer_hal_select_alarm_mode = 0x400002d0)
                [!provide]                        PROVIDE (systimer_hal_connect_alarm_counter = 0x400002d4)
                [!provide]                        PROVIDE (systimer_hal_counter_can_stall_by_cpu = 0x400002d8)
                0x40000010                        _rom_chip_id = 0x40000010
                0x40000014                        _rom_eco_version = 0x40000014
                0x40000484                        esp_rom_newlib_init_common_mutexes = 0x40000484
                0x40000488                        memset = 0x40000488
                0x4000048c                        memcpy = 0x4000048c
                0x40000490                        memmove = 0x40000490
                0x40000494                        memcmp = 0x40000494
                0x40000498                        strcpy = 0x40000498
                0x4000049c                        strncpy = 0x4000049c
                0x400004a0                        strcmp = 0x400004a0
                0x400004a4                        strncmp = 0x400004a4
                0x400004a8                        strlen = 0x400004a8
                0x400004ac                        strstr = 0x400004ac
                0x400004b0                        bzero = 0x400004b0
                0x400004b4                        _isatty_r = 0x400004b4
                0x400004b8                        sbrk = 0x400004b8
                0x400004bc                        isalnum = 0x400004bc
                0x400004c0                        isalpha = 0x400004c0
                0x400004c4                        isascii = 0x400004c4
                0x400004c8                        isblank = 0x400004c8
                0x400004cc                        iscntrl = 0x400004cc
                0x400004d0                        isdigit = 0x400004d0
                0x400004d4                        islower = 0x400004d4
                0x400004d8                        isgraph = 0x400004d8
                0x400004dc                        isprint = 0x400004dc
                0x400004e0                        ispunct = 0x400004e0
                0x400004e4                        isspace = 0x400004e4
                0x400004e8                        isupper = 0x400004e8
                0x400004ec                        toupper = 0x400004ec
                0x400004f0                        tolower = 0x400004f0
                0x400004f4                        toascii = 0x400004f4
                0x400004f8                        memccpy = 0x400004f8
                0x400004fc                        memchr = 0x400004fc
                0x40000500                        memrchr = 0x40000500
                0x40000504                        strcasecmp = 0x40000504
                0x40000508                        strcasestr = 0x40000508
                0x4000050c                        strcat = 0x4000050c
                0x40000510                        strdup = 0x40000510
                0x40000514                        strchr = 0x40000514
                0x40000518                        strcspn = 0x40000518
                0x4000051c                        strcoll = 0x4000051c
                0x40000520                        strlcat = 0x40000520
                0x40000524                        strlcpy = 0x40000524
                0x40000528                        strlwr = 0x40000528
                0x4000052c                        strncasecmp = 0x4000052c
                0x40000530                        strncat = 0x40000530
                0x40000534                        strndup = 0x40000534
                0x40000538                        strnlen = 0x40000538
                0x4000053c                        strrchr = 0x4000053c
                0x40000540                        strsep = 0x40000540
                0x40000544                        strspn = 0x40000544
                0x40000548                        strtok_r = 0x40000548
                0x4000054c                        strupr = 0x4000054c
                0x40000550                        longjmp = 0x40000550
                0x40000554                        setjmp = 0x40000554
                0x40000558                        abs = 0x40000558
                0x4000055c                        div = 0x4000055c
                0x40000560                        labs = 0x40000560
                0x40000564                        ldiv = 0x40000564
                0x40000568                        qsort = 0x40000568
                0x4000056c                        rand_r = 0x4000056c
                0x40000570                        rand = 0x40000570
                0x40000574                        srand = 0x40000574
                0x40000578                        utoa = 0x40000578
                0x4000057c                        itoa = 0x4000057c
                0x40000580                        atoi = 0x40000580
                0x40000584                        atol = 0x40000584
                0x40000588                        strtol = 0x40000588
                0x4000058c                        strtoul = 0x4000058c
                0x40000590                        fflush = 0x40000590
                0x40000594                        _fflush_r = 0x40000594
                0x40000598                        _fwalk = 0x40000598
                0x4000059c                        _fwalk_reent = 0x4000059c
                0x400005a0                        __smakebuf_r = 0x400005a0
                0x400005a4                        __swhatbuf_r = 0x400005a4
                0x400005a8                        __swbuf_r = 0x400005a8
                0x400005ac                        __swbuf = 0x400005ac
                0x400005b0                        __swsetup_r = 0x400005b0
                0x400005b4                        _strtod_l = 0x400005b4
                0x400005b8                        _strtod_r = 0x400005b8
                0x400005bc                        strtod_l = 0x400005bc
                0x400005c0                        strtod = 0x400005c0
                0x400005c4                        strtof_l = 0x400005c4
                0x400005c8                        strtof = 0x400005c8
                0x400005cc                        _strtol_r = 0x400005cc
                0x400005d0                        strtol_l = 0x400005d0
                0x400005d4                        _strtoul_r = 0x400005d4
                0x400005d8                        strtoul_l = 0x400005d8
                0x400005dc                        __match = 0x400005dc
                0x400005e0                        __hexnan = 0x400005e0
                0x400005e4                        __hexdig_fun = 0x400005e4
                0x400005e8                        __gethex = 0x400005e8
                0x400005ec                        _Balloc = 0x400005ec
                0x400005f0                        _Bfree = 0x400005f0
                0x400005f4                        __multadd = 0x400005f4
                0x400005f8                        __s2b = 0x400005f8
                0x400005fc                        __hi0bits = 0x400005fc
                0x40000600                        __lo0bits = 0x40000600
                0x40000604                        __i2b = 0x40000604
                0x40000608                        __multiply = 0x40000608
                0x4000060c                        __pow5mult = 0x4000060c
                0x40000610                        __lshift = 0x40000610
                0x40000614                        __mcmp = 0x40000614
                0x40000618                        __mdiff = 0x40000618
                0x4000061c                        __ulp = 0x4000061c
                0x40000620                        __b2d = 0x40000620
                0x40000624                        __d2b = 0x40000624
                0x40000628                        __ratio = 0x40000628
                0x4000062c                        _mprec_log10 = 0x4000062c
                0x40000630                        __copybits = 0x40000630
                0x40000634                        __any_on = 0x40000634
                0x40000638                        asctime = 0x40000638
                0x4000063c                        asctime_r = 0x4000063c
                0x40000640                        atof = 0x40000640
                0x40000644                        atoff = 0x40000644
                0x40000648                        _dtoa_r = 0x40000648
                0x4000064c                        _wctomb_r = 0x4000064c
                0x40000650                        __ascii_wctomb = 0x40000650
                0x40000654                        _mbtowc_r = 0x40000654
                0x40000658                        __ascii_mbtowc = 0x40000658
                0x4000065c                        puts = 0x4000065c
                0x40000660                        putc = 0x40000660
                0x40000664                        putchar = 0x40000664
                0x40000668                        nan = 0x40000668
                0x4000066c                        nanf = 0x4000066c
                0x40000670                        __errno = 0x40000670
                0x3fcdffd8                        syscall_table_ptr = 0x3fcdffd8
                0x3fcdffd4                        _global_impure_ptr = 0x3fcdffd4
                0x60000000                        PROVIDE (UART0 = 0x60000000)
                [!provide]                        PROVIDE (UART1 = 0x60010000)
                0x60002000                        PROVIDE (SPIMEM1 = 0x60002000)
                0x60003000                        PROVIDE (SPIMEM0 = 0x60003000)
                [!provide]                        PROVIDE (GPIO = 0x60004000)
                0x60008000                        PROVIDE (RTCCNTL = 0x60008000)
                [!provide]                        PROVIDE (RTCIO = 0x60008400)
                0x60008800                        PROVIDE (EFUSE = 0x60008800)
                [!provide]                        PROVIDE (I2C0 = 0x60013000)
                [!provide]                        PROVIDE (LEDC = 0x60019000)
                0x6001f000                        PROVIDE (TIMERG0 = 0x6001f000)
                [!provide]                        PROVIDE (SYSTIMER = 0x60023000)
                [!provide]                        PROVIDE (GPSPI2 = 0x60024000)
                [!provide]                        PROVIDE (SYSCON = 0x60026000)
                [!provide]                        PROVIDE (APB_SARADC = 0x60040000)
                [!provide]                        PROVIDE (GDMA = 0x6003f000)
                [!provide]                        PROVIDE (SYSTEM = 0x600c0000)
                0x006e0000                        iram_dram_offset = 0x6e0000
                0x3fcdcb70                        bootloader_usable_dram_end = 0x3fcdcb70
                0x00002000                        bootloader_stack_overhead = 0x2000
                0x00005000                        bootloader_dram_seg_len = 0x5000
                0x00007000                        bootloader_iram_loader_seg_len = 0x7000
                0x00002000                        bootloader_iram_seg_len = 0x2000
                0x3fcdab70                        bootloader_dram_seg_end = (bootloader_usable_dram_end - bootloader_stack_overhead)
                0x3fcd5b70                        bootloader_dram_seg_start = (bootloader_dram_seg_end - bootloader_dram_seg_len)
                0x403aeb70                        bootloader_iram_loader_seg_start = ((bootloader_dram_seg_start - bootloader_iram_loader_seg_len) + iram_dram_offset)
                0x403acb70                        bootloader_iram_seg_start = (bootloader_iram_loader_seg_start - bootloader_iram_seg_len)
                0x00000001                        ASSERT ((bootloader_iram_loader_seg_start == 0x403aeb70), bootloader_iram_loader_seg_start inconsistent with SRAM_DRAM_END)

.iram_loader.text
                0x403aeb70     0x2908
                0x403aeb70                        . = ALIGN (0x10)
                0x403aeb70                        _loader_text_start = ABSOLUTE (.)
 *(.stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 *(.iram1 .iram1.*)
 .iram1.0       0x403aeb70        0x2 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                0x403aeb70                esp_flash_encryption_enabled
 .iram1.1       0x403aeb72      0x1b0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403aeb72                bootloader_flash_execute_command_common
 .iram1.2       0x403aed22        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403aed22                bootloader_execute_flash_command
 .iram1.0       0x403aed30      0x15a esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403aed30                bootloader_flash_unlock
 .iram1.4       0x403aee8a       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403aee8a                bootloader_read_flash_id
 .iram1.0       0x403aeebe       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                0x403aeebe                bootloader_flash_cs_timing_config
 .iram1.2       0x403aeede       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                0x403aeede                bootloader_flash_set_dummy_out
 .iram1.4       0x403aeefa       0x56 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                0x403aeefa                bootloader_configure_spi_pins
 .iram1.0       0x403aef50       0x1e esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403aef50                efuse_hal_chip_revision
 .iram1.1       0x403aef6e       0x20 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403aef6e                efuse_hal_blk_version
 .iram1.2       0x403aef8e       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403aef8e                efuse_hal_get_disable_wafer_version_major
 .iram1.3       0x403aef9e       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403aef9e                efuse_hal_get_disable_blk_version_major
 .iram1.4       0x403aefae       0x22 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403aefae                efuse_hal_flash_encryption_enabled
 .iram1.0       0x403aefd0       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403aefd0                efuse_hal_get_major_chip_version
 .iram1.1       0x403aefe0       0x10 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                0x403aefe0                efuse_hal_get_minor_chip_version
 *liblog.a:(.literal .text .literal.* .text.*)
 .text.esp_log_early_timestamp
                0x403aeff0       0x26 esp-idf/log/liblog.a(log_noos.c.obj)
                0x403aeff0                esp_log_early_timestamp
                0x403aeff0                esp_log_timestamp
 *libgcc.a:(.literal .text .literal.* .text.*)
 *libclang_rt.builtins.a:(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_clock_loader.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_common_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_common_ota_select_crc
                0x403af016        0xe esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403af016                bootloader_common_ota_select_crc
 .text.bootloader_common_ota_select_invalid
                0x403af024       0x16 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403af024                bootloader_common_ota_select_invalid
 .text.bootloader_common_ota_select_valid
                0x403af03a       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403af03a                bootloader_common_ota_select_valid
 .text.bootloader_common_check_efuse_blk_validity
                0x403af064       0xb4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403af064                bootloader_common_check_efuse_blk_validity
 .text.bootloader_common_check_chip_validity
                0x403af118      0x118 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403af118                bootloader_common_check_chip_validity
 .text.bootloader_common_select_otadata
                0x403af230       0x3a esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403af230                bootloader_common_select_otadata
 .text.bootloader_common_get_active_otadata
                0x403af26a       0x2e esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                0x403af26a                bootloader_common_get_active_otadata
 *libbootloader_support.a:bootloader_flash.*(.literal .text .literal.* .text.*)
 .text.spi_to_esp_err
                0x403af298       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .text.bootloader_mmap_get_free_pages
                0x403af2ba        0x6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af2ba                bootloader_mmap_get_free_pages
 .text.bootloader_mmap
                0x403af2c0       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af2c0                bootloader_mmap
 .text.bootloader_munmap
                0x403af388       0x32 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af388                bootloader_munmap
 .text.bootloader_flash_read
                0x403af3ba      0x138 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af3ba                bootloader_flash_read
 .text.bootloader_flash_erase_sector
                0x403af4f2       0x12 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af4f2                bootloader_flash_erase_sector
 .text.bootloader_flash_write
                0x403af504       0xca esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af504                bootloader_flash_write
 .text.bootloader_enable_wp
                0x403af5ce        0xc esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af5ce                bootloader_enable_wp
 .text.bootloader_flash_get_spi_mode
                0x403af5da       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                0x403af5da                bootloader_flash_get_spi_mode
 *libbootloader_support.a:bootloader_random.*(.literal .text .literal.* .text.*)
 .text.bootloader_fill_random
                0x403af618       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                0x403af618                bootloader_fill_random
 *libbootloader_support.a:bootloader_random*.*(.literal.bootloader_random_disable .text.bootloader_random_disable)
 .text.bootloader_random_disable
                0x403af688       0x86 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
                0x403af688                bootloader_random_disable
 *libbootloader_support.a:bootloader_efuse.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:bootloader_utility.*(.literal .text .literal.* .text.*)
 .text.log_invalid_app_partition
                0x403af70e       0x84 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.index_to_partition
                0x403af792       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.try_load_partition
                0x403af7d8       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.cache_ll_l1_enable_bus.constprop.0
                0x403af81c       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.set_actual_ota_seq
                0x403af84c       0xaa esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.load_image
                0x403af8f6      0x1aa esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .text.bootloader_common_read_otadata
                0x403afaa0       0xa2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403afaa0                bootloader_common_read_otadata
 .text.bootloader_utility_load_partition_table
                0x403afb42      0x234 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403afb42                bootloader_utility_load_partition_table
 .text.bootloader_utility_get_selected_boot_partition
                0x403afd76      0x112 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403afd76                bootloader_utility_get_selected_boot_partition
 .text.bootloader_reset
                0x403afe88       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403afe88                bootloader_reset
 .text.bootloader_utility_load_boot_image
                0x403afea4      0x11c esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403afea4                bootloader_utility_load_boot_image
 .text.bootloader_debug_buffer
                0x403affc0        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                0x403affc0                bootloader_debug_buffer
 *libbootloader_support.a:bootloader_sha.*(.literal .text .literal.* .text.*)
 .text.bootloader_sha256_start
                0x403affc2       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x403affc2                bootloader_sha256_start
 .text.bootloader_sha256_data
                0x403affee       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x403affee                bootloader_sha256_data
 .text.bootloader_sha256_finish
                0x403b0022       0x46 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                0x403b0022                bootloader_sha256_finish
 *libbootloader_support.a:bootloader_console_loader.*(.literal .text .literal.* .text.*)
 .text.bootloader_console_deinit
                0x403b0068        0xa esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                0x403b0068                bootloader_console_deinit
 *libbootloader_support.a:bootloader_panic.*(.literal .text .literal.* .text.*)
 .text.__assert_func
                0x403b0072       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x403b0072                __assert_func
 .text.unlikely.abort
                0x403b0092       0x2a esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                0x403b0092                abort
 *libbootloader_support.a:bootloader_soc.*(.literal .text .literal.* .text.*)
 .text.bootloader_ana_super_wdt_reset_config
                0x403b00bc       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x403b00bc                bootloader_ana_super_wdt_reset_config
 .text.bootloader_ana_bod_reset_config
                0x403b00e8       0x2e esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x403b00e8                bootloader_ana_bod_reset_config
 .text.bootloader_ana_clock_glitch_reset_config
                0x403b0116        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                0x403b0116                bootloader_ana_clock_glitch_reset_config
 *libbootloader_support.a:esp_image_format.*(.literal .text .literal.* .text.*)
 .text.should_map
                0x403b0118       0x1e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_util_regions_overlap
                0x403b0136       0x4e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.process_segments
                0x403b0184      0x4a2 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.image_load
                0x403b0626      0x3fe esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .text.bootloader_load_image
                0x403b0a24        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                0x403b0a24                bootloader_load_image
 *libbootloader_support.a:flash_encrypt.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_encryption_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:flash_partitions.*(.literal .text .literal.* .text.*)
 .text.esp_partition_table_verify
                0x403b0a2c      0x17e esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                0x403b0a2c                esp_partition_table_verify
 *libbootloader_support.a:secure_boot.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_secure_features.*(.literal .text .literal.* .text.*)
 *libbootloader_support.a:secure_boot_signatures_bootloader.*(.literal .text .literal.* .text.*)
 *libmicro-ecc.a:*.*(.literal .text .literal.* .text.*)
 *libspi_flash.a:*.*(.literal .text .literal.* .text.*)
 *libhal.a:wdt_hal_iram.*(.literal .text .literal.* .text.*)
 *libhal.a:mmu_hal.*(.literal .text .literal.* .text.*)
 .text.mmu_ll_check_valid_paddr_region.isra.0
                0x403b0baa       0x74 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .text.mmu_hal_unmap_all
                0x403b0c1e       0x1a esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403b0c1e                mmu_hal_unmap_all
 .text.mmu_hal_init
                0x403b0c38       0x24 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403b0c38                mmu_hal_init
 .text.mmu_hal_pages_to_bytes
                0x403b0c5c       0x22 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403b0c5c                mmu_hal_pages_to_bytes
 .text.mmu_hal_check_valid_ext_vaddr_region
                0x403b0c7e       0x48 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403b0c7e                mmu_hal_check_valid_ext_vaddr_region
 .text.mmu_hal_map_region
                0x403b0cc6       0xde esp-idf/hal/libhal.a(mmu_hal.c.obj)
                0x403b0cc6                mmu_hal_map_region
 *libhal.a:cache_hal.*(.literal .text .literal.* .text.*)
 .text.cache_hal_init
                0x403b0da4       0x2c esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x403b0da4                cache_hal_init
 .text.cache_hal_disable
                0x403b0dd0       0x16 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x403b0dd0                cache_hal_disable
 .text.cache_hal_enable
                0x403b0de6       0x18 esp-idf/hal/libhal.a(cache_hal.c.obj)
                0x403b0de6                cache_hal_enable
 *libhal.a:efuse_hal.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:rtc_clk.*(.literal .text .literal.* .text.*)
 .text.rtc_clk_bbpll_disable
                0x403b0dfe       0x16 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_cpu_freq_to_pll_mhz
                0x403b0e14       0x64 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .text.rtc_clk_8m_enable
                0x403b0e78       0x76 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b0e78                rtc_clk_8m_enable
 .text.rtc_clk_slow_src_set
                0x403b0eee       0x68 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b0eee                rtc_clk_slow_src_set
 .text.rtc_clk_slow_src_get
                0x403b0f56       0x1a esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b0f56                rtc_clk_slow_src_get
 .text.rtc_clk_slow_freq_get_hz
                0x403b0f70       0x2e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b0f70                rtc_clk_slow_freq_get_hz
 .text.rtc_clk_fast_src_set
                0x403b0f9e       0x38 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b0f9e                rtc_clk_fast_src_set
 .text.rtc_clk_fast_src_get
                0x403b0fd6        0xc esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b0fd6                rtc_clk_fast_src_get
 .text.rtc_clk_xtal_freq_get
                0x403b0fe2       0x5c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b0fe2                rtc_clk_xtal_freq_get
                0x403b0fe2                rtc_get_xtal
 .text.rtc_clk_cpu_freq_mhz_to_config
                0x403b103e       0x60 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b103e                rtc_clk_cpu_freq_mhz_to_config
 .text.rtc_clk_cpu_freq_get_config
                0x403b109e       0xa0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b109e                rtc_clk_cpu_freq_get_config
 .text.rtc_clk_xtal_freq_update
                0x403b113e       0x2e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b113e                rtc_clk_xtal_freq_update
 .text.rtc_clk_apb_freq_update
                0x403b116c       0x18 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b116c                rtc_clk_apb_freq_update
 .text.rtc_clk_cpu_freq_to_xtal
                0x403b1184       0x5c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b1184                rtc_clk_cpu_freq_to_xtal
 .text.rtc_clk_cpu_freq_set_config
                0x403b11e0      0x1f6 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b11e0                rtc_clk_cpu_freq_set_config
 .text.rtc_clk_apb_freq_get
                0x403b13d6       0x2e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b13d6                rtc_clk_apb_freq_get
 .text.rtc_clk_divider_set
                0x403b1404       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b1404                rtc_clk_divider_set
 .text.rtc_clk_8m_divider_set
                0x403b1444       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                0x403b1444                rtc_clk_8m_divider_set
 *libesp_hw_support.a:rtc_time.*(.literal .text .literal.* .text.*)
 *libesp_hw_support.a:regi2c_ctrl.*(.literal .text .literal.* .text.*)
 *libefuse.a:*.*(.literal .text .literal.* .text.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x403b1478                        _loader_text_end = ABSOLUTE (.)

.iram.text      0x403acb70        0x0
                0x403acb70                        . = ALIGN (0x10)
 *(.entry.text)
 *(.init.literal)
 *(.init)

.dram0.bss      0x3fcd5b70      0x108
                0x3fcd5b70                        . = ALIGN (0x8)
                0x3fcd5b70                        _dram_start = ABSOLUTE (.)
                0x3fcd5b70                        _bss_start = ABSOLUTE (.)
 *(.dynsbss)
 *(.sbss)
 *(.sbss.*)
 .sbss.ota_has_initial_contents
                0x3fcd5b70        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 *fill*         0x3fcd5b71        0x3 
 .sbss.ram_obfs_value
                0x3fcd5b74        0x8 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .sbss.mapped   0x3fcd5b7c        0x1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x3fcd5b7d        0x3 
 .sbss.s_cur_pll_freq
                0x3fcd5b80        0x4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 *(.gnu.linkonce.sb.*)
 *(.scommon)
 *(.sbss2)
 *(.sbss2.*)
 *(.gnu.linkonce.sb2.*)
 *(.dynbss)
 *(.bss)
 *(.bss.*)
 .bss.ctx       0x3fcd5b84       0xd8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .bss.bootloader_image_hdr
                0x3fcd5c5c       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x3fcd5c5c                bootloader_image_hdr
 *(.gnu.linkonce.b.*)
 *(COMMON)
                0x3fcd5c78                        . = ALIGN (0x8)
 *fill*         0x3fcd5c74        0x4 
                0x3fcd5c78                        _bss_end = ABSOLUTE (.)

.dram0.bootdesc
                0x3fcd5c80       0x50
                0x3fcd5c80                        _data_start = ABSOLUTE (.)
 *(.data_bootloader_desc .data_bootloader_desc.*)
 .data_bootloader_desc
                0x3fcd5c80       0x50 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x3fcd5c80                esp_bootloader_desc

.dram0.data     0x3fcd5cd0        0x4
 *(.dram1 .dram1.*)
 *(.data)
 *(.data.*)
 *(.gnu.linkonce.d.*)
 *(.data1)
 *(.sdata)
 *(.sdata.*)
 .sdata.current_read_mapping
                0x3fcd5cd0        0x4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *(.gnu.linkonce.s.*)
 *(.gnu.linkonce.s2.*)
 *(.jcr)
                0x3fcd5cd4                        _data_end = ABSOLUTE (.)

.dram0.rodata   0x3fcd5cd4     0x1784
                0x3fcd5cd4                        _rodata_start = ABSOLUTE (.)
 *(.rodata)
 *(.rodata.*)
 .rodata.__assert_func.str1.4
                0x3fcd5cd4     0x16c8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                 0x22 (size before relaxing)
 .rodata.abort.str1.4
                0x3fcd739c       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .rodata.call_start_cpu0.str1.4
                0x3fcd739c       0x3c esp-idf/main/libmain.a(bootloader_start.c.obj)
 .rodata.log_invalid_app_partition.str1.4
                0x3fcd739c       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.try_load_partition.str1.4
                0x3fcd739c       0x42 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.set_actual_ota_seq.str1.4
                0x3fcd739c       0x85 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.load_image.str1.4
                0x3fcd739c       0xad esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_common_read_otadata.str1.4
                0x3fcd739c       0x91 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_partition_table.str1.4
                0x3fcd739c      0x1df esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_get_selected_boot_partition.str1.4
                0x3fcd739c      0x113 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.bootloader_utility_load_boot_image.str1.4
                0x3fcd739c       0xe2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .rodata.esp_partition_table_verify.str1.4
                0x3fcd739c      0x16c esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .rodata.bootloader_util_regions_overlap.str1.4
                0x3fcd739c       0x5e esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.process_segments.str1.4
                0x3fcd739c      0x2a9 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.image_load.str1.4
                0x3fcd739c      0x1da esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.0
                0x3fcd739c       0x20 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.__func__.1
                0x3fcd73bc       0x16 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .rodata.bootloader_sha256_data.str1.4
                0x3fcd73d2       0x51 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x3fcd73d2        0x2 
 .rodata.__func__.0
                0x3fcd73d4       0x19 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 *fill*         0x3fcd73ed        0x3 
 .rodata.__func__.1
                0x3fcd73f0       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .rodata.bootloader_init.str1.4
                0x3fcd7407       0xc7 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 *fill*         0x3fcd7407        0x1 
 .rodata.__func__.0
                0x3fcd7408       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .rodata.bootloader_common_check_efuse_blk_validity.str1.4
                0x3fcd7418       0xbb esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_common_check_chip_validity.str1.4
                0x3fcd7418       0xde esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .rodata.bootloader_fill_random.str1.4
                0x3fcd7418       0x4c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.__func__.0
                0x3fcd7418       0x17 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .rodata.bootloader_mmap.str1.4
                0x3fcd742f       0xac esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_read.str1.4
                0x3fcd742f       0xe7 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.str1.4
                0x3fcd742f       0xdf esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_flash_write.str1.4
                0x3fcd742f       0xef esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 *fill*         0x3fcd742f        0x1 
 .rodata.__func__.1
                0x3fcd7430       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .rodata.bootloader_init_spi_flash.str1.4
                0x3fcd7458      0x12c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .rodata.bootloader_read_bootloader_header.str1.4
                0x3fcd7458       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_check_bootloader_validity.str1.4
                0x3fcd7458       0x65 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_enable_random.str1.4
                0x3fcd7458       0x3d esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.bootloader_print_banner.str1.4
                0x3fcd7458       0x60 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .rodata.rtc_clk_init.str1.4
                0x3fcd7458       0x44 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .rodata.rtc_clk_xtal_freq_get.str1.4
                0x3fcd7458       0x4e esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .rodata.rtc_clk_cpu_freq_get_config.str1.4
                0x3fcd7458       0x3c esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 *(.gnu.linkonce.r.*)
 *(.rodata1)
 *(.sdata2 .sdata2.* .srodata .srodata.*)
                0x3fcd7458                        __XT_EXCEPTION_TABLE_ = ABSOLUTE (.)
 *(.xt_except_table)
 *(.gcc_except_table)
 *(.gnu.linkonce.e.*)
 *(.gnu.version_r)
 *(.eh_frame_hdr)
 *(.eh_frame)
                0x3fcd7584                        . = ((. + 0x3) & 0xfffffffffffffffc)
                0x3fcd7458                        __init_array_start = ABSOLUTE (.)
 *crtbegin.*(.ctors)
 *(EXCLUDE_FILE(*crtend.*) .ctors)
 *(SORT_BY_NAME(.ctors.*))
 *(.ctors)
                0x3fcd7458                        __init_array_end = ABSOLUTE (.)
 *crtbegin.*(.dtors)
 *(EXCLUDE_FILE(*crtend.*) .dtors)
 *(SORT_BY_NAME(.dtors.*))
 *(.dtors)
                0x3fcd7458                        __XT_EXCEPTION_DESCS_ = ABSOLUTE (.)
 *(.xt_except_desc)
 *(.gnu.linkonce.h.*)
                0x3fcd7458                        __XT_EXCEPTION_DESCS_END__ = ABSOLUTE (.)
 *(.xt_except_desc_end)
 *(.dynamic)
 *(.gnu.version_d)
                0x3fcd7458                        _rodata_end = ABSOLUTE (.)
                0x3fcd7458                        _lit4_start = ABSOLUTE (.)
 *(*.lit4)
 *(.lit4.*)
 *(.gnu.linkonce.lit4.*)
                0x3fcd7458                        _lit4_end = ABSOLUTE (.)
                0x3fcd7584                        . = ALIGN (0x4)
                0x3fcd7458                        _dram_end = ABSOLUTE (.)

.iram.text      0x403acb70      0xbda
                0x403acb70                        _stext = .
                0x403acb70                        _text_start = ABSOLUTE (.)
 *(.literal .text .literal.* .text.* .stub .gnu.warning .gnu.linkonce.literal.* .gnu.linkonce.t.*.literal .gnu.linkonce.t.*)
 .text.esp_bootloader_get_description
                0x403acb70        0xa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                0x403acb70                esp_bootloader_get_description
 .text.call_start_cpu0
                0x403acb7a       0x82 esp-idf/main/libmain.a(bootloader_start.c.obj)
                0x403acb7a                call_start_cpu0
 .text.bootloader_init
                0x403acbfc      0x128 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
                0x403acbfc                bootloader_init
 .text.bootloader_clock_configure
                0x403acd24       0xd6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                0x403acd24                bootloader_clock_configure
 .text.bootloader_init_mem
                0x403acdfa        0x2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                0x403acdfa                bootloader_init_mem
 .text.bootloader_random_enable
                0x403acdfc      0x1a2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
                0x403acdfc                bootloader_random_enable
 .text.bootloader_flash_update_id
                0x403acf9e       0x1c esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                0x403acf9e                bootloader_flash_update_id
 .text.bootloader_init_spi_flash
                0x403acfba      0x230 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                0x403acfba                bootloader_init_spi_flash
 .text.bootloader_clear_bss_section
                0x403ad1ea       0x22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ad1ea                bootloader_clear_bss_section
 .text.bootloader_read_bootloader_header
                0x403ad20c       0x3e esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ad20c                bootloader_read_bootloader_header
 .text.bootloader_check_bootloader_validity
                0x403ad24a       0x84 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ad24a                bootloader_check_bootloader_validity
 .text.bootloader_config_wdt
                0x403ad2ce       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ad2ce                bootloader_config_wdt
 .text.bootloader_enable_random
                0x403ad396       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ad396                bootloader_enable_random
 .text.bootloader_print_banner
                0x403ad3be       0x56 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                0x403ad3be                bootloader_print_banner
 .text.bootloader_console_init
                0x403ad414       0xb2 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                0x403ad414                bootloader_console_init
 .text.esp_cpu_configure_region_protection
                0x403ad4c6      0x134 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                0x403ad4c6                esp_cpu_configure_region_protection
 .text.rtc_clk_init
                0x403ad5fa      0x140 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                0x403ad5fa                rtc_clk_init
 *(.iram .iram.*)
 *(.fini.literal)
 *(.fini)
 *(.gnu.version)
                0x403ad74a                        . = (. + 0x10)
 *fill*         0x403ad73a       0x10 
                0x403ad74a                        _text_end = ABSOLUTE (.)
                0x403ad74a                        _etext = .

.riscv.attributes
                0x00000000       0x48
 *(.riscv.attributes)
 .riscv.attributes
                0x00000000       0x44 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .riscv.attributes
                0x00000044       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .riscv.attributes
                0x00000088       0x44 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .riscv.attributes
                0x000000cc       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .riscv.attributes
                0x00000110       0x44 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .riscv.attributes
                0x00000154       0x44 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .riscv.attributes
                0x00000198       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .riscv.attributes
                0x000001dc       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .riscv.attributes
                0x00000220       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .riscv.attributes
                0x00000264       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .riscv.attributes
                0x000002a8       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .riscv.attributes
                0x000002ec       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .riscv.attributes
                0x00000330       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .riscv.attributes
                0x00000374       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .riscv.attributes
                0x000003bc       0x44 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .riscv.attributes
                0x00000400       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .riscv.attributes
                0x00000444       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .riscv.attributes
                0x00000488       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .riscv.attributes
                0x000004cc       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .riscv.attributes
                0x00000510       0x44 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .riscv.attributes
                0x00000554       0x48 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .riscv.attributes
                0x0000059c       0x48 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .riscv.attributes
                0x000005e4       0x44 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .riscv.attributes
                0x00000628       0x48 esp-idf/log/liblog.a(log_noos.c.obj)
 .riscv.attributes
                0x00000670       0x44 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x000006b4       0x44 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .riscv.attributes
                0x000006f8       0x44 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .riscv.attributes
                0x0000073c       0x44 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug
 *(.debug)

.line
 *(.line)

.debug_srcinfo
 *(.debug_srcinfo)

.debug_sfnames
 *(.debug_sfnames)

.debug_aranges  0x00000000      0x7a8
 *(.debug_aranges)
 .debug_aranges
                0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_aranges
                0x00000020       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_aranges
                0x00000048       0x28 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_aranges
                0x00000070       0x98 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_aranges
                0x00000108       0x20 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_aranges
                0x00000128       0x70 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_aranges
                0x00000198       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_aranges
                0x000001b8       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_aranges
                0x000001e8       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_aranges
                0x00000218       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_aranges
                0x00000238       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_aranges
                0x00000288       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_aranges
                0x000002a8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_aranges
                0x000002c8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_aranges
                0x000002e8       0x40 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_aranges
                0x00000328       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .debug_aranges
                0x00000350       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_aranges
                0x00000408       0x58 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_aranges
                0x00000460       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_aranges
                0x000004a8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_aranges
                0x000004c8       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_aranges
                0x000004e8       0x20 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_aranges
                0x00000508       0xf0 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_aranges
                0x000005f8       0x38 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_aranges
                0x00000630       0x48 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x00000678       0x58 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_aranges
                0x000006d0       0x68 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_aranges
                0x00000738       0x70 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_pubnames
 *(.debug_pubnames)

.debug_info     0x00000000    0x18fb5
 *(.debug_info .gnu.linkonce.wi.*)
 .debug_info    0x00000000      0x181 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_info    0x00000181      0x21d esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_info    0x0000039e      0xc71 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_info    0x0000100f     0x1eac esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_info    0x00002ebb      0x68a esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_info    0x00003545     0x2520 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_info    0x00005a65       0xc6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_info    0x00005b2b      0x44e esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_info    0x00005f79      0x104 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_info    0x0000607d      0x751 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_info    0x000067ce      0xa2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_info    0x000071fa      0x32b esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_info    0x00007525       0x9f esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_info    0x000075c4      0x26b esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_info    0x0000782f      0x88d esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_info    0x000080bc      0x1d8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .debug_info    0x00008294     0x36ac esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_info    0x0000b940      0xc7d esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_info    0x0000c5bd     0x3e6f esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_info    0x0001042c     0x1743 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_info    0x00011b6f      0x3d8 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_info    0x00011f47      0x87b esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_info    0x000127c2     0x1452 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_info    0x00013c14      0x281 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_info    0x00013e95     0x1a1c esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x000158b1     0x1cdb esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_info    0x0001758c      0xf9d esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_info    0x00018529      0xa8c esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_abbrev   0x00000000     0x442e
 *(.debug_abbrev)
 .debug_abbrev  0x00000000       0xaa esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_abbrev  0x000000aa       0xfb esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_abbrev  0x000001a5      0x309 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_abbrev  0x000004ae      0x507 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_abbrev  0x000009b5      0x254 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_abbrev  0x00000c09      0x4f1 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_abbrev  0x000010fa       0x89 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_abbrev  0x00001183      0x1ca esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_abbrev  0x0000134d       0x80 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_abbrev  0x000013cd      0x207 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_abbrev  0x000015d4      0x296 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_abbrev  0x0000186a      0x12e esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_abbrev  0x00001998       0x65 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_abbrev  0x000019fd      0x16d esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_abbrev  0x00001b6a      0x322 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_abbrev  0x00001e8c       0xaf esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .debug_abbrev  0x00001f3b      0x568 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_abbrev  0x000024a3      0x331 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_abbrev  0x000027d4      0x344 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_abbrev  0x00002b18      0x302 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_abbrev  0x00002e1a      0x11a esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_abbrev  0x00002f34      0x306 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_abbrev  0x0000323a      0x453 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_abbrev  0x0000368d      0x1a5 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_abbrev  0x00003832      0x216 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00003a48      0x376 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_abbrev  0x00003dbe      0x2f8 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_abbrev  0x000040b6      0x378 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_line     0x00000000    0x11620
 *(.debug_line)
 .debug_line    0x00000000      0x228 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_line    0x00000228      0x424 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_line    0x0000064c      0x669 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_line    0x00000cb5     0x1eea esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_line    0x00002b9f      0x71e esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_line    0x000032bd     0x2212 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_line    0x000054cf      0x206 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_line    0x000056d5      0x480 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_line    0x00005b55      0x2a6 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_line    0x00005dfb      0x84d esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_line    0x00006648      0x9ac esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_line    0x00006ff4      0x41b esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_line    0x0000740f       0xe5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_line    0x000074f4      0x507 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_line    0x000079fb      0xb89 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_line    0x00008584      0x699 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .debug_line    0x00008c1d     0x1a37 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_line    0x0000a654      0xbe7 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_line    0x0000b23b      0xae9 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_line    0x0000bd24      0x541 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_line    0x0000c265      0x752 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_line    0x0000c9b7      0x7df esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_line    0x0000d196     0x1931 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_line    0x0000eac7      0x459 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_line    0x0000ef20      0x4a3 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x0000f3c3      0x796 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_line    0x0000fb59     0x12a8 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_line    0x00010e01      0x81f esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_frame    0x00000000     0x1490
 *(.debug_frame)
 .debug_frame   0x00000000       0x20 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_frame   0x00000020       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_frame   0x00000060       0x38 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_frame   0x00000098      0x288 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_frame   0x00000320       0x58 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_frame   0x00000378      0x184 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_frame   0x000004fc       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_frame   0x0000051c       0x68 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_frame   0x00000584       0x40 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_frame   0x000005c4       0x34 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_frame   0x000005f8      0x100 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_frame   0x000006f8       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_frame   0x00000728       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_frame   0x00000748       0x2c esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_frame   0x00000774       0xc4 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_frame   0x00000838       0x50 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .debug_frame   0x00000888      0x2b8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_frame   0x00000b40       0xd8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_frame   0x00000c18       0xc4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_frame   0x00000cdc       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_frame   0x00000d0c       0x34 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_frame   0x00000d40       0x38 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_frame   0x00000d78      0x2d4 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_frame   0x0000104c       0x84 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_frame   0x000010d0       0x80 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x00001150       0xbc esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_frame   0x0000120c      0x164 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_frame   0x00001370      0x120 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_str      0x00000000     0x9744
 *(.debug_str)
 .debug_str     0x00000000     0x9744 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                0x2e6 (size before relaxing)
 .debug_str     0x00009744      0x2fc esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_str     0x00009744      0x9de esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_str     0x00009744     0x14e1 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_str     0x00009744      0x549 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_str     0x00009744     0x15cc esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_str     0x00009744      0x28a esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_str     0x00009744      0x417 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_str     0x00009744      0x2d3 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_str     0x00009744      0xfa4 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_str     0x00009744      0xd22 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_str     0x00009744      0x662 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_str     0x00009744      0x273 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_str     0x00009744      0x30e esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_str     0x00009744      0x846 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_str     0x00009744      0x2b9 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .debug_str     0x00009744     0x2582 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_str     0x00009744     0x11fa esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_str     0x00009744     0x2ce5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_str     0x00009744      0xa8f esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_str     0x00009744      0x2de esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_str     0x00009744      0xbeb esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_str     0x00009744     0x10fd esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_str     0x00009744      0x31c esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_str     0x00009744     0x135f esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x00009744     0x14c3 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_str     0x00009744      0x65f esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_str     0x00009744      0x639 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_loc      0x00000000     0x7b71
 *(.debug_loc)
 .debug_loc     0x00000000       0xc8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_loc     0x000000c8       0xc7 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_loc     0x0000018f     0x1245 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_loc     0x000013d4      0x2d9 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_loc     0x000016ad     0x216b esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_loc     0x00003818      0x17d esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_loc     0x00003995       0x5e esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_loc     0x000039f3      0x3e0 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_loc     0x00003dd3      0x1b5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_loc     0x00003f88       0xf8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_loc     0x00004080      0x17b esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_loc     0x000041fb     0x1184 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_loc     0x0000537f      0x1a5 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_loc     0x00005524      0x157 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_loc     0x0000567b       0x87 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_loc     0x00005702       0xe5 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_loc     0x000057e7      0xaaf esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_loc     0x00006296      0x1d6 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_loc     0x0000646c      0xded esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_loc     0x00007259      0x918 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_macinfo
 *(.debug_macinfo)

.debug_pubtypes
 *(.debug_pubtypes)

.debug_ranges   0x00000000     0x1588
 *(.debug_ranges)
 .debug_ranges  0x00000000       0x10 esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
 .debug_ranges  0x00000010       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .debug_ranges  0x00000028       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .debug_ranges  0x00000058      0x200 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .debug_ranges  0x00000258       0x70 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .debug_ranges  0x000002c8      0x410 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .debug_ranges  0x000006d8       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .debug_ranges  0x000006e8       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .debug_ranges  0x00000708       0x20 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .debug_ranges  0x00000728       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .debug_ranges  0x00000758       0x78 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .debug_ranges  0x000007d0       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .debug_ranges  0x000007e0       0x10 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .debug_ranges  0x000007f0       0x28 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .debug_ranges  0x00000818       0x90 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .debug_ranges  0x000008a8       0x18 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .debug_ranges  0x000008c0      0x180 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .debug_ranges  0x00000a40       0xb8 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .debug_ranges  0x00000af8       0x70 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .debug_ranges  0x00000b68       0x48 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .debug_ranges  0x00000bb0       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .debug_ranges  0x00000bf0       0x40 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .debug_ranges  0x00000c30      0x398 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .debug_ranges  0x00000fc8       0x60 esp-idf/log/liblog.a(log_noos.c.obj)
 .debug_ranges  0x00001028       0x88 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x000010b0       0xa8 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .debug_ranges  0x00001158      0x368 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .debug_ranges  0x000014c0       0xc8 esp-idf/hal/libhal.a(cache_hal.c.obj)

.debug_weaknames
 *(.debug_weaknames)

.debug_funcnames
 *(.debug_funcnames)

.debug_typenames
 *(.debug_typenames)

.debug_varnames
 *(.debug_varnames)

.debug_gnu_pubnames
 *(.debug_gnu_pubnames)

.debug_gnu_pubtypes
 *(.debug_gnu_pubtypes)

.debug_types
 *(.debug_types)

.debug_addr
 *(.debug_addr)

.debug_line_str
 *(.debug_line_str)

.debug_loclists
 *(.debug_loclists)

.debug_macro
 *(.debug_macro)

.debug_names
 *(.debug_names)

.debug_rnglists
 *(.debug_rnglists)

.debug_str_offsets
 *(.debug_str_offsets)

.comment        0x00000000       0x2f
 *(.comment)
 .comment       0x00000000       0x2f esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                 0x30 (size before relaxing)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
 .comment       0x0000002f       0x30 esp-idf/main/libmain.a(bootloader_start.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
 .comment       0x0000002f       0x30 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
 .comment       0x0000002f       0x30 esp-idf/log/liblog.a(log_noos.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(efuse_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(mmu_hal.c.obj)
 .comment       0x0000002f       0x30 esp-idf/hal/libhal.a(cache_hal.c.obj)

.note.GNU-stack
 *(.note.GNU-stack)

/DISCARD/
 *(.rela.*)
OUTPUT(bootloader.elf elf32-littleriscv)

Cross Reference Table

Symbol                                            File
Cache_Disable_ICache                              esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Enable_ICache                               esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Get_ICache_Line_Size                        esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Invalidate_Addr                             esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Resume_ICache                               esp-idf/hal/libhal.a(cache_hal.c.obj)
Cache_Suspend_ICache                              esp-idf/hal/libhal.a(cache_hal.c.obj)
EFUSE                                             esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN0                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_CAL_VOL_ATTEN3                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN0                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC1_INIT_CODE_ATTEN3                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_ADC_CALIBRATION_3                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MAJOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_BLK_VERSION_MINOR                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_CUSTOM_MAC_USED                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIG_DBIAS_HVT                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIG_LDO_ACT_DBIAS26                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIG_LDO_ACT_STEPD10                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIG_LDO_SLP_DBIAS2                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIG_LDO_SLP_DBIAS26                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_BLK_VERSION_MAJOR               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DISABLE_WAFER_VERSION_MAJOR             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_DIS_DIRECT_BOOT                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_ICACHE                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MANUAL_ENCRYPT             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_DIS_DOWNLOAD_MODE                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_DIS_PAD_JTAG                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_ENABLE_SECURITY_DOWNLOAD                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_FLASH_TPUW                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_FORCE_SEND_RESUME                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KEY0                                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_KEY0_FE_128BIT                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KEY0_FE_256BIT                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_KEY0_SB_128BIT                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_MAC                                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_OCODE                                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_PKG_VERSION                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_RD_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RD_DIS_KEY0                             esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_RD_DIS_KEY0_HI                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
ESP_EFUSE_RD_DIS_KEY0_LOW                         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
ESP_EFUSE_RTC_LDO_ACT_DBIAS13                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RTC_LDO_ACT_DBIAS31                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RTC_LDO_SLP_DBIAS13                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RTC_LDO_SLP_DBIAS29                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_RTC_LDO_SLP_DBIAS31                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_BOOT_EN                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SECURE_VERSION                          esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_SPI_BOOT_CRYPT_CNT                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_TEMP_CALIB                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_UART_PRINT_CONTROL                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
ESP_EFUSE_USER_DATA                               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_USER_DATA_MAC_CUSTOM                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MAJOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WAFER_VERSION_MINOR                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WDT_DELAY_SEL                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS                                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN0              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_CAL_VOL_ATTEN3              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN0            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC1_INIT_CODE_ATTEN3            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ADC_CALIBRATION_3                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MAJOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLK_VERSION_MINOR                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_BLOCK_KEY0                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ESP_EFUSE_WR_DIS_CUSTOM_MAC                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_CUSTOM_MAC_USED                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIG_DBIAS_HVT                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIG_LDO_ACT_DBIAS26              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIG_LDO_ACT_STEPD10              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIG_LDO_SLP_DBIAS2               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIG_LDO_SLP_DBIAS26              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_BLK_VERSION_MAJOR        esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DISABLE_WAFER_VERSION_MAJOR      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DIRECT_BOOT                  esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_ICACHE              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MANUAL_ENCRYPT      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_DOWNLOAD_MODE                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_DIS_PAD_JTAG                     esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_ENABLE_SECURITY_DOWNLOAD         esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FLASH_TPUW                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_FORCE_SEND_RESUME                esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_MAC                              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_OCODE                            esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_PKG_VERSION                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RD_DIS                           esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
ESP_EFUSE_WR_DIS_RTC_LDO_ACT_DBIAS13              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RTC_LDO_ACT_DBIAS31              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RTC_LDO_SLP_DBIAS13              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RTC_LDO_SLP_DBIAS29              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_RTC_LDO_SLP_DBIAS31              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_BOOT_EN                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SECURE_VERSION                   esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_SPI_BOOT_CRYPT_CNT               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
ESP_EFUSE_WR_DIS_TEMP_CALIB                       esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_UART_PRINT_CONTROL               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MAJOR              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WAFER_VERSION_MINOR              esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_WDT_DELAY_SEL                    esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_WR_DIS_XTS_KEY_LENGTH_256               esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
ESP_EFUSE_XTS_KEY_LENGTH_256                      esp-idf/efuse/libefuse.a(esp_efuse_table.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ROM_Boot_Cache_Init                               esp-idf/hal/libhal.a(mmu_hal.c.obj)
RTCCNTL                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
SPIMEM0                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
SPIMEM1                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
TIMERG0                                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
UART0                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
__ashldi3                                         D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_ashldi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__assert_func                                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/log/liblog.a(log_noos.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
__clz_tab                                         D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_clz.o)
                                                  D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                                                  D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
__divdi3                                          D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_divdi3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
__getreent                                        esp-idf/main/libmain.a(bootloader_start.c.obj)
__lshrdi3                                         D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_lshrdi3.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__popcountsi2                                     D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_popcountsi2.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
__sf                                              D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
__udivdi3                                         D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/rv32imc_zicsr_zifencei/ilp32/no-rtti\libgcc.a(_udivdi3.o)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
_bss_end                                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
_bss_start                                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
_data_end                                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
_data_start                                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
_dram_end                                         esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_dram_start                                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_impure_data                                      D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
_impure_ptr                                       D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-impure.o)
_loader_text_end                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
_loader_text_start                                esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
abort                                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
                                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_after_init                             esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_ana_bod_reset_config                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_ana_clock_glitch_reset_config          esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_ana_super_wdt_reset_config             esp-idf/bootloader_support/libbootloader_support.a(bootloader_soc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_atexit                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_before_init                            esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_check_bootloader_validity              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_clear_bss_section                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_clock_configure                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_common_check_chip_validity             esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_common_check_efuse_blk_validity        esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_common_get_active_otadata              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_get_partition_description       esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_crc                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_invalid              esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_ota_select_valid                esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_common_read_otadata                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_common_select_otadata                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
bootloader_config_wdt                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_configure_spi_pins                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_console_deinit                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_console_init                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_debug_buffer                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_enable_random                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_enable_wp                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_execute_flash_command                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_fill_random                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_random.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_clock_config                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_flash_cs_timing_config                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_flash_dummy_config                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_flash_erase_range                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_execute_command_common           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_get_spi_mode                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_flash_is_octal_mode_enabled            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_flash_read_sfdp                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_reset_chip                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_flash_set_dummy_out                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_flash_unlock                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_flash_update_id                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_flash_update_size                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_flash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_flash_xmc_startup                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_image_hdr                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_init                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_init_mem                               esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_init_spi_flash                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_load_image                             esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_load_image_no_verify                   esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
bootloader_mmap                                   esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_mmap_get_free_pages                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_munmap                                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_print_banner                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_random_disable                         esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_random_enable                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
bootloader_read_bootloader_header                 esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
bootloader_read_flash_id                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
bootloader_reset                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_sha256_data                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_finish                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_flash_contents                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_hex_to_str                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_sha256_start                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
bootloader_spi_flash_reset                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
bootloader_utility_get_selected_boot_partition    esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_boot_image                esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
bootloader_utility_load_partition_table           esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
cache_hal_disable                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_enable                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
cache_hal_get_cache_line_size                     esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_init                                    esp-idf/hal/libhal.a(cache_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
cache_hal_invalidate_addr                         esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_is_cache_enabled                        esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_resume                                  esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_suspend                                 esp-idf/hal/libhal.a(cache_hal.c.obj)
cache_hal_vaddr_to_cache_level_id                 esp-idf/hal/libhal.a(cache_hal.c.obj)
call_start_cpu0                                   esp-idf/main/libmain.a(bootloader_start.c.obj)
efuse_hal_blk_version                             esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_chip_revision                           esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_flash_encryption_enabled                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
efuse_hal_get_disable_blk_version_major           esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_get_disable_wafer_version_major         esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
efuse_hal_get_mac                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_major_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_get_minor_chip_version                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/hal/libhal.a(efuse_hal.c.obj)
efuse_hal_is_coding_error_in_block                esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_program                                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_read                                    esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
efuse_hal_set_timing                              esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_bootloader_desc                               esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
esp_bootloader_get_description                    esp-idf/esp_bootloader_format/libesp_bootloader_format.a(esp_bootloader_desc.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
esp_cpu_configure_region_protection               esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_mem.c.obj)
esp_efuse_batch_write_begin                       esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_batch_write_cancel                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_batch_write_commit                      esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_block_is_empty                          esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_check_errors                            esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_destroy_block                           esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_disable_rom_download_mode               esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_enable_rom_secure_download_mode         esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_find_purpose                            esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_coding_scheme                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_field_size                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_get_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_get_key_purpose                         esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_keypurpose_dis_write                esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_get_pkg_ver                             esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_key_block_unused                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_block                              esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_read_field_bit                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_blob                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_field_cnt                          esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_read_reg                                esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_set_key_dis_read                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_key_dis_write                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_read_protect                        esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_set_rom_log_scheme                      esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_set_write_protect                       esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_apply_new_coding_scheme         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_chip                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_burn_chip_opt                   esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_burn_efuses                     esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_check_errors                    esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_clear_program_registers         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_count_once                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_debug_dump_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_pending              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_debug_dump_single_block         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_erase_virt_blocks               esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_fill_buff                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_number_of_items             esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_get_read_register_address       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_utility_is_correct_written_data         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_process                         esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_read_reg                        esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_reset                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_update_virt_blocks              esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
esp_efuse_utility_write_blob                      esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_cnt                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_utility_write_reg                       esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_efuse_write_block                             esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_field_bit                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_field_blob                        esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_fields.c.obj)
esp_efuse_write_field_cnt                         esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_efuse_write_key                               esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_keys                              esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
esp_efuse_write_reg                               esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
esp_flash_encryption_cfg_verify_release_mode      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_encryption_enabled                      esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_flash_encryption_set_release_mode             esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_flash_write_protect_crypt_cnt                 esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_get_flash_encryption_mode                     esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
esp_image_get_flash_size                          esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_get_metadata                            esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader                       esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_image_verify_bootloader_data                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
esp_log_early_timestamp                           esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_lock                                 esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_lock_timeout                         esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_impl_unlock                               esp-idf/log/liblog.a(log_noos.c.obj)
esp_log_timestamp                                 esp-idf/log/liblog.a(log_noos.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_partition_table_verify                        esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_crc32_le                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
esp_rom_delay_us                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_get_cpu_ticks_per_us                      esp-idf/log/liblog.a(log_noos.c.obj)
esp_rom_get_reset_reason                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
esp_rom_gpio_pad_set_drv                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
esp_rom_install_uart_printf                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
esp_rom_mbedtls_md5_finish_ret                    esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_mbedtls_md5_starts_ret                    esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_mbedtls_md5_update_ret                    esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
esp_rom_output_flush_tx                           esp-idf/bootloader_support/libbootloader_support.a(bootloader_console_loader.c.obj)
esp_rom_output_tx_wait_idle                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
esp_rom_printf                                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_encrypt.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_common_loader.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_panic.c.obj)
esp_rom_regi2c_write                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_regi2c_write_mask                         esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_random_esp32c2.c.obj)
esp_rom_set_cpu_ticks_per_us                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
esp_rom_software_reset_system                     esp-idf/esp_hw_support/libesp_hw_support.a(cpu_region_protect.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
esp_rom_spiflash_config_clk                       esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
esp_rom_spiflash_config_param                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
esp_rom_spiflash_erase_block                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_erase_sector                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_read                             esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_wait_idle                        esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write                            esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_rom_spiflash_write_encrypted                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
esp_secure_boot_read_key_digests                  esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
ets_efuse_clear_program_registers                 esp-idf/hal/libhal.a(efuse_hal.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
ets_efuse_read                                    esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
ets_efuse_rs_calculate                            esp-idf/hal/libhal.a(efuse_hal.c.obj)
ets_sha_enable                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_finish                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_init                                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
ets_sha_update                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
memcmp                                            D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcmp.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
memcpy                                            D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memcpy.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
memset                                            D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/../lib/gcc/riscv32-esp-elf/13.2.0/../../../../riscv32-esp-elf/lib/rv32imc_zicsr_zifencei/ilp32/no-rtti\libc.a(libc_a-memset.o)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_api.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_sha.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(esp_image_format.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
                                                  esp-idf/main/libmain.a(bootloader_start.c.obj)
mmu_hal_bytes_to_pages                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_check_valid_ext_vaddr_region              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_init                                      esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_esp32c2.c.obj)
mmu_hal_map_region                                esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_paddr_to_vaddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_pages_to_bytes                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_unmap_all                                 esp-idf/hal/libhal.a(mmu_hal.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_utility.c.obj)
mmu_hal_unmap_region                              esp-idf/hal/libhal.a(mmu_hal.c.obj)
mmu_hal_vaddr_to_paddr                            esp-idf/hal/libhal.a(mmu_hal.c.obj)
range_read_addr_blocks                            esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
range_write_addr_blocks                           esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
                                                  esp-idf/efuse/libefuse.a(esp_efuse_utility.c.obj)
rom_spiflash_legacy_data                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash_config_esp32c2.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_flash.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(flash_partitions.c.obj)
rtc_clk_32k_enable_external                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_8m_divider_set                            esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_8m_enable                                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_8md256_enabled                            esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_apb_freq_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_apb_freq_update                           esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_get_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_mhz_to_config                    esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config                       esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_cpu_freq_set_config_fast                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_set_xtal                         esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_freq_to_xtal                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_cpu_set_to_default_config                 esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_clk_divider_set                               esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_fast_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_fast_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_init                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_slow_freq_get_hz                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
rtc_clk_slow_src_get                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_clock_init.c.obj)
rtc_clk_slow_src_set                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_clk_xtal_freq_get                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/bootloader_support/libbootloader_support.a(bootloader_console.c.obj)
rtc_clk_xtal_freq_update                          esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
                                                  esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk_init.c.obj)
rtc_dig_8m_enabled                                esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_disable                             esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_dig_clk8m_enable                              esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
rtc_get_xtal                                      esp-idf/esp_hw_support/libesp_hw_support.a(rtc_clk.c.obj)
s_cache_hal_init_ctx                              esp-idf/hal/libhal.a(cache_hal.c.obj)
s_table                                           esp-idf/efuse/libefuse.a(esp_efuse_api_key.c.obj)
wdt_hal_config_stage                              esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_enable                                    esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_init                                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_set_flashboot_en                          esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_disable                     esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
wdt_hal_write_protect_enable                      esp-idf/bootloader_support/libbootloader_support.a(bootloader_init.c.obj)
