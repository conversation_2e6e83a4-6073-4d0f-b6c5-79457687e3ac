/**
 * @file base_config.h
 * @brief TIMO智能闹钟底座设备配置
 * @version 1.0.0
 * @date 2025-06-27
 * 
 * 底座硬件配置：
 * - ESP32-C2 (ESP8684H2) 主控
 * - WS2812 RGB灯带 (30颗LED)
 * - 声音传感器
 * - 用户按键
 * - LED连接指示灯
 */

#ifndef BASE_CONFIG_H
#define BASE_CONFIG_H

#include "driver/gpio.h"
#include "esp_adc/adc_oneshot.h"

#ifdef __cplusplus
extern "C" {
#endif

/* ========== GPIO引脚定义 ========== */

/* 声音传感器 */
#define SOUND_SENSOR_ADC_CHANNEL    ADC_CHANNEL_0  // GPIO0
#define SOUND_SENSOR_ADC_UNIT       ADC_UNIT_1
#define SOUND_SENSOR_ADC_ATTEN      ADC_ATTEN_DB_12

/* WS2812 RGB灯带 */
#define WS2812_GPIO                 8
#define WS2812_LED_COUNT            30
#define WS2812_RMT_CHANNEL          RMT_CHANNEL_0

/* 用户按键 */
#define USER_BUTTON_GPIO            9
#define USER_BUTTON_ACTIVE_LEVEL    0   // 按下为低电平

/* LED连接指示灯 */
#define STATUS_LED_GPIO             18

/* ========== 系统配置 ========== */

/* 蓝牙配置 */
#define BT_DEVICE_NAME              "TIMO_Base"
#define BT_SERVICE_UUID             "12345678-1234-1234-1234-123456789abc"
#define BT_CHAR_UUID_TX             "*************-4321-4321-cba987654321"
#define BT_CHAR_UUID_RX             "11111111-**************-************"

/* 声音传感器配置 */
#define SOUND_THRESHOLD_LOW         100     // 低音量阈值
#define SOUND_THRESHOLD_MEDIUM      500     // 中音量阈值
#define SOUND_THRESHOLD_HIGH        1000    // 高音量阈值
#define SOUND_SAMPLE_RATE_MS        50      // 采样间隔(毫秒)

/* 氛围灯效配置 */
#define AMBIENT_UPDATE_RATE_MS      50      // 灯效更新间隔(毫秒)
#define AMBIENT_BRIGHTNESS_MAX      255     // 最大亮度
#define AMBIENT_BRIGHTNESS_MIN      10      // 最小亮度

/* 任务优先级 */
#define TASK_PRIORITY_HIGH          (configMAX_PRIORITIES - 1)
#define TASK_PRIORITY_NORMAL        (configMAX_PRIORITIES - 2)
#define TASK_PRIORITY_LOW           (configMAX_PRIORITIES - 3)

/* 任务堆栈大小 - ESP32-C2优化版本 */
#define TASK_STACK_SIZE_LARGE       (3072)  // 3KB - 减少大任务堆栈
#define TASK_STACK_SIZE_MEDIUM      (1536)  // 1.5KB - 减少中等任务堆栈
#define TASK_STACK_SIZE_SMALL       (1024)  // 1KB - 保持小任务堆栈
#define TASK_STACK_SIZE_TINY        (768)   // 768B - 新增微型任务堆栈

/* 系统事件位定义 */
#define BASE_EVENT_SYSTEM_READY     (1 << 0)
#define BASE_EVENT_LED_READY        (1 << 1)
#define BASE_EVENT_SOUND_READY      (1 << 2)
#define BASE_EVENT_BUTTON_READY     (1 << 3)
#define BASE_EVENT_BLE_READY        (1 << 4)
#define BASE_EVENT_BLUETOOTH_READY  (1 << 5)
#define BASE_EVENT_SOUND_DETECTED   (1 << 6)
#define BASE_EVENT_BUTTON_PRESS     (1 << 7)
#define BASE_EVENT_BUTTON_LONG      (1 << 8)

/* ========== 氛围场景定义 ========== */

typedef enum {
    AMBIENT_SCENE_OFF = 0,          // 关闭
    AMBIENT_SCENE_STANDBY,          // 待机模式
    AMBIENT_SCENE_CONVERSATION,     // 对话律动
    AMBIENT_SCENE_MORNING_WAKE,     // 晨间唤醒
    AMBIENT_SCENE_NAP_WAKE,         // 小憩唤醒
    AMBIENT_SCENE_SLEEP_AID,        // 助眠模式
    AMBIENT_SCENE_TODO_REMIND,      // 待办提醒
    AMBIENT_SCENE_ALERT_LOW,        // 低级预警
    AMBIENT_SCENE_ALERT_MEDIUM,     // 中级预警
    AMBIENT_SCENE_ALERT_HIGH,       // 高级预警
    AMBIENT_SCENE_FOCUS_MODE,       // 专注模式
    AMBIENT_SCENE_CHARGING,         // 充电状态
    AMBIENT_SCENE_PAIRING,          // 配对模式
    AMBIENT_SCENE_MAX
} ambient_scene_t;

/* ========== LED模式定义 ========== */

typedef enum {
    LED_MODE_OFF = 0,           // 关闭
    LED_MODE_SOLID,             // 纯色
    LED_MODE_BREATHING,         // 呼吸灯
    LED_MODE_RAINBOW,           // 彩虹
    LED_MODE_WAVE,              // 波浪
    LED_MODE_FLASH,             // 闪烁
    LED_MODE_MUSIC_SYNC,        // 音乐同步
    LED_MODE_NOTIFICATION,      // 通知
    LED_MODE_MAX
} led_mode_t;

/* ========== 颜色定义 ========== */

typedef struct {
    uint8_t r;
    uint8_t g;
    uint8_t b;
} rgb_color_t;

/* LED配置结构体 */
typedef struct {
    led_mode_t mode;            // LED模式
    rgb_color_t color;          // 颜色
    uint8_t brightness;         // 亮度 (0-255)
    uint8_t speed;              // 速度 (1-10)
    bool enabled;               // 是否启用
} led_config_t;

/* 预定义颜色 */
#define COLOR_OFF           ((rgb_color_t){0, 0, 0})
#define COLOR_WHITE         ((rgb_color_t){255, 255, 255})
#define COLOR_RED           ((rgb_color_t){255, 0, 0})
#define COLOR_GREEN         ((rgb_color_t){0, 255, 0})
#define COLOR_BLUE          ((rgb_color_t){0, 0, 255})
#define COLOR_YELLOW        ((rgb_color_t){255, 255, 0})
#define COLOR_CYAN          ((rgb_color_t){0, 255, 255})
#define COLOR_MAGENTA       ((rgb_color_t){255, 0, 255})
#define COLOR_ORANGE        ((rgb_color_t){255, 165, 0})
#define COLOR_PURPLE        ((rgb_color_t){128, 0, 128})
#define COLOR_WARM_WHITE    ((rgb_color_t){255, 220, 180})
#define COLOR_COOL_WHITE    ((rgb_color_t){180, 220, 255})

/* ========== 系统状态定义 ========== */

typedef struct {
    bool bluetooth_connected;
    bool main_device_online;
    bool charging_active;
    ambient_scene_t current_scene;
    uint8_t brightness;
    uint16_t sound_level;
    bool user_button_pressed;
} base_system_status_t;

/* ========== 函数声明 ========== */

/**
 * @brief 获取系统状态
 * @return base_system_status_t* 
 */
base_system_status_t* base_get_system_status(void);

/**
 * @brief 设置状态LED
 * @param on true=点亮, false=熄灭
 */
void base_set_status_led(bool on);

/**
 * @brief 切换状态LED
 */
void base_toggle_status_led(void);

/* ========== WS2812 LED驱动函数声明 ========== */

/**
 * @brief 初始化WS2812 LED驱动
 * @return esp_err_t
 */
esp_err_t ws2812_init(void);

/**
 * @brief 启动WS2812 LED驱动
 * @return esp_err_t
 */
esp_err_t ws2812_start(void);

/**
 * @brief 设置单个LED颜色
 * @param index LED索引 (0 ~ WS2812_LED_COUNT-1)
 * @param color RGB颜色
 * @return esp_err_t
 */
esp_err_t ws2812_set_pixel(uint16_t index, rgb_color_t color);

/**
 * @brief 设置所有LED颜色
 * @param color RGB颜色
 * @return esp_err_t
 */
esp_err_t ws2812_set_all_pixels(rgb_color_t color);

/**
 * @brief 清空所有LED
 * @return esp_err_t
 */
esp_err_t ws2812_clear_all(void);

/**
 * @brief 刷新LED显示
 * @return esp_err_t
 */
esp_err_t ws2812_refresh(void);

/**
 * @brief 设置全局亮度
 * @param brightness 亮度值 (0-255)
 * @return esp_err_t
 */
esp_err_t ws2812_set_brightness(uint8_t brightness);

/**
 * @brief 获取全局亮度
 * @return uint8_t 当前亮度值
 */
uint8_t ws2812_get_brightness(void);

/**
 * @brief 彩虹效果
 * @param brightness 亮度 (0-255)
 * @param speed 速度 (1-10)
 * @return esp_err_t
 */
esp_err_t ws2812_rainbow_effect(uint8_t brightness, uint16_t speed);

/**
 * @brief 呼吸灯效果
 * @param color 基础颜色
 * @param speed 速度 (1-10)
 * @return esp_err_t
 */
esp_err_t ws2812_breathing_effect(rgb_color_t color, uint16_t speed);

/**
 * @brief 波浪效果
 * @param color 基础颜色
 * @param brightness 亮度 (0-255)
 * @param speed 速度 (1-10)
 * @return esp_err_t
 */
esp_err_t ws2812_wave_effect(rgb_color_t color, uint8_t brightness, uint16_t speed);

/* ========== 声音传感器函数声明 ========== */

/**
 * @brief 初始化声音传感器
 * @return esp_err_t
 */
esp_err_t sound_sensor_init(void);

/**
 * @brief 启动声音传感器
 * @return esp_err_t
 */
esp_err_t sound_sensor_start(void);

/**
 * @brief 停止声音传感器
 * @return esp_err_t
 */
esp_err_t sound_sensor_stop(void);

/**
 * @brief 读取原始ADC值
 * @param raw_value 原始ADC值输出
 * @return esp_err_t
 */
esp_err_t sound_sensor_read_raw(int *raw_value);

/**
 * @brief 读取校准后的电压值
 * @param voltage_mv 电压值输出(毫伏)
 * @return esp_err_t
 */
esp_err_t sound_sensor_read_voltage(int *voltage_mv);

/**
 * @brief 读取声音级别 (0-1000)
 * @return uint16_t 声音级别
 */
uint16_t sound_sensor_read_level(void);

/**
 * @brief 更新声音统计数据
 */
void sound_sensor_update_stats(void);

/**
 * @brief 获取当前声音级别
 * @return uint16_t 当前声音级别
 */
uint16_t sound_sensor_get_current_level(void);

/**
 * @brief 获取平均声音级别
 * @return uint16_t 平均声音级别
 */
uint16_t sound_sensor_get_average_level(void);

/**
 * @brief 获取峰值声音级别
 * @return uint16_t 峰值声音级别
 */
uint16_t sound_sensor_get_peak_level(void);

/**
 * @brief 检测声音事件
 * @param threshold 阈值
 * @return bool true=检测到事件
 */
bool sound_sensor_detect_event(uint16_t threshold);

/**
 * @brief 检测声音突变
 * @param threshold 阈值
 * @return bool true=检测到突变
 */
bool sound_sensor_detect_spike(uint16_t threshold);

/**
 * @brief 获取声音传感器状态
 * @return bool true=运行中
 */
bool sound_sensor_is_running(void);

/**
 * @brief 重置声音统计数据
 */
void sound_sensor_reset_stats(void);

/* ========== 按键处理函数声明 ========== */

/* 按键事件类型 */
typedef enum {
    BUTTON_EVENT_NONE = 0,
    BUTTON_EVENT_PRESS,
    BUTTON_EVENT_RELEASE,
    BUTTON_EVENT_LONG_PRESS,
    BUTTON_EVENT_LONG_RELEASE
} button_event_type_t;

/* 按键事件结构体 */
typedef struct {
    button_event_type_t type;
    uint32_t timestamp;
    uint32_t duration;
} button_event_t;

/**
 * @brief 初始化按键处理器
 * @return esp_err_t
 */
esp_err_t button_handler_init(void);

/**
 * @brief 启动按键处理器
 * @return esp_err_t
 */
esp_err_t button_handler_start(void);

/**
 * @brief 停止按键处理器
 * @return esp_err_t
 */
esp_err_t button_handler_stop(void);

/**
 * @brief 获取按键事件
 * @param event 事件输出
 * @return bool true=有事件
 */
bool button_handler_get_event(button_event_t *event);

/**
 * @brief 等待按键事件
 * @param event 事件输出
 * @param timeout_ms 超时时间(毫秒)
 * @return bool true=有事件
 */
bool button_handler_wait_event(button_event_t *event, uint32_t timeout_ms);

/**
 * @brief 获取当前按键状态
 * @return bool true=按下
 */
bool button_handler_is_pressed(void);

/**
 * @brief 获取按键按下时长
 * @return uint32_t 按下时长(毫秒)
 */
uint32_t button_handler_get_press_duration(void);

/**
 * @brief 清空按键事件队列
 */
void button_handler_clear_events(void);

/**
 * @brief 获取按键统计信息
 * @param total_presses 总按键次数
 * @param long_presses 长按次数
 */
void button_handler_get_stats(uint32_t *total_presses, uint32_t *long_presses);

/* ========== 蓝牙通信函数声明 ========== */

/**
 * @brief 初始化蓝牙通信
 * @return esp_err_t
 */
esp_err_t bluetooth_comm_init(void);

/**
 * @brief 启动蓝牙通信
 * @return esp_err_t
 */
esp_err_t bluetooth_comm_start(void);

/**
 * @brief 停止蓝牙通信
 * @return esp_err_t
 */
esp_err_t bluetooth_comm_stop(void);

/**
 * @brief 发送数据到主设备
 * @param data 数据指针
 * @param length 数据长度
 * @return esp_err_t
 */
esp_err_t bluetooth_comm_send_data(uint8_t *data, uint16_t length);

/**
 * @brief 获取蓝牙连接状态
 * @return bool true=已连接
 */
bool bluetooth_comm_is_connected(void);

/* ========== 氛围灯效函数声明 ========== */

/**
 * @brief 初始化氛围灯效控制器
 * @return esp_err_t
 */
esp_err_t ambient_effects_init(void);

/**
 * @brief 启动氛围灯效控制器
 * @return esp_err_t
 */
esp_err_t ambient_effects_start(void);

/**
 * @brief 停止氛围灯效控制器
 * @return esp_err_t
 */
esp_err_t ambient_effects_stop(void);

/**
 * @brief 设置氛围场景
 * @param scene 场景类型
 * @return esp_err_t
 */
esp_err_t ambient_effects_set_scene(ambient_scene_t scene);

/**
 * @brief 获取当前氛围场景
 * @return ambient_scene_t 当前场景
 */
ambient_scene_t ambient_effects_get_scene(void);

/**
 * @brief 设置自定义灯效配置
 * @param config 灯效配置
 * @return esp_err_t
 */
esp_err_t ambient_effects_set_config(const led_config_t *config);

/**
 * @brief 获取当前灯效配置
 * @return led_config_t* 当前配置
 */
led_config_t* ambient_effects_get_config(void);

/**
 * @brief 闪烁效果
 */
void ambient_flash_effect(void);

/**
 * @brief 音乐同步效果
 */
void ambient_music_sync_effect(void);

/**
 * @brief 通知效果
 */
void ambient_notification_effect(void);

#ifdef __cplusplus
}
#endif

#endif /* BASE_CONFIG_H */
