@echo off
cd /D E:\Electronics\MyProject\ESP\Timo_main\2_base_station_firmware\build\esp-idf\esp_system || (set FAIL_LINE=2& goto :ABORT)
d:\APP\Espressif\.espressif\v5.3.3\python_env\idf5.3_py3.11_env\Scripts\python.exe D:/APP/Espressif/esp/v5.3.3/esp-idf/tools/ldgen/ldgen.py --config E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/sdkconfig --fragments-list D:/APP/Espressif/esp/v5.3.3/esp-idf/components/riscv/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_gpio/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_pm/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_mm/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/spi_flash/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_system/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_system/app.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_common/common.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_common/soc.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_rom/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/hal/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/log/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/heap/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/soc/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_hw_support/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_hw_support/dma/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_hw_support/ldo/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/freertos/linker_common.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/freertos/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/newlib/newlib.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/newlib/system_libs.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_gptimer/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_ringbuf/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_uart/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/app_trace/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_event/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_pcnt/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_spi/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_mcpwm/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_ana_cmpr/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_dac/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_rmt/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_sdm/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_i2c/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_ledc/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_driver_parlio/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_phy/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/vfs/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/lwip/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_netif/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/wpa_supplicant/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_coex/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_wifi/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_gdbstub/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/bt/linker_esp32c2.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_adc/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_eth/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_psram/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_lcd/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/espcoredump/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/ieee802154/linker.lf;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/openthread/linker.lf --input E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/esp-idf/esp_system/ld/sections.ld.in --output E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/esp-idf/esp_system/ld/sections.ld --kconfig D:/APP/Espressif/esp/v5.3.3/esp-idf/Kconfig --env-file E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/config.env --libraries-file E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/ldgen_libraries --objdump D:/APP/Espressif/.espressif/v5.3.3/tools/riscv32-esp-elf/esp-13.2.0_20240530/riscv32-esp-elf/bin/riscv32-esp-elf-objdump.exe || (set FAIL_LINE=3& goto :ABORT)
goto :EOF

:ABORT
set ERROR_CODE=%ERRORLEVEL%
echo Batch file failed at line %FAIL_LINE% with errorcode %ERRORLEVEL%
exit /b %ERROR_CODE%