idf_component_register(
    SRCS
        "main.c"
        "base_main.c"
        "ws2812_driver.c"
        "sound_sensor.c"
        "button_handler.c"
        "ambient_effects.c"
        "bluetooth_comm.c"
        "ble_service.c"
        "system_test.c"
        "system_monitor.c"
        "led_effects.c"
        "config_manager.c"
        "error_handler.c"
    INCLUDE_DIRS
        "."
        "include"
    REQUIRES
        driver
        esp_driver_spi
        nvs_flash
        esp_timer
        esp_adc
        esp_event
        freertos
        log
        spi_flash
        bt
)
