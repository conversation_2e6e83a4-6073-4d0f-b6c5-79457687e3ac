/**
 * @file system_test.h
 * @brief TIMO底座系统功能测试头文件
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef SYSTEM_TEST_H
#define SYSTEM_TEST_H

#include "esp_err.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief 运行完整的系统测试
 * @return esp_err_t 
 */
esp_err_t system_test_run_all(void);

/**
 * @brief 运行快速测试
 * @return esp_err_t 
 */
esp_err_t system_test_run_quick(void);

#ifdef __cplusplus
}
#endif

#endif /* SYSTEM_TEST_H */
