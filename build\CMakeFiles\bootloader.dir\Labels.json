{"sources": [{"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/CMakeFiles/bootloader"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/CMakeFiles/bootloader.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/CMakeFiles/bootloader-complete.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule"}, {"file": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule"}], "target": {"labels": ["bootloader"], "name": "bootloader"}}