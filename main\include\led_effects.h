/**
 * @file led_effects.h
 * @brief Advanced LED Effects Manager Header
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef LED_EFFECTS_H
#define LED_EFFECTS_H

#include "esp_err.h"
#include "base_config.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

/* LED effects configuration */
#define LED_EFFECTS_QUEUE_SIZE          10
#define LED_EFFECTS_FRAME_RATE_MS       50      // 20 FPS

/* LED effect types */
typedef enum {
    LED_EFFECT_OFF = 0,
    LED_EFFECT_SOLID,
    LED_EFFECT_BREATHING,
    LED_EFFECT_RAINBOW,
    LED_EFFECT_WAVE,
    LED_EFFECT_FIRE,
    LED_EFFECT_SPARKLE,
    LED_EFFECT_COMET,
    LED_EFFECT_THEATER_CHASE,
    LED_EFFECT_MAX
} led_effect_type_t;

/* LED command types */
typedef enum {
    LED_CMD_SET_EFFECT = 0,
    LED_CMD_SET_BRIGHTNESS,
    LED_CMD_SET_SPEED,
    LED_CMD_STOP,
    LED_CMD_MAX
} led_command_type_t;

/* LED effect parameters */
typedef struct {
    rgb_color_t primary_color;          // Primary color
    rgb_color_t secondary_color;        // Secondary color (for multi-color effects)
    uint8_t brightness;                 // Brightness (0-255)
    uint8_t speed;                      // Speed (1-10)
    uint32_t duration_ms;               // Duration in milliseconds (0 = infinite)
    uint8_t intensity;                  // Effect intensity (0-255)
    bool reverse;                       // Reverse direction
} led_effect_params_t;

/* LED effect state */
typedef struct {
    led_effect_type_t current_effect;   // Current effect
    uint8_t brightness;                 // Current brightness
    uint8_t speed;                      // Current speed
    uint32_t start_time;                // Effect start time
    uint32_t frame_count;               // Frame counter
    bool is_running;                    // Running state
} led_effect_state_t;

/* LED effect command */
typedef struct {
    led_command_type_t type;            // Command type
    led_effect_type_t effect;           // Effect type (for SET_EFFECT)
    led_effect_params_t params;         // Effect parameters
} led_effect_command_t;

/**
 * @brief Initialize LED effects manager
 * @return esp_err_t
 */
esp_err_t led_effects_init(void);

/**
 * @brief Start LED effects task
 * @return esp_err_t
 */
esp_err_t led_effects_start(void);

/**
 * @brief Stop LED effects
 * @return esp_err_t
 */
esp_err_t led_effects_stop(void);

/**
 * @brief LED effects task
 * @param pvParameters Task parameters
 */
void led_effects_task(void *pvParameters);

/**
 * @brief Set LED effect
 * @param effect Effect type
 * @param params Effect parameters (NULL for defaults)
 * @return esp_err_t
 */
esp_err_t led_effects_set_effect(led_effect_type_t effect, const led_effect_params_t *params);

/**
 * @brief Set effect brightness
 * @param brightness Brightness value (0-255)
 * @return esp_err_t
 */
esp_err_t led_effects_set_brightness(uint8_t brightness);

/**
 * @brief Set effect speed
 * @param speed Speed value (1-10)
 * @return esp_err_t
 */
esp_err_t led_effects_set_speed(uint8_t speed);

/**
 * @brief Stop current effect
 * @return esp_err_t
 */
esp_err_t led_effects_stop_effect(void);

/**
 * @brief Get current effect state
 * @param state Output state structure
 * @return esp_err_t
 */
esp_err_t led_effects_get_state(led_effect_state_t *state);

/* Predefined effect parameter sets */
extern const led_effect_params_t LED_PARAMS_WARM_WHITE;
extern const led_effect_params_t LED_PARAMS_COOL_WHITE;
extern const led_effect_params_t LED_PARAMS_RAINBOW_FAST;
extern const led_effect_params_t LED_PARAMS_RAINBOW_SLOW;
extern const led_effect_params_t LED_PARAMS_FIRE_INTENSE;
extern const led_effect_params_t LED_PARAMS_BREATHING_BLUE;
extern const led_effect_params_t LED_PARAMS_WAVE_GREEN;

#ifdef __cplusplus
}
#endif

#endif /* LED_EFFECTS_H */
