/**
 * @file config_manager.h
 * @brief Configuration Management System Header
 * @version 1.0.0
 * @date 2025-06-27
 */

#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include "esp_err.h"
#include "esp_log.h"
#include "led_effects.h"
#include <stdint.h>
#include <stdbool.h>

#ifdef __cplusplus
extern "C" {
#endif

#define CONFIG_VERSION              1
#define MAX_DEVICE_NAME_LEN         32

/* LED configuration */
typedef struct {
    uint8_t brightness;                 // Default brightness (0-255)
    led_effect_type_t default_effect;   // Default LED effect
    bool auto_brightness;               // Auto brightness adjustment
    uint8_t night_mode_brightness;      // Night mode brightness
} led_config_t;

/* Sound configuration */
typedef struct {
    uint8_t sensitivity;                // Sensitivity level (1-10)
    uint16_t threshold_low;             // Low threshold
    uint16_t threshold_medium;          // Medium threshold
    uint16_t threshold_high;            // High threshold
    bool enable_detection;              // Enable sound detection
} sound_config_t;

/* Button configuration */
typedef struct {
    uint32_t long_press_duration;       // Long press duration (ms)
    uint32_t debounce_time;             // Debounce time (ms)
    bool enable_long_press;             // Enable long press detection
} button_config_t;

/* BLE configuration */
typedef struct {
    bool enable;                        // Enable BLE
    bool auto_advertise;                // Auto start advertising
    uint32_t connection_timeout;        // Connection timeout (ms)
    int8_t tx_power;                    // TX power level
} ble_config_t;

/* System configuration */
typedef struct {
    esp_log_level_t log_level;          // Log level
    uint32_t monitor_interval;          // System monitor interval (ms)
    uint32_t auto_sleep_timeout;        // Auto sleep timeout (ms, 0=disabled)
    uint32_t watchdog_timeout;          // Watchdog timeout (seconds)
} system_config_t;

/* Complete device configuration */
typedef struct {
    uint32_t version;                   // Configuration version
    char device_name[MAX_DEVICE_NAME_LEN]; // Device name
    led_config_t led_config;            // LED configuration
    sound_config_t sound_config;        // Sound configuration
    button_config_t button_config;      // Button configuration
    ble_config_t ble_config;            // BLE configuration
    system_config_t system_config;      // System configuration
} device_config_t;

/**
 * @brief Initialize configuration manager
 * @return esp_err_t
 */
esp_err_t config_manager_init(void);

/**
 * @brief Load configuration from NVS
 * @return esp_err_t
 */
esp_err_t config_manager_load(void);

/**
 * @brief Save configuration to NVS
 * @return esp_err_t
 */
esp_err_t config_manager_save(void);

/**
 * @brief Get current configuration
 * @param config Output configuration structure
 * @return esp_err_t
 */
esp_err_t config_manager_get_config(device_config_t *config);

/**
 * @brief Set configuration
 * @param config Input configuration structure
 * @return esp_err_t
 */
esp_err_t config_manager_set_config(const device_config_t *config);

/**
 * @brief Reset configuration to defaults
 * @return esp_err_t
 */
esp_err_t config_manager_reset_to_defaults(void);

/**
 * @brief Get LED configuration
 * @param led_config Output LED configuration
 * @return esp_err_t
 */
esp_err_t config_manager_get_led_config(led_config_t *led_config);

/**
 * @brief Set LED configuration
 * @param led_config Input LED configuration
 * @return esp_err_t
 */
esp_err_t config_manager_set_led_config(const led_config_t *led_config);

/**
 * @brief Get sound configuration
 * @param sound_config Output sound configuration
 * @return esp_err_t
 */
esp_err_t config_manager_get_sound_config(sound_config_t *sound_config);

/**
 * @brief Set sound configuration
 * @param sound_config Input sound configuration
 * @return esp_err_t
 */
esp_err_t config_manager_set_sound_config(const sound_config_t *sound_config);

/**
 * @brief Get button configuration
 * @param button_config Output button configuration
 * @return esp_err_t
 */
esp_err_t config_manager_get_button_config(button_config_t *button_config);

/**
 * @brief Set button configuration
 * @param button_config Input button configuration
 * @return esp_err_t
 */
esp_err_t config_manager_set_button_config(const button_config_t *button_config);

/**
 * @brief Get BLE configuration
 * @param ble_config Output BLE configuration
 * @return esp_err_t
 */
esp_err_t config_manager_get_ble_config(ble_config_t *ble_config);

/**
 * @brief Set BLE configuration
 * @param ble_config Input BLE configuration
 * @return esp_err_t
 */
esp_err_t config_manager_set_ble_config(const ble_config_t *ble_config);

/**
 * @brief Get system configuration
 * @param system_config Output system configuration
 * @return esp_err_t
 */
esp_err_t config_manager_get_system_config(system_config_t *system_config);

/**
 * @brief Set system configuration
 * @param system_config Input system configuration
 * @return esp_err_t
 */
esp_err_t config_manager_set_system_config(const system_config_t *system_config);

/**
 * @brief Check if configuration is loaded
 * @return bool
 */
bool config_manager_is_loaded(void);

#ifdef __cplusplus
}
#endif

#endif /* CONFIG_MANAGER_H */
