/**
 * @file led_effects.c
 * @brief Advanced LED Effects Manager for WS2812
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "led_effects.h"
#include "base_config.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include <math.h>

static const char *TAG = "LED_EFFECTS";

/* LED effects state */
static led_effect_state_t g_effect_state = {0};
static TaskHandle_t g_effects_task_handle = NULL;
static QueueHandle_t g_effects_queue = NULL;
static bool g_effects_running = false;

/* Effect parameters */
static led_effect_params_t g_current_params = {0};
static uint32_t g_effect_start_time = 0;
static uint32_t g_frame_counter = 0;

/**
 * @brief Initialize LED effects manager
 */
esp_err_t led_effects_init(void)
{
    ESP_LOGI(TAG, "Initializing LED effects manager...");
    
    // Create effects command queue
    g_effects_queue = xQueueCreate(LED_EFFECTS_QUEUE_SIZE, sizeof(led_effect_command_t));
    if (g_effects_queue == NULL) {
        ESP_LOGE(TAG, "Failed to create effects queue");
        return ESP_ERR_NO_MEM;
    }
    
    // Initialize state
    memset(&g_effect_state, 0, sizeof(g_effect_state));
    g_effect_state.current_effect = LED_EFFECT_OFF;
    g_effect_state.brightness = 100;
    g_effect_state.speed = 5;
    
    ESP_LOGI(TAG, "LED effects manager initialized");
    return ESP_OK;
}

/**
 * @brief Start LED effects task
 */
esp_err_t led_effects_start(void)
{
    if (g_effects_running) {
        ESP_LOGW(TAG, "LED effects already running");
        return ESP_OK;
    }
    
    BaseType_t ret = xTaskCreate(
        led_effects_task,
        "led_effects",
        TASK_STACK_SIZE_MEDIUM,
        NULL,
        TASK_PRIORITY_NORMAL,
        &g_effects_task_handle
    );
    
    if (ret != pdPASS) {
        ESP_LOGE(TAG, "Failed to create effects task");
        return ESP_ERR_NO_MEM;
    }
    
    g_effects_running = true;
    ESP_LOGI(TAG, "LED effects started");
    return ESP_OK;
}

/**
 * @brief Stop LED effects
 */
esp_err_t led_effects_stop(void)
{
    if (!g_effects_running) {
        return ESP_OK;
    }
    
    g_effects_running = false;
    
    if (g_effects_task_handle) {
        vTaskDelete(g_effects_task_handle);
        g_effects_task_handle = NULL;
    }
    
    // Turn off all LEDs
    ws2812_clear_all();
    ws2812_refresh();
    
    ESP_LOGI(TAG, "LED effects stopped");
    return ESP_OK;
}

/**
 * @brief Set LED effect
 */
esp_err_t led_effects_set_effect(led_effect_type_t effect, const led_effect_params_t *params)
{
    if (g_effects_queue == NULL) {
        return ESP_ERR_INVALID_STATE;
    }
    
    led_effect_command_t cmd;
    cmd.type = LED_CMD_SET_EFFECT;
    cmd.effect = effect;
    
    if (params) {
        memcpy(&cmd.params, params, sizeof(led_effect_params_t));
    } else {
        // Use default parameters
        cmd.params.primary_color = COLOR_WHITE;
        cmd.params.secondary_color = COLOR_OFF;
        cmd.params.brightness = 100;
        cmd.params.speed = 5;
        cmd.params.duration_ms = 0; // Infinite
    }
    
    if (xQueueSend(g_effects_queue, &cmd, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to send effect command");
        return ESP_ERR_TIMEOUT;
    }
    
    return ESP_OK;
}

/**
 * @brief Set effect brightness
 */
esp_err_t led_effects_set_brightness(uint8_t brightness)
{
    if (g_effects_queue == NULL) {
        return ESP_ERR_INVALID_STATE;
    }
    
    led_effect_command_t cmd;
    cmd.type = LED_CMD_SET_BRIGHTNESS;
    cmd.params.brightness = brightness;
    
    if (xQueueSend(g_effects_queue, &cmd, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to send brightness command");
        return ESP_ERR_TIMEOUT;
    }
    
    return ESP_OK;
}

/**
 * @brief Set effect speed
 */
esp_err_t led_effects_set_speed(uint8_t speed)
{
    if (g_effects_queue == NULL) {
        return ESP_ERR_INVALID_STATE;
    }
    
    led_effect_command_t cmd;
    cmd.type = LED_CMD_SET_SPEED;
    cmd.params.speed = speed;
    
    if (xQueueSend(g_effects_queue, &cmd, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to send speed command");
        return ESP_ERR_TIMEOUT;
    }
    
    return ESP_OK;
}

/**
 * @brief HSV to RGB conversion
 */
static rgb_color_t hsv_to_rgb(uint16_t hue, uint8_t saturation, uint8_t value)
{
    rgb_color_t rgb = {0, 0, 0};
    
    if (saturation == 0) {
        rgb.r = rgb.g = rgb.b = value;
        return rgb;
    }
    
    uint8_t region = hue / 43;
    uint8_t remainder = (hue - (region * 43)) * 6;
    
    uint8_t p = (value * (255 - saturation)) >> 8;
    uint8_t q = (value * (255 - ((saturation * remainder) >> 8))) >> 8;
    uint8_t t = (value * (255 - ((saturation * (255 - remainder)) >> 8))) >> 8;
    
    switch (region) {
        case 0: rgb.r = value; rgb.g = t; rgb.b = p; break;
        case 1: rgb.r = q; rgb.g = value; rgb.b = p; break;
        case 2: rgb.r = p; rgb.g = value; rgb.b = t; break;
        case 3: rgb.r = p; rgb.g = q; rgb.b = value; break;
        case 4: rgb.r = t; rgb.g = p; rgb.b = value; break;
        default: rgb.r = value; rgb.g = p; rgb.b = q; break;
    }
    
    return rgb;
}

/**
 * @brief Apply brightness to color
 */
static rgb_color_t apply_brightness(rgb_color_t color, uint8_t brightness)
{
    rgb_color_t result;
    result.r = (color.r * brightness) / 255;
    result.g = (color.g * brightness) / 255;
    result.b = (color.b * brightness) / 255;
    return result;
}

/**
 * @brief Solid color effect
 */
static void effect_solid_color(void)
{
    rgb_color_t color = apply_brightness(g_current_params.primary_color, g_current_params.brightness);
    
    for (int i = 0; i < WS2812_LED_COUNT; i++) {
        ws2812_set_pixel(i, color);
    }
}

/**
 * @brief Breathing effect
 */
static void effect_breathing(void)
{
    uint32_t elapsed = esp_timer_get_time() / 1000 - g_effect_start_time;
    uint32_t period = 2000 / g_current_params.speed; // Period in ms
    
    float phase = (float)(elapsed % period) / period * 2.0f * M_PI;
    uint8_t brightness = (uint8_t)((sin(phase) + 1.0f) * 0.5f * g_current_params.brightness);
    
    rgb_color_t color = apply_brightness(g_current_params.primary_color, brightness);
    
    for (int i = 0; i < WS2812_LED_COUNT; i++) {
        ws2812_set_pixel(i, color);
    }
}

/**
 * @brief Rainbow effect
 */
static void effect_rainbow(void)
{
    uint32_t elapsed = esp_timer_get_time() / 1000 - g_effect_start_time;
    uint32_t hue_offset = (elapsed * g_current_params.speed) / 10;
    
    for (int i = 0; i < WS2812_LED_COUNT; i++) {
        uint16_t hue = ((i * 360 / WS2812_LED_COUNT) + hue_offset) % 360;
        rgb_color_t color = hsv_to_rgb(hue, 255, g_current_params.brightness);
        ws2812_set_pixel(i, color);
    }
}

/**
 * @brief Wave effect
 */
static void effect_wave(void)
{
    uint32_t elapsed = esp_timer_get_time() / 1000 - g_effect_start_time;
    float time_factor = (float)elapsed / 1000.0f * g_current_params.speed;
    
    for (int i = 0; i < WS2812_LED_COUNT; i++) {
        float wave = sin(time_factor + (float)i * 0.5f);
        uint8_t brightness = (uint8_t)((wave + 1.0f) * 0.5f * g_current_params.brightness);
        
        rgb_color_t color = apply_brightness(g_current_params.primary_color, brightness);
        ws2812_set_pixel(i, color);
    }
}

/**
 * @brief Fire effect
 */
static void effect_fire(void)
{
    static uint8_t heat[WS2812_LED_COUNT];
    
    // Cool down every cell a little
    for (int i = 0; i < WS2812_LED_COUNT; i++) {
        heat[i] = (heat[i] > 55) ? heat[i] - 55 : 0;
    }
    
    // Heat from each cell drifts 'up' and diffuses a little
    for (int k = WS2812_LED_COUNT - 1; k >= 2; k--) {
        heat[k] = (heat[k - 1] + heat[k - 2] + heat[k - 2]) / 3;
    }
    
    // Randomly ignite new 'sparks' of heat near the bottom
    if (esp_random() % 255 < g_current_params.speed * 2) {
        int y = esp_random() % 7;
        heat[y] = (heat[y] + esp_random() % 160 + 96) > 255 ? 255 : heat[y] + esp_random() % 160 + 96;
    }
    
    // Map from heat cells to LED colors
    for (int j = 0; j < WS2812_LED_COUNT; j++) {
        rgb_color_t color;
        uint8_t t192 = (heat[j] > 191) ? 255 : heat[j] * 4 / 3;
        uint8_t heatramp = t192 & 0x3F; // 0..63
        heatramp <<= 2; // scale up to 0..252
        
        if (t192 & 0x80) {
            // hottest
            color.r = 255;
            color.g = 255;
            color.b = heatramp;
        } else if (t192 & 0x40) {
            // middle
            color.r = 255;
            color.g = heatramp;
            color.b = 0;
        } else {
            // coolest
            color.r = heatramp;
            color.g = 0;
            color.b = 0;
        }
        
        color = apply_brightness(color, g_current_params.brightness);
        ws2812_set_pixel(j, color);
    }
}

/**
 * @brief Process effect commands
 */
static void process_effect_commands(void)
{
    led_effect_command_t cmd;
    
    while (xQueueReceive(g_effects_queue, &cmd, 0) == pdTRUE) {
        switch (cmd.type) {
        case LED_CMD_SET_EFFECT:
            if (cmd.effect != g_effect_state.current_effect) {
                ESP_LOGI(TAG, "Switching to effect: %d", cmd.effect);
                g_effect_state.current_effect = cmd.effect;
                g_current_params = cmd.params;
                g_effect_start_time = esp_timer_get_time() / 1000;
                g_frame_counter = 0;
            }
            break;
            
        case LED_CMD_SET_BRIGHTNESS:
            g_current_params.brightness = cmd.params.brightness;
            g_effect_state.brightness = cmd.params.brightness;
            ESP_LOGI(TAG, "Brightness set to: %d", cmd.params.brightness);
            break;
            
        case LED_CMD_SET_SPEED:
            g_current_params.speed = cmd.params.speed;
            g_effect_state.speed = cmd.params.speed;
            ESP_LOGI(TAG, "Speed set to: %d", cmd.params.speed);
            break;
            
        case LED_CMD_STOP:
            g_effect_state.current_effect = LED_EFFECT_OFF;
            ws2812_clear_all();
            break;
        }
    }
}

/**
 * @brief LED effects task
 */
void led_effects_task(void *pvParameters)
{
    ESP_LOGI(TAG, "LED effects task started");
    
    TickType_t last_wake_time = xTaskGetTickCount();
    const TickType_t frame_delay = pdMS_TO_TICKS(LED_EFFECTS_FRAME_RATE_MS);
    
    while (g_effects_running) {
        // Process commands
        process_effect_commands();
        
        // Check if effect should stop due to duration
        if (g_current_params.duration_ms > 0) {
            uint32_t elapsed = esp_timer_get_time() / 1000 - g_effect_start_time;
            if (elapsed >= g_current_params.duration_ms) {
                g_effect_state.current_effect = LED_EFFECT_OFF;
            }
        }
        
        // Render current effect
        switch (g_effect_state.current_effect) {
        case LED_EFFECT_OFF:
            ws2812_clear_all();
            break;
        case LED_EFFECT_SOLID:
            effect_solid_color();
            break;
        case LED_EFFECT_BREATHING:
            effect_breathing();
            break;
        case LED_EFFECT_RAINBOW:
            effect_rainbow();
            break;
        case LED_EFFECT_WAVE:
            effect_wave();
            break;
        case LED_EFFECT_FIRE:
            effect_fire();
            break;
        default:
            break;
        }
        
        // Update display
        if (g_effect_state.current_effect != LED_EFFECT_OFF) {
            ws2812_refresh();
        }
        
        g_frame_counter++;
        
        // Wait for next frame
        vTaskDelayUntil(&last_wake_time, frame_delay);
    }
    
    ESP_LOGI(TAG, "LED effects task stopped");
    vTaskDelete(NULL);
}

/**
 * @brief Get current effect state
 */
esp_err_t led_effects_get_state(led_effect_state_t *state)
{
    if (state == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    memcpy(state, &g_effect_state, sizeof(led_effect_state_t));
    return ESP_OK;
}

/**
 * @brief Stop current effect
 */
esp_err_t led_effects_stop_effect(void)
{
    if (g_effects_queue == NULL) {
        return ESP_ERR_INVALID_STATE;
    }

    led_effect_command_t cmd;
    cmd.type = LED_CMD_STOP;

    if (xQueueSend(g_effects_queue, &cmd, pdMS_TO_TICKS(100)) != pdTRUE) {
        ESP_LOGE(TAG, "Failed to send stop command");
        return ESP_ERR_TIMEOUT;
    }

    return ESP_OK;
}
