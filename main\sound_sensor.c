/**
 * @file sound_sensor.c
 * @brief 声音传感器驱动实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "base_config.h"
#include "esp_log.h"
#include "esp_err.h"
#include "esp_adc/adc_oneshot.h"
#include "esp_adc/adc_cali.h"
#include "esp_adc/adc_cali_scheme.h"
#include "esp_timer.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include <string.h>

static const char *TAG = "SOUND_SENSOR";

/* ADC句柄 */
static adc_oneshot_unit_handle_t g_adc_handle = NULL;
static adc_cali_handle_t g_adc_cali_handle = NULL;
static bool g_sound_sensor_initialized = false;
static bool g_sound_sensor_running = false;

/* 声音数据缓冲区 */
#define SOUND_BUFFER_SIZE   100
static uint16_t g_sound_buffer[SOUND_BUFFER_SIZE];
static uint16_t g_buffer_index = 0;

/* 声音统计数据 */
static uint16_t g_current_level = 0;
static uint16_t g_average_level = 0;
static uint16_t g_peak_level = 0;
static uint32_t g_last_peak_time = 0;

/**
 * @brief 初始化ADC校准
 */
static esp_err_t adc_calibration_init(void)
{
    esp_err_t ret = ESP_FAIL;
    bool calibrated = false;

#if ADC_CALI_SCHEME_CURVE_FITTING_SUPPORTED
    if (!calibrated) {
        ESP_LOGI(TAG, "校准方案版本为曲线拟合");
        adc_cali_curve_fitting_config_t cali_config = {
            .unit_id = SOUND_SENSOR_ADC_UNIT,
            .atten = SOUND_SENSOR_ADC_ATTEN,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        ret = adc_cali_create_scheme_curve_fitting(&cali_config, &g_adc_cali_handle);
        if (ret == ESP_OK) {
            calibrated = true;
        }
    }
#endif

#if ADC_CALI_SCHEME_LINE_FITTING_SUPPORTED
    if (!calibrated) {
        ESP_LOGI(TAG, "校准方案版本为线性拟合");
        adc_cali_line_fitting_config_t cali_config = {
            .unit_id = SOUND_SENSOR_ADC_UNIT,
            .atten = SOUND_SENSOR_ADC_ATTEN,
            .bitwidth = ADC_BITWIDTH_DEFAULT,
        };
        ret = adc_cali_create_scheme_line_fitting(&cali_config, &g_adc_cali_handle);
        if (ret == ESP_OK) {
            calibrated = true;
        }
    }
#endif

    if (!calibrated) {
        ESP_LOGW(TAG, "ADC校准失败，使用原始值");
        g_adc_cali_handle = NULL;
    }

    return ret;
}

/**
 * @brief 初始化声音传感器
 */
esp_err_t sound_sensor_init(void)
{
    if (g_sound_sensor_initialized) {
        ESP_LOGW(TAG, "声音传感器已初始化");
        return ESP_OK;
    }
    
    ESP_LOGI(TAG, "初始化声音传感器...");
    
    // 配置ADC
    adc_oneshot_unit_init_cfg_t init_config = {
        .unit_id = SOUND_SENSOR_ADC_UNIT,
    };
    
    esp_err_t ret = adc_oneshot_new_unit(&init_config, &g_adc_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "创建ADC单元失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 配置ADC通道
    adc_oneshot_chan_cfg_t config = {
        .bitwidth = ADC_BITWIDTH_DEFAULT,
        .atten = SOUND_SENSOR_ADC_ATTEN,
    };
    
    ret = adc_oneshot_config_channel(g_adc_handle, SOUND_SENSOR_ADC_CHANNEL, &config);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "配置ADC通道失败: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 初始化ADC校准
    adc_calibration_init();
    
    // 初始化缓冲区
    memset(g_sound_buffer, 0, sizeof(g_sound_buffer));
    g_buffer_index = 0;
    
    g_sound_sensor_initialized = true;
    ESP_LOGI(TAG, "声音传感器初始化完成");
    return ESP_OK;
}

/**
 * @brief 启动声音传感器
 */
esp_err_t sound_sensor_start(void)
{
    if (!g_sound_sensor_initialized) {
        ESP_LOGE(TAG, "声音传感器未初始化");
        return ESP_ERR_INVALID_STATE;
    }
    
    g_sound_sensor_running = true;
    ESP_LOGI(TAG, "声音传感器启动");
    return ESP_OK;
}

/**
 * @brief 停止声音传感器
 */
esp_err_t sound_sensor_stop(void)
{
    g_sound_sensor_running = false;
    ESP_LOGI(TAG, "声音传感器停止");
    return ESP_OK;
}

/**
 * @brief 读取原始ADC值
 */
esp_err_t sound_sensor_read_raw(int *raw_value)
{
    if (!g_sound_sensor_initialized || !g_sound_sensor_running) {
        return ESP_ERR_INVALID_STATE;
    }
    
    return adc_oneshot_read(g_adc_handle, SOUND_SENSOR_ADC_CHANNEL, raw_value);
}

/**
 * @brief 读取校准后的电压值
 */
esp_err_t sound_sensor_read_voltage(int *voltage_mv)
{
    int raw_value;
    esp_err_t ret = sound_sensor_read_raw(&raw_value);
    if (ret != ESP_OK) {
        return ret;
    }
    
    if (g_adc_cali_handle) {
        ret = adc_cali_raw_to_voltage(g_adc_cali_handle, raw_value, voltage_mv);
    } else {
        // 如果没有校准，使用简单的线性转换
        *voltage_mv = (raw_value * 3300) / 4095;
        ret = ESP_OK;
    }
    
    return ret;
}

/**
 * @brief 读取声音级别 (0-1000)
 */
uint16_t sound_sensor_read_level(void)
{
    int voltage_mv;
    esp_err_t ret = sound_sensor_read_voltage(&voltage_mv);
    if (ret != ESP_OK) {
        return 0;
    }
    
    // 将电压转换为声音级别 (0-1000)
    // 假设静音时电压为1650mV (3.3V/2)，最大声音时电压为3300mV
    if (voltage_mv < 1650) {
        voltage_mv = 1650;
    }
    
    uint16_t level = ((voltage_mv - 1650) * 1000) / (3300 - 1650);
    if (level > 1000) {
        level = 1000;
    }
    
    return level;
}

/**
 * @brief 更新声音统计数据
 */
void sound_sensor_update_stats(void)
{
    if (!g_sound_sensor_initialized || !g_sound_sensor_running) {
        return;
    }
    
    // 读取当前声音级别
    g_current_level = sound_sensor_read_level();
    
    // 更新缓冲区
    g_sound_buffer[g_buffer_index] = g_current_level;
    g_buffer_index = (g_buffer_index + 1) % SOUND_BUFFER_SIZE;
    
    // 计算平均值
    uint32_t sum = 0;
    for (int i = 0; i < SOUND_BUFFER_SIZE; i++) {
        sum += g_sound_buffer[i];
    }
    g_average_level = sum / SOUND_BUFFER_SIZE;
    
    // 更新峰值
    if (g_current_level > g_peak_level) {
        g_peak_level = g_current_level;
        g_last_peak_time = esp_timer_get_time() / 1000;
    }
    
    // 峰值衰减 (每秒衰减10%)
    uint32_t current_time = esp_timer_get_time() / 1000;
    if (current_time - g_last_peak_time > 1000) {
        g_peak_level = (g_peak_level * 90) / 100;
        g_last_peak_time = current_time;
    }
}

/**
 * @brief 获取当前声音级别
 */
uint16_t sound_sensor_get_current_level(void)
{
    return g_current_level;
}

/**
 * @brief 获取平均声音级别
 */
uint16_t sound_sensor_get_average_level(void)
{
    return g_average_level;
}

/**
 * @brief 获取峰值声音级别
 */
uint16_t sound_sensor_get_peak_level(void)
{
    return g_peak_level;
}

/**
 * @brief 检测声音事件
 */
bool sound_sensor_detect_event(uint16_t threshold)
{
    return g_current_level > threshold;
}

/**
 * @brief 检测声音突变
 */
bool sound_sensor_detect_spike(uint16_t threshold)
{
    // 检测当前级别是否比平均级别高出阈值
    return (g_current_level > g_average_level + threshold);
}

/**
 * @brief 获取声音传感器状态
 */
bool sound_sensor_is_running(void)
{
    return g_sound_sensor_running;
}

/**
 * @brief 重置声音统计数据
 */
void sound_sensor_reset_stats(void)
{
    memset(g_sound_buffer, 0, sizeof(g_sound_buffer));
    g_buffer_index = 0;
    g_current_level = 0;
    g_average_level = 0;
    g_peak_level = 0;
    g_last_peak_time = 0;
}
