/**
 * @file main.c
 * @brief TIMO Base Station Main Program for ESP32-C2
 * @version 1.0.0
 * @date 2025-06-27
 *
 * Main Features:
 * - WS2812 RGB LED control
 * - Sound sensor monitoring
 * - User button handling
 * - Wireless charging management
 * - Bluetooth communication
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/queue.h"
#include "esp_system.h"
// #include "esp_wifi.h" // ESP32-C2 project does not use WiFi
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
// #include "esp_netif.h" // ESP32-C2 project does not use network
#include "esp_err.h"
#include "esp_timer.h"
// #include "esp_sleep.h" // ESP32-C2 sleep functionality is limited
#include "esp_pm.h"
// ESP32-C2 Bluetooth headers are handled in bluetooth_comm.c

#include "base_config.h"
#include "base_main.h"

static const char *TAG = "BASE_MAIN";

/**
 * @brief System initialization
 */
static void system_init(void)
{
    ESP_LOGI(TAG, "=== TIMO Base Station Device Starting ===");
    ESP_LOGI(TAG, "Version: %s", "1.0.0");
    ESP_LOGI(TAG, "Build time: %s %s", __DATE__, __TIME__);
    ESP_LOGI(TAG, "Chip model: ESP32-C2");

    // Initialize NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    ESP_LOGI(TAG, "NVS initialization completed");

    // Initialize event loop (no network interface used)
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    ESP_LOGI(TAG, "Event loop initialization completed");

    // Temporarily disable Bluetooth controller initialization
    /*
    esp_bt_controller_config_t bt_cfg = BT_CONTROLLER_INIT_CONFIG_DEFAULT();
    ret = esp_bt_controller_init(&bt_cfg);
    if (ret) {
        ESP_LOGE(TAG, "Bluetooth controller init failed: %s", esp_err_to_name(ret));
        return;
    }

    ret = esp_bt_controller_enable(ESP_BT_MODE_BLE);
    if (ret) {
        ESP_LOGE(TAG, "Bluetooth controller enable failed: %s", esp_err_to_name(ret));
        return;
    }
    */

    // Temporarily disable Bluetooth stack
    /*
    ret = esp_bluedroid_init();
    if (ret) {
        ESP_LOGE(TAG, "Bluetooth stack init failed: %s", esp_err_to_name(ret));
        return;
    }

    ret = esp_bluedroid_enable();
    if (ret) {
        ESP_LOGE(TAG, "Bluetooth stack enable failed: %s", esp_err_to_name(ret));
        return;
    }

    ESP_LOGI(TAG, "Bluetooth initialization completed");
    */

    // Initialize power management
    esp_pm_config_t pm_config = {
        .max_freq_mhz = 120,
        .min_freq_mhz = 40,
        .light_sleep_enable = true
    };
    ESP_ERROR_CHECK(esp_pm_configure(&pm_config));
    ESP_LOGI(TAG, "Power management initialization completed");
}

/**
 * @brief Main program entry point
 */
void app_main(void)
{
    // System initialization
    system_init();

    // Start base station application
    base_main_start();

    ESP_LOGI(TAG, "Base station device startup completed, entering main loop");

    // Main loop - system monitoring
    while (1) {
        // Print system status
        ESP_LOGI(TAG, "Base station device running... Free heap: %lu bytes", (unsigned long)esp_get_free_heap_size());

        // Delay 10 seconds
        vTaskDelay(pdMS_TO_TICKS(10000));
    }
}
