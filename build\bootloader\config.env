{"COMPONENT_KCONFIGS": "D:/APP/Espressif/esp/v5.3.3/esp-idf/components/efuse/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_common/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_hw_support/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_system/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/freertos/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/hal/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/log/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/newlib/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/soc/Kconfig;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "D:/APP/Espressif/esp/v5.3.3/esp-idf/components/bootloader/Kconfig.projbuild;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_app_format/Kconfig.projbuild;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_rom/Kconfig.projbuild;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esptool_py/Kconfig.projbuild;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "D:/APP/Espressif/esp/v5.3.3/esp-idf/components/bootloader/sdkconfig.rename;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_hw_support/sdkconfig.rename;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esp_system/sdkconfig.rename;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/esptool_py/sdkconfig.rename;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/freertos/sdkconfig.rename;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/hal/sdkconfig.rename;D:/APP/Espressif/esp/v5.3.3/esp-idf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32c2", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.3.3", "IDF_ENV_FPGA": "", "IDF_PATH": "D:/APP/Espressif/esp/v5.3.3/esp-idf", "COMPONENT_KCONFIGS_SOURCE_FILE": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "E:/Electronics/MyProject/ESP/Timo_main/2_base_station_firmware/build/bootloader/kconfigs_projbuild.in"}