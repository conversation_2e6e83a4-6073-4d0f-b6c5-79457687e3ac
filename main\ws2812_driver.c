/**
 * @file ws2812_driver.c
 * @brief WS2812 RGB LED驱动实现
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "base_config.h"
#include "esp_log.h"
#include "esp_err.h"
#include "driver/spi_master.h"
#include "driver/gpio.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

// ESP32-C2不支持RMT，使用SPI驱动WS2812

static const char *TAG = "WS2812";

/* SPI设备句柄 */
static spi_device_handle_t g_spi_device = NULL;
static bool g_ws2812_initialized = false;

/* LED缓冲区 */
static rgb_color_t g_led_buffer[WS2812_LED_COUNT];
static uint8_t g_global_brightness = 100;

/* WS2812时序参数 (基于SPI 3.2MHz) */
#define WS2812_SPI_FREQ_HZ      3200000  // 3.2MHz SPI频率
#define WS2812_BIT_0            0xC0     // 0码: 110000 (高电平短，低电平长)
#define WS2812_BIT_1            0xF8     // 1码: 111110 (高电平长，低电平短)
#define WS2812_RESET_BYTES      50       // 复位信号字节数 (>50us低电平)

/**
 * @brief 初始化WS2812 LED驱动
 * @note ESP32-C2不支持RMT，使用SPI驱动WS2812
 */
esp_err_t ws2812_init(void)
{
    if (g_ws2812_initialized) {
        ESP_LOGW(TAG, "WS2812已初始化");
        return ESP_OK;
    }

    ESP_LOGI(TAG, "初始化WS2812 LED驱动 (SPI模式)...");

    // 配置SPI总线
    spi_bus_config_t bus_config = {
        .mosi_io_num = WS2812_GPIO,
        .miso_io_num = -1,
        .sclk_io_num = -1,
        .quadwp_io_num = -1,
        .quadhd_io_num = -1,
        .max_transfer_sz = (WS2812_LED_COUNT * 24 + WS2812_RESET_BYTES) * 8,
        .flags = SPICOMMON_BUSFLAG_MASTER | SPICOMMON_BUSFLAG_MOSI,
    };

    esp_err_t ret = spi_bus_initialize(SPI2_HOST, &bus_config, SPI_DMA_DISABLED);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI总线初始化失败: %s", esp_err_to_name(ret));
        return ret;
    }

    // 配置SPI设备
    spi_device_interface_config_t dev_config = {
        .clock_speed_hz = WS2812_SPI_FREQ_HZ,
        .mode = 0,
        .spics_io_num = -1,
        .queue_size = 1,
        .flags = SPI_DEVICE_NO_DUMMY,
    };

    ret = spi_bus_add_device(SPI2_HOST, &dev_config, &g_spi_device);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI设备添加失败: %s", esp_err_to_name(ret));
        spi_bus_free(SPI2_HOST);
        return ret;
    }

    // 初始化LED缓冲区
    memset(g_led_buffer, 0, sizeof(g_led_buffer));

    g_ws2812_initialized = true;
    ESP_LOGI(TAG, "WS2812 LED驱动初始化完成 (SPI频率: %.1fMHz, GPIO%d)",
             WS2812_SPI_FREQ_HZ / 1000000.0, WS2812_GPIO);
    return ESP_OK;
}

/**
 * @brief 将RGB颜色转换为SPI数据
 * @param color RGB颜色值
 * @param spi_data 输出的SPI数据缓冲区 (24字节)
 */
static void rgb_to_spi_data(rgb_color_t color, uint8_t *spi_data)
{
    uint32_t grb = ((uint32_t)color.g << 16) | ((uint32_t)color.r << 8) | color.b;

    for (int i = 0; i < 24; i++) {
        if (grb & (1 << (23 - i))) {
            spi_data[i] = WS2812_BIT_1;
        } else {
            spi_data[i] = WS2812_BIT_0;
        }
    }
}

/**
 * @brief 启动WS2812 LED驱动
 */
esp_err_t ws2812_start(void)
{
    if (!g_ws2812_initialized) {
        ESP_LOGE(TAG, "WS2812未初始化");
        return ESP_ERR_INVALID_STATE;
    }

    ESP_LOGI(TAG, "启动WS2812 LED驱动");

    // 发送复位信号，清空所有LED
    return ws2812_clear_all();
}

/**
 * @brief 设置单个LED颜色
 */
esp_err_t ws2812_set_pixel(uint16_t index, rgb_color_t color)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    if (index >= WS2812_LED_COUNT) {
        return ESP_ERR_INVALID_ARG;
    }

    // 应用全局亮度
    rgb_color_t adjusted_color = {
        .r = (color.r * g_global_brightness) / 255,
        .g = (color.g * g_global_brightness) / 255,
        .b = (color.b * g_global_brightness) / 255
    };

    // 更新缓冲区
    g_led_buffer[index] = adjusted_color;

    return ESP_OK;
}

/**
 * @brief 设置所有LED颜色
 */
esp_err_t ws2812_set_all_pixels(rgb_color_t color)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = ESP_OK;
    for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
        ret = ws2812_set_pixel(i, color);
        if (ret != ESP_OK) {
            break;
        }
    }

    return ret;
}

/**
 * @brief 清空所有LED
 */
esp_err_t ws2812_clear_all(void)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    rgb_color_t black = COLOR_OFF;
    return ws2812_set_all_pixels(black);
}

/**
 * @brief 刷新LED显示
 * @note 通过SPI发送所有LED数据
 */
esp_err_t ws2812_refresh(void)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    // 计算总数据长度：LED数据 + 复位信号
    size_t total_bytes = WS2812_LED_COUNT * 24 + WS2812_RESET_BYTES;
    uint8_t *spi_data = malloc(total_bytes);
    if (!spi_data) {
        ESP_LOGE(TAG, "分配SPI数据缓冲区失败");
        return ESP_ERR_NO_MEM;
    }

    // 转换LED数据
    for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
        rgb_to_spi_data(g_led_buffer[i], &spi_data[i * 24]);
    }

    // 添加复位信号 (低电平)
    memset(&spi_data[WS2812_LED_COUNT * 24], 0x00, WS2812_RESET_BYTES);

    // 发送SPI数据
    spi_transaction_t trans = {
        .length = total_bytes * 8,  // 位长度
        .tx_buffer = spi_data,
        .rx_buffer = NULL
    };

    esp_err_t ret = spi_device_transmit(g_spi_device, &trans);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "SPI传输失败: %s", esp_err_to_name(ret));
    }

    free(spi_data);
    return ret;
}

/**
 * @brief 设置全局亮度
 */
esp_err_t ws2812_set_brightness(uint8_t brightness)
{
    // uint8_t类型范围0-255，无需检查上限
    g_global_brightness = brightness;

    ESP_LOGI(TAG, "设置全局亮度: %d/255", brightness);

    // 重新应用当前颜色（亮度会在ws2812_set_pixel中应用）
    if (g_ws2812_initialized) {
        // 保存原始颜色
        rgb_color_t temp_buffer[WS2812_LED_COUNT];
        memcpy(temp_buffer, g_led_buffer, sizeof(temp_buffer));

        // 重新设置所有像素以应用新亮度
        for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
            ws2812_set_pixel(i, temp_buffer[i]);
        }

        return ws2812_refresh();
    }

    return ESP_OK;
}

/**
 * @brief 获取全局亮度
 */
uint8_t ws2812_get_brightness(void)
{
    return g_global_brightness;
}

/**
 * @brief 彩虹效果
 */
esp_err_t ws2812_rainbow_effect(uint8_t brightness, uint16_t speed)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    static uint8_t hue_offset = 0;
    
    for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
        uint8_t hue = (i * 255 / WS2812_LED_COUNT + hue_offset) % 255;
        
        // HSV转RGB (简化版本)
        rgb_color_t color;
        if (hue < 85) {
            color.r = hue * 3;
            color.g = 255 - hue * 3;
            color.b = 0;
        } else if (hue < 170) {
            hue -= 85;
            color.r = 255 - hue * 3;
            color.g = 0;
            color.b = hue * 3;
        } else {
            hue -= 170;
            color.r = 0;
            color.g = hue * 3;
            color.b = 255 - hue * 3;
        }
        
        // 应用亮度
        color.r = (color.r * brightness) / 255;
        color.g = (color.g * brightness) / 255;
        color.b = (color.b * brightness) / 255;
        
        ws2812_set_pixel(i, color);
    }
    
    ws2812_refresh();
    
    hue_offset += speed;
    return ESP_OK;
}

/**
 * @brief 呼吸灯效果
 */
esp_err_t ws2812_breathing_effect(rgb_color_t color, uint16_t speed)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    static uint8_t brightness = 0;
    static int8_t direction = 1;
    
    // 计算亮度
    brightness += direction * speed;
    if (brightness >= 255) {
        brightness = 255;
        direction = -1;
    } else if (brightness <= 0) {
        brightness = 0;
        direction = 1;
    }
    
    // 应用亮度到颜色
    rgb_color_t dimmed_color = {
        .r = (color.r * brightness) / 255,
        .g = (color.g * brightness) / 255,
        .b = (color.b * brightness) / 255
    };
    
    ws2812_set_all_pixels(dimmed_color);
    return ws2812_refresh();
}

/**
 * @brief 波浪效果
 */
esp_err_t ws2812_wave_effect(rgb_color_t color, uint8_t brightness, uint16_t speed)
{
    if (!g_ws2812_initialized) {
        return ESP_ERR_INVALID_STATE;
    }
    
    static uint16_t wave_offset = 0;
    
    for (uint16_t i = 0; i < WS2812_LED_COUNT; i++) {
        // 计算波浪强度 (简化的正弦波)
        uint16_t wave_pos = (i * 255 / WS2812_LED_COUNT + wave_offset) % 255;
        uint8_t intensity;
        
        if (wave_pos < 128) {
            intensity = wave_pos * 2;
        } else {
            intensity = (255 - wave_pos) * 2;
        }
        
        // 应用强度到颜色
        rgb_color_t wave_color = {
            .r = (color.r * intensity * brightness) / (255 * 255),
            .g = (color.g * intensity * brightness) / (255 * 255),
            .b = (color.b * intensity * brightness) / (255 * 255)
        };
        
        ws2812_set_pixel(i, wave_color);
    }
    
    ws2812_refresh();
    
    wave_offset += speed;
    return ESP_OK;
}
