/**
 * @file config_manager.c
 * @brief Configuration Management System
 * @version 1.0.0
 * @date 2025-06-27
 */

#include "config_manager.h"
#include "base_config.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "nvs.h"
#include <string.h>

static const char *TAG = "CONFIG_MGR";
static const char *NVS_NAMESPACE = "timo_config";

/* Default configuration */
static const device_config_t g_default_config = {
    .version = CONFIG_VERSION,
    .device_name = "TIMO_Base",
    .led_config = {
        .brightness = 100,
        .default_effect = LED_EFFECT_BREATHING,
        .auto_brightness = true,
        .night_mode_brightness = 20
    },
    .sound_config = {
        .sensitivity = 5,
        .threshold_low = SOUND_THRESHOLD_LOW,
        .threshold_medium = SOUND_THRESHOLD_MEDIUM,
        .threshold_high = SOUND_THRESHOLD_HIGH,
        .enable_detection = true
    },
    .button_config = {
        .long_press_duration = 1000,
        .debounce_time = 50,
        .enable_long_press = true
    },
    .ble_config = {
        .enable = true,
        .auto_advertise = true,
        .connection_timeout = 30000,
        .tx_power = 3
    },
    .system_config = {
        .log_level = ESP_LOG_INFO,
        .monitor_interval = 1000,
        .auto_sleep_timeout = 0,
        .watchdog_timeout = 10
    }
};

/* Current configuration */
static device_config_t g_current_config;
static bool g_config_loaded = false;

/**
 * @brief Initialize configuration manager
 */
esp_err_t config_manager_init(void)
{
    ESP_LOGI(TAG, "Initializing configuration manager...");
    
    // Initialize NVS if not already done
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);
    
    // Load configuration from NVS
    ret = config_manager_load();
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to load config, using defaults");
        memcpy(&g_current_config, &g_default_config, sizeof(device_config_t));
        g_config_loaded = true;
        
        // Save default configuration
        config_manager_save();
    }
    
    ESP_LOGI(TAG, "Configuration manager initialized");
    return ESP_OK;
}

/**
 * @brief Load configuration from NVS
 */
esp_err_t config_manager_load(void)
{
    nvs_handle_t nvs_handle;
    esp_err_t ret;
    
    ret = nvs_open(NVS_NAMESPACE, NVS_READONLY, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS namespace: %s", esp_err_to_name(ret));
        return ret;
    }
    
    size_t required_size = sizeof(device_config_t);
    ret = nvs_get_blob(nvs_handle, "device_config", &g_current_config, &required_size);
    
    nvs_close(nvs_handle);
    
    if (ret == ESP_OK) {
        // Validate configuration version
        if (g_current_config.version != CONFIG_VERSION) {
            ESP_LOGW(TAG, "Config version mismatch (got %d, expected %d), using defaults",
                     g_current_config.version, CONFIG_VERSION);
            memcpy(&g_current_config, &g_default_config, sizeof(device_config_t));
        }
        g_config_loaded = true;
        ESP_LOGI(TAG, "Configuration loaded successfully");
    } else {
        ESP_LOGW(TAG, "Failed to load configuration: %s", esp_err_to_name(ret));
    }
    
    return ret;
}

/**
 * @brief Save configuration to NVS
 */
esp_err_t config_manager_save(void)
{
    if (!g_config_loaded) {
        ESP_LOGE(TAG, "Configuration not loaded");
        return ESP_ERR_INVALID_STATE;
    }
    
    nvs_handle_t nvs_handle;
    esp_err_t ret;
    
    ret = nvs_open(NVS_NAMESPACE, NVS_READWRITE, &nvs_handle);
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to open NVS namespace: %s", esp_err_to_name(ret));
        return ret;
    }
    
    ret = nvs_set_blob(nvs_handle, "device_config", &g_current_config, sizeof(device_config_t));
    if (ret == ESP_OK) {
        ret = nvs_commit(nvs_handle);
        if (ret == ESP_OK) {
            ESP_LOGI(TAG, "Configuration saved successfully");
        } else {
            ESP_LOGE(TAG, "Failed to commit configuration: %s", esp_err_to_name(ret));
        }
    } else {
        ESP_LOGE(TAG, "Failed to save configuration: %s", esp_err_to_name(ret));
    }
    
    nvs_close(nvs_handle);
    return ret;
}

/**
 * @brief Get current configuration
 */
esp_err_t config_manager_get_config(device_config_t *config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        ESP_LOGE(TAG, "Configuration not loaded");
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(config, &g_current_config, sizeof(device_config_t));
    return ESP_OK;
}

/**
 * @brief Set configuration
 */
esp_err_t config_manager_set_config(const device_config_t *config)
{
    if (config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    memcpy(&g_current_config, config, sizeof(device_config_t));
    g_current_config.version = CONFIG_VERSION;
    g_config_loaded = true;
    
    ESP_LOGI(TAG, "Configuration updated");
    return ESP_OK;
}

/**
 * @brief Reset configuration to defaults
 */
esp_err_t config_manager_reset_to_defaults(void)
{
    memcpy(&g_current_config, &g_default_config, sizeof(device_config_t));
    g_config_loaded = true;
    
    esp_err_t ret = config_manager_save();
    if (ret == ESP_OK) {
        ESP_LOGI(TAG, "Configuration reset to defaults");
    }
    
    return ret;
}

/**
 * @brief Get LED configuration
 */
esp_err_t config_manager_get_led_config(led_config_t *led_config)
{
    if (led_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(led_config, &g_current_config.led_config, sizeof(led_config_t));
    return ESP_OK;
}

/**
 * @brief Set LED configuration
 */
esp_err_t config_manager_set_led_config(const led_config_t *led_config)
{
    if (led_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(&g_current_config.led_config, led_config, sizeof(led_config_t));
    ESP_LOGI(TAG, "LED configuration updated");
    return ESP_OK;
}

/**
 * @brief Get sound configuration
 */
esp_err_t config_manager_get_sound_config(sound_config_t *sound_config)
{
    if (sound_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(sound_config, &g_current_config.sound_config, sizeof(sound_config_t));
    return ESP_OK;
}

/**
 * @brief Set sound configuration
 */
esp_err_t config_manager_set_sound_config(const sound_config_t *sound_config)
{
    if (sound_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(&g_current_config.sound_config, sound_config, sizeof(sound_config_t));
    ESP_LOGI(TAG, "Sound configuration updated");
    return ESP_OK;
}

/**
 * @brief Get button configuration
 */
esp_err_t config_manager_get_button_config(button_config_t *button_config)
{
    if (button_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(button_config, &g_current_config.button_config, sizeof(button_config_t));
    return ESP_OK;
}

/**
 * @brief Set button configuration
 */
esp_err_t config_manager_set_button_config(const button_config_t *button_config)
{
    if (button_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(&g_current_config.button_config, button_config, sizeof(button_config_t));
    ESP_LOGI(TAG, "Button configuration updated");
    return ESP_OK;
}

/**
 * @brief Get BLE configuration
 */
esp_err_t config_manager_get_ble_config(ble_config_t *ble_config)
{
    if (ble_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(ble_config, &g_current_config.ble_config, sizeof(ble_config_t));
    return ESP_OK;
}

/**
 * @brief Set BLE configuration
 */
esp_err_t config_manager_set_ble_config(const ble_config_t *ble_config)
{
    if (ble_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(&g_current_config.ble_config, ble_config, sizeof(ble_config_t));
    ESP_LOGI(TAG, "BLE configuration updated");
    return ESP_OK;
}

/**
 * @brief Get system configuration
 */
esp_err_t config_manager_get_system_config(system_config_t *system_config)
{
    if (system_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(system_config, &g_current_config.system_config, sizeof(system_config_t));
    return ESP_OK;
}

/**
 * @brief Set system configuration
 */
esp_err_t config_manager_set_system_config(const system_config_t *system_config)
{
    if (system_config == NULL) {
        return ESP_ERR_INVALID_ARG;
    }
    
    if (!g_config_loaded) {
        return ESP_ERR_INVALID_STATE;
    }
    
    memcpy(&g_current_config.system_config, system_config, sizeof(system_config_t));
    ESP_LOGI(TAG, "System configuration updated");
    return ESP_OK;
}

/**
 * @brief Check if configuration is loaded
 */
bool config_manager_is_loaded(void)
{
    return g_config_loaded;
}
